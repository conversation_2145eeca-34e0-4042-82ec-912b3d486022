<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.authine.cloudpivot</groupId>
    <artifactId>cloudpivot-extensions</artifactId>
    <version>${rversion}</version>
    <name>cloudpivot-extensions</name>
    <description>云枢应用扩展包脚手架工程)</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.15</version>
    </parent>

    <properties>
        <java.version>1.8</java.version>

        <!-- CloudPivot云枢平台 版本号 -->
        <!-- 请将此属性值修改为当前使用的云枢版本号，如6.13.14/6.14.18 -->
        <cloudpivot.version>8.2.26</cloudpivot.version>

        <!-- 自定义插件应用的版本号 -->
        <rversion>0.0.1-SNAPSHOT</rversion>

        <timestamp>${maven.build.timestamp}</timestamp>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>

    </properties>


    <modules>
        <module>cloudpivot-extension-debug</module>
        <module>cloudpivot-extension-template</module>
        <module>cloudpivot-extension-my</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <artifactId>cloudpivot-extension-template</artifactId>
                <groupId>app.modules</groupId>
                <version>${rversion}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.1.0</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


    <repositories>
        <!-- Authine CloudPivot Repository 声明 -->
        <repository>
            <id>nexus</id>
            <name>Authine nexus</name>
            <url>https://nexus01.authine.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <!-- 其他repository 声明 -->
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>


    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <name>Authine nexus</name>
            <url>https://nexus01.authine.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
