<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <springProperty scope="context" name="endpoint" source="cloudpivot.bizlog.endpoint" defaultValue=""/>
    <springProperty scope="context" name="accessKeyId" source="cloudpivot.bizlog.accessKeyId" defaultValue=""/>
    <springProperty scope="context" name="accessKey" source="cloudpivot.bizlog.accessKeySecret" defaultValue=""/>
    <springProperty scope="context" name="projectName" source="cloudpivot.bizlog.project" defaultValue=""/>
    <springProperty scope="context" name="logStore" source="cloudpivot.bizlog.logStore" defaultValue=""/>

    <appender name="logHubAppender" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>${endpoint}</endpoint>
        <accessKeyId>${accessKeyId}</accessKeyId>
        <accessKey>${accessKey}</accessKey>

        <!-- sls 项目配置 -->
        <projectName>${projectName}</projectName>
        <logstore>${logStore}</logstore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <topic>web-api-log</topic>
        <source>web-api-log</source>

        <!--
            packageTimeoutInMS = 3000;     指定被缓存日志的发送超时时间，如果缓存超时，则会被立即发送,单位毫秒
            logsCountPerPackage = 4096;    指定每个缓存的日志包中包含日志数量的最大值,取值为1~4096
            logsBytesPerPackage = 3145728; 指定每个缓存的日志包的大小上限,取值为1~5242880，单位为字节
            memPoolSizeInByte = 104857600; 指定单个Producer实例可以使用的内存的上限,单位字节
            retryTimes = 3;                指定发送失败时重试的次数
            maxIOThreadSizeInPool = 8;     指定I/O线程池最大线程数量，主要用于发送数据到日志服务
        -->
        <!-- 可选项 详见 '参数说明'-->
        <packageTimeoutInMS>3000</packageTimeoutInMS>
        <logsCountPerPackage>4096</logsCountPerPackage>
        <logsBytesPerPackage>3145728</logsBytesPerPackage>
        <memPoolSizeInByte>104857600</memPoolSizeInByte>
        <retryTimes>3</retryTimes>
        <maxIOThreadSizeInPool>8</maxIOThreadSizeInPool>

        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder>
            <pattern>%d %-5level [%thread] %logger{0}: %msg</pattern>
        </encoder>

        <!-- 可选项 设置时间格式 -->
        <timeFormat>yyyy-MM-dd'T'HH:mmZ</timeFormat>
        <!-- 可选项 设置时区 -->
        <timeZone>Asia/Shanghai</timeZone>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder
            by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger - %msg</pattern>
        </encoder>
    </appender>

    <!-- 可用来获取StatusManager中的状态 -->
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/>

    <root>
        <level value="INFO"/>
        <appender-ref ref="logHubAppender"/>
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
