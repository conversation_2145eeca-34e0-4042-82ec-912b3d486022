html {
	height: 100%;
}
.page-login {
	height: 100%;
	min-height: 600px;
	padding-top: 0;
	overflow: hidden;
}
.page-login:before {
	background-image: url("../images/login-bg.jpg");
}
.solo-gan{
	font-size: 30px;
}
.page-login.page-dark.layout-full:after {
	background-color: rgba(38, 50, 56, .6);
}
.page-login .page-brand-info {
	height: 100%;
}
.page-login .page-brand-info .page-brand {
	padding: 0 60px 60px;
}
.page-login .page-brand-info .page-brand .brand-img {
	vertical-align: middle;
}
.page-login .page-brand-info .page-brand h3 {
	max-width: 650px;
	color: #fff;
}
.page-login .page-brand-info .page-brand .list-icons {
	padding-left: 0;
	margin: 30px 0;
}
.page-login-main {
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
	min-height: 600px;
	padding: 150px 60px 180px;
	padding: 0 60px;
	color: #76838f;
	background: #fff;
}
.page-login-main > .alert {
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	text-align: center;
	border-radius: 0;
}
.page-login-main .vertical-align {
	height: 100%;
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
	padding-bottom: 65px;
}
.page-login-main > .alert {
	padding-right: 10px;
}
.page-login-main > .alert + .vertical-align {
	padding-top: 65px;
}
.page-login .login-form {
	width: 350px;
	margin: 45px 0 20px;
}
.page-login .login-form .form-control {
	height: 42px;
}
input.page-login .login-form .form-control {
	padding: 0 12px;
}
.page-login .login-form.fv-form-bootstrap .form-control-feedback {
	margin: 6px;
	background-color: transparent;
}
.page-login .login-form .input-group + .form-control-feedback {
	display: none !important;
}
.page-login .login-form > button {
	padding: 8px 0;
}
.page-login .page-copyright {
	position: absolute;
	right: 0;
	bottom: 15px;
	left: 0;
	text-align: center;
}
.help-block {
    margin-top: 7px!important;
}

/* 阿里云拖动验证 */
.container {
	background: #fff;
	padding: 20px;
	margin: 20px;
	width: 400px;
}
.nc-container #nc_1_wrapper, .nc-container.tb-login #nc_1_wrapper,.errloading{
	width: 100%!important;
}
.nc-container .scale_text.scale_text span[data-nc-lang="_startTEXT"]{
	display: inline!important;
}
.nc_scale{
	height: 40px!important;
}
.nc-container .nc_scale span{
	width: 42px!important;
	height: 40px!important;
	line-height: 40px!important;
}
.nc-container .errloading{
	padding: 9px 5px!important;
}
.ln {
	padding: 5px 0;
}
.ln .h {
	display: inline-block;
	width: 4em;
}
.ln input {
	border: solid 1px #999;
	padding: 5px 8px;
}

@media (max-height: 600px) {
	.page-login .page-copyright {
		display: none;
	}
}
@media (min-width: 992px) {
	.page-login .page-content {
		padding-right: 500px;
	}
}
@media (max-width: 767px) {
	.page-login {
		overflow: auto;
		background: #fff;
		background-image: none;
	}
	.page-login .login-form {
		width: auto;
	}
}
@media (max-width: 480px) {
	.page-login-main {
		width: 100%;
	}
	.page-login .login-form {
		width: auto;
	}
}