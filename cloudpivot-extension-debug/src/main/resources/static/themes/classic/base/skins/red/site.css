/**
 * Admui-iframe v1.0.0 (http://www.admui.com/)
 * Copyright 2015-2017 Admui Team
 * Licensed under the Admui License 1.0 (http://www.admui.com/about/#license)
 */
* {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
}
*:before, *:after {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
}
html {
	font-size: 10px;

	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
	font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, "Microsoft Yahei", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif;
	font-size: 14px;
	line-height: 1.6;
	color: #76838f;
	background-color: #fff;
}
input, button, select, textarea {
	font-family: inherit;
	font-size: inherit;
	line-height: inherit;
}
a {
	color: #f96868;
	text-decoration: none;
}
a:hover, a:focus {
	color: #fa7a7a;
	text-decoration: underline;
}
a:focus {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline: none;
	outline-offset: -2px;
}
figure {
	margin: 0;
}
img {
	vertical-align: middle;
}
.img-responsive {
	display: block;
	max-width: 100%;
	height: auto;
}
.img-rounded {
	border-radius: 4px;
}
.img-thumbnail {
	display: inline-block;
	max-width: 100%;
	height: auto;
	padding: 4px;
	line-height: 1.6;
	background-color: #fff;
	border: 1px solid #e4eaec;
	border-radius: 3px;
	-webkit-transition: all .2s ease-in-out;
	     -o-transition: all .2s ease-in-out;
	        transition: all .2s ease-in-out;
}
.img-circle {
	border-radius: 50%;
}
hr {
	margin-top: 22px;
	margin-bottom: 22px;
	border: 0;
	border-top: 1px solid #e4eaec;
}
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}
.sr-only-focusable:active, .sr-only-focusable:focus {
	position: static;
	width: auto;
	height: auto;
	margin: 0;
	overflow: visible;
	clip: auto;
}
[role="button"] {
	cursor: pointer;
}
input, button, select, textarea {
	font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, "Microsoft Yahei", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif;
}
a.text-action {
	color: #a3afb7;
}
a.text-action, a.text-action:hover, a.text-action:focus {
	text-decoration: none;
}
a.text-action:hover, a.text-action:focus {
	color: #ccd5db;
}
a.text-action .icon + span {
	margin-left: 3px;
}
a.text-like {
	color: #a3afb7 !important;
}
a.text-like, a.text-like:hover, a.text-like:focus {
	text-decoration: none;
}
a.text-like.active, a.text-like:hover, a.text-like:focus {
	color: #f96868 !important;
}
.text-action + .text-action {
	margin-left: 6px;
}
.img-bordered {
	padding: 3px;
	border: 1px solid #e4eaec;
}
.img-bordered-primary {
	border-color: #f96868 !important;
}
.img-bordered-purple {
	border-color: #7c51d1 !important;
}
.img-bordered-red {
	border-color: #e9595b !important;
}
.img-bordered-green {
	border-color: #7dd3ae !important;
}
.img-bordered-orange {
	border-color: #ec9940 !important;
}
html {
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
input, textarea, keygen, select, button {
	outline: none;

	text-rendering: optimizeLegibility !important;
	-webkit-font-smoothing: antialiased !important;
	-moz-osx-font-smoothing: grayscale !important;
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
	font-weight: 500;
}
h1 .icon:first-child, h2 .icon:first-child, h3 .icon:first-child, h4 .icon:first-child, h5 .icon:first-child, h6 .icon:first-child, .h1 .icon:first-child, .h2 .icon:first-child, .h3 .icon:first-child, .h4 .icon:first-child, .h5 .icon:first-child, .h6 .icon:first-child {
	margin-right: .5em;
}
mark, .mark {
	padding: 1px 3px;
	color: #fff;
	border-radius: 2px;
}
.drop-cap {
	float: left;
	padding: 5px;
	margin-right: 5px;
	font-family: Georgia;
	font-size: 60px;
	line-height: 50px;
	color: #263238;
}
.drop-cap-reversed {
	color: #fff;
	background-color: #263238;
}
.list-icons {
	padding-left: 10px;
	margin-left: 0;
	list-style: none;
}
.list-icons > li {
	margin-top: 6px;
}
.list-icons > li:first-child {
	margin-top: 0;
}
.list-icons > li i {
	float: left;
	width: 1em;
	margin: 0 6px 0 0;
}
.text-primary {
	color: #f96868;
}
a.text-primary:hover, a.text-primary:focus {
	color: #f73737;
}
.text-success {
	color: #46be8a;
}
a.text-success:hover, a.text-success:focus {
	color: #369b6f;
}
.text-info {
	color: #57c7d4;
}
a.text-info:hover, a.text-info:focus {
	color: #33b6c5;
}
.text-warning {
	color: #f2a654;
}
a.text-warning:hover, a.text-warning:focus {
	color: #ee8d25;
}
.text-danger {
	color: #f96868;
}
a.text-danger:hover, a.text-danger:focus {
	color: #f73737;
}
blockquote {
	font-size: 14px;
	color: #526069;
	border-left-width: 4px;
}
blockquote p {
	margin-bottom: 0;
}
blockquote footer, blockquote small, blockquote .small {
	font-size: 12px;
}
.blockquote-reverse {
	border-right-width: 4px;
}
.blockquote {
	padding: 10px 15px;
	border-left-width: 4px;
	border-radius: 3px;
}
.blockquote.blockquote-reverse {
	border-right-width: 4px;
}
.blockquote-success {
	background-color: rgba(70, 190, 138, .1);
	border-color: #46be8a;
}
.blockquote-info {
	background-color: rgba(87, 199, 212, .1);
	border-color: #57c7d4;
}
.blockquote-warning {
	background-color: rgba(242, 166, 84, .1);
	border-color: #f2a654;
}
.blockquote-danger {
	background-color: rgba(249, 104, 104, .1);
	border-color: #f96868;
}
code {
	padding: 1px 2px;
	border: 1px solid #fab4b4;
}
pre code {
	border: none;
}
.container {
	max-width: 100%;
}
@media (min-width: 1600px) {
	.container {
		width: 1304px;
	}
}
@media (min-width: 1600px) {
	.col-xlg-1, .col-xlg-2, .col-xlg-3, .col-xlg-4, .col-xlg-5, .col-xlg-6, .col-xlg-7, .col-xlg-8, .col-xlg-9, .col-xlg-10, .col-xlg-11, .col-xlg-12 {
		float: left;
	}
	.col-xlg-12 {
		width: 100%;
	}
	.col-xlg-11 {
		width: 91.66666667%;
	}
	.col-xlg-10 {
		width: 83.33333333%;
	}
	.col-xlg-9 {
		width: 75%;
	}
	.col-xlg-8 {
		width: 66.66666667%;
	}
	.col-xlg-7 {
		width: 58.33333333%;
	}
	.col-xlg-6 {
		width: 50%;
	}
	.col-xlg-5 {
		width: 41.66666667%;
	}
	.col-xlg-4 {
		width: 33.33333333%;
	}
	.col-xlg-3 {
		width: 25%;
	}
	.col-xlg-2 {
		width: 16.66666667%;
	}
	.col-xlg-1 {
		width: 8.33333333%;
	}
	.col-xlg-pull-12 {
		right: 100%;
	}
	.col-xlg-pull-11 {
		right: 91.66666667%;
	}
	.col-xlg-pull-10 {
		right: 83.33333333%;
	}
	.col-xlg-pull-9 {
		right: 75%;
	}
	.col-xlg-pull-8 {
		right: 66.66666667%;
	}
	.col-xlg-pull-7 {
		right: 58.33333333%;
	}
	.col-xlg-pull-6 {
		right: 50%;
	}
	.col-xlg-pull-5 {
		right: 41.66666667%;
	}
	.col-xlg-pull-4 {
		right: 33.33333333%;
	}
	.col-xlg-pull-3 {
		right: 25%;
	}
	.col-xlg-pull-2 {
		right: 16.66666667%;
	}
	.col-xlg-pull-1 {
		right: 8.33333333%;
	}
	.col-xlg-pull-0 {
		right: auto;
	}
	.col-xlg-push-12 {
		left: 100%;
	}
	.col-xlg-push-11 {
		left: 91.66666667%;
	}
	.col-xlg-push-10 {
		left: 83.33333333%;
	}
	.col-xlg-push-9 {
		left: 75%;
	}
	.col-xlg-push-8 {
		left: 66.66666667%;
	}
	.col-xlg-push-7 {
		left: 58.33333333%;
	}
	.col-xlg-push-6 {
		left: 50%;
	}
	.col-xlg-push-5 {
		left: 41.66666667%;
	}
	.col-xlg-push-4 {
		left: 33.33333333%;
	}
	.col-xlg-push-3 {
		left: 25%;
	}
	.col-xlg-push-2 {
		left: 16.66666667%;
	}
	.col-xlg-push-1 {
		left: 8.33333333%;
	}
	.col-xlg-push-0 {
		left: auto;
	}
	.col-xlg-offset-12 {
		margin-left: 100%;
	}
	.col-xlg-offset-11 {
		margin-left: 91.66666667%;
	}
	.col-xlg-offset-10 {
		margin-left: 83.33333333%;
	}
	.col-xlg-offset-9 {
		margin-left: 75%;
	}
	.col-xlg-offset-8 {
		margin-left: 66.66666667%;
	}
	.col-xlg-offset-7 {
		margin-left: 58.33333333%;
	}
	.col-xlg-offset-6 {
		margin-left: 50%;
	}
	.col-xlg-offset-5 {
		margin-left: 41.66666667%;
	}
	.col-xlg-offset-4 {
		margin-left: 33.33333333%;
	}
	.col-xlg-offset-3 {
		margin-left: 25%;
	}
	.col-xlg-offset-2 {
		margin-left: 16.66666667%;
	}
	.col-xlg-offset-1 {
		margin-left: 8.33333333%;
	}
	.col-xlg-offset-0 {
		margin-left: 0;
	}
}
.col-xlg-1, .col-xlg-2, .col-xlg-3, .col-xlg-4, .col-xlg-5, .col-xlg-6, .col-xlg-7, .col-xlg-8, .col-xlg-9, .col-xlg-10, .col-xlg-11, .col-xlg-12 {
	position: relative;
	min-height: 1px;
	padding-right: 12px;
	padding-left: 12px;
}
.row.no-space {
	margin-right: 0;
	margin-left: 0;
}
.row.no-space > [class*="col-"] {
	padding-right: 0;
	padding-left: 0;
}
.row-lg {
	margin-right: -24px;
	margin-left: -24px;
}
.row-lg > .col-xs-1, .row-lg > .col-sm-1, .row-lg > .col-md-1, .row-lg > .col-lg-1, .row-lg > .col-xlg-1, .row-lg > .col-xs-2, .row-lg > .col-sm-2, .row-lg > .col-md-2, .row-lg > .col-lg-2, .row-lg > .col-xlg-2, .row-lg > .col-xs-3, .row-lg > .col-sm-3, .row-lg > .col-md-3, .row-lg > .col-lg-3, .row-lg > .col-xlg-3, .row-lg > .col-xs-4, .row-lg > .col-sm-4, .row-lg > .col-md-4, .row-lg > .col-lg-4, .row-lg > .col-xlg-4, .row-lg > .col-xs-5, .row-lg > .col-sm-5, .row-lg > .col-md-5, .row-lg > .col-lg-5, .row-lg > .col-xlg-5, .row-lg > .col-xs-6, .row-lg > .col-sm-6, .row-lg > .col-md-6, .row-lg > .col-lg-6, .row-lg > .col-xlg-6, .row-lg > .col-xs-7, .row-lg > .col-sm-7, .row-lg > .col-md-7, .row-lg > .col-lg-7, .row-lg > .col-xlg-7, .row-lg > .col-xs-8, .row-lg > .col-sm-8, .row-lg > .col-md-8, .row-lg > .col-lg-8, .row-lg > .col-xlg-8, .row-lg > .col-xs-9, .row-lg > .col-sm-9, .row-lg > .col-md-9, .row-lg > .col-lg-9, .row-lg > .col-xlg-9, .row-lg > .col-xs-10, .row-lg > .col-sm-10, .row-lg > .col-md-10, .row-lg > .col-lg-10, .row-lg > .col-xlg-10, .row-lg > .col-xs-11, .row-lg > .col-sm-11, .row-lg > .col-md-11, .row-lg > .col-lg-11, .row-lg > .col-xlg-11, .row-lg > .col-xs-12, .row-lg > .col-sm-12, .row-lg > .col-md-12, .row-lg > .col-lg-12, .row-lg > .col-xlg-12 {
	padding-right: 24px;
	padding-left: 24px;
}
.table {
	color: #76838f;
}
.table > thead > tr > th, .table > tfoot > tr > th {
	font-weight: 500;
	color: #526069;
	vertical-align: middle;
	background: rgba(243, 247, 249, .5);
}
.table > thead > tr > th small, .table > tfoot > tr > th small {
	font-weight: 300;
}
.table > thead > tr > th {
	border-bottom: 1px solid #e4eaec;
}
.table > tbody + tbody {
	border-top: 1px solid #e4eaec;
}
.table th > .checkbox-custom:only-child, .table td > .checkbox-custom:only-child {
	margin-top: 0;
	margin-bottom: 0;
	text-align: center;
}
.table .success, .table .warning, .table .danger, .table .info {
	color: #fff;
}
.table .success a, .table .warning a, .table .danger a, .table .info a {
	color: #fff;
}
.table .disabled {
	color: #ccd5db;
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
}
.table .disabled a {
	color: #ccd5db;
}
.table .cell-30 {
	width: 30px;
}
.table .cell-40 {
	width: 40px;
}
.table .cell-50 {
	width: 50px;
}
.table .cell-60 {
	width: 60px;
}
.table .cell-80 {
	width: 80px;
}
.table .cell-100 {
	width: 100px;
}
.table .cell-120 {
	width: 120px;
}
.table .cell-130 {
	width: 130px;
}
.table .cell-150 {
	width: 150px;
}
.table .cell-180 {
	width: 180px;
}
.table .cell-200 {
	width: 200px;
}
.table .cell-250 {
	width: 250px;
}
.table .cell-300 {
	width: 300px;
}
.table-primary thead tr, .table-success thead tr, .table-info thead tr, .table-warning thead tr, .table-danger thead tr, .table-dark thead tr {
	color: #fff;
}
.table-default thead tr {
	background: #f3f7f9;
}
.table-primary thead tr {
	background: #f96868;
}
.table-success thead tr {
	background: #46be8a;
}
.table-info thead tr {
	background: #57c7d4;
}
.table-warning thead tr {
	background: #f2a654;
}
.table-danger thead tr {
	background: #f96868;
}
.table-dark thead tr {
	background: #526069;
}
.table-gray thead tr {
	color: #526069;
	background: #ccd5db;
}
.table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
	border-bottom-width: 1px;
}
.table-bordered > thead:first-child > tr:first-child > th {
	border: 1px solid #e4eaec;
}
.table-section + tbody {
	display: none;
}
.table-section-arrow {
	position: relative;
	display: inline-block;
	font-family: "Web Icons";
	font-style: normal;
	font-weight: normal;
	text-align: center;
	-webkit-transition: -webkit-transform .15s;
	     -o-transition:      -o-transform .15s;
	        transition: -webkit-transform .15s;
	        transition:         transform .15s;
	        transition:         transform .15s, -webkit-transform .15s, -o-transform .15s;
	-webkit-transform: translate(0, 0);
	    -ms-transform: translate(0, 0);
	     -o-transform: translate(0, 0);
	        transform: translate(0, 0);

	text-rendering: auto;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.table-section-arrow:before {
	content: "\f181";
}
.table-section.active tr {
	background-color: #f3f7f9;
}
.table-section.active + tbody {
	display: table-row-group;
}
.table-section.active .table-section-arrow {
	-webkit-transform: rotate(-180deg);
	    -ms-transform: rotate(-180deg);
	     -o-transform: rotate(-180deg);
	        transform: rotate(-180deg);
}
.form-control {
	-webkit-appearance: none;
	-webkit-box-shadow: none;
	        box-shadow: none;
	-webkit-transition: border .25s linear, color .25s linear, background-color .25s linear, -webkit-box-shadow .25s linear;
	     -o-transition: box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
	        transition: border .25s linear, color .25s linear, background-color .25s linear, -webkit-box-shadow .25s linear;
	        transition: box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
	        transition: box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear, -webkit-box-shadow .25s linear;

	   -moz-appearance: none;
}
select.form-control {
	padding-right: 30px;
	background: #fff url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAFCAYAAABB9hwOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA4RpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpiNWZkMzNlMC0zNTcxLTI4NDgtYjA3NC01ZTRhN2RjMWVmNjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RTUxRUI3MDdEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RTUxRUI3MDZEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6ZWNiNjQzMjYtNDc1Yi01OTQxLWIxYjItNDVkZjU5YjZlODA2IiBzdFJlZjpkb2N1bWVudElEPSJhZG9iZTpkb2NpZDpwaG90b3Nob3A6N2RlYzI2YWMtZGI5OC0xMWU1LWIwMjgtY2ZhNDhhOGNjNWY1Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+AXTIGgAAAFRJREFUeNpidI1KSWFgYDBlwASngXjOrqWzGcgBTEC8DIjfo4m/h4qTDUAGfwPi+UD8Hyr2H8r/RqnBIHATiPdC2XuhfIoACxJ7PRDzQmmKAUCAAQDxOxHyb4DjOAAAAABJRU5ErkJggg==") no-repeat center right;
}
select[multiple].form-control {
	padding-right: 12px;
	background: #fff;
}
.has-success .help-block, .has-success .control-label, .has-success .radio, .has-success .checkbox, .has-success .radio-inline, .has-success .checkbox-inline, .has-success.radio label, .has-success.checkbox label, .has-success.radio-inline label, .has-success.checkbox-inline label {
	color: #46be8a;
}
.has-success .form-control {
	border-color: #46be8a;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-success .form-control:focus {
	border-color: #369b6f;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #91d9ba;
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #91d9ba;
}
.has-success .input-group-addon {
	color: #46be8a;
	background-color: #fff;
	border-color: #46be8a;
}
.has-success .form-control-feedback {
	color: #46be8a;
}
.has-success .form-control {
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-success .form-control:focus {
	border-color: #46be8a;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(70, 190, 138, .6);
	        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(70, 190, 138, .6);
}
.has-success .form-control.focus, .has-success .form-control:focus {
	border-color: #46be8a;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.has-warning .help-block, .has-warning .control-label, .has-warning .radio, .has-warning .checkbox, .has-warning .radio-inline, .has-warning .checkbox-inline, .has-warning.radio label, .has-warning.checkbox label, .has-warning.radio-inline label, .has-warning.checkbox-inline label {
	color: #f2a654;
}
.has-warning .form-control {
	border-color: #f2a654;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-warning .form-control:focus {
	border-color: #ee8d25;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #f9d7b3;
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #f9d7b3;
}
.has-warning .input-group-addon {
	color: #f2a654;
	background-color: #fff;
	border-color: #f2a654;
}
.has-warning .form-control-feedback {
	color: #f2a654;
}
.has-warning .form-control {
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-warning .form-control:focus {
	border-color: #f2a654;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(242, 166, 84, .6);
	        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(242, 166, 84, .6);
}
.has-warning .form-control.focus, .has-warning .form-control:focus {
	border-color: #f2a654;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
	color: #f96868;
}
.has-error .form-control {
	border-color: #f96868;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-error .form-control:focus {
	border-color: #f73737;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdcaca;
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdcaca;
}
.has-error .input-group-addon {
	color: #f96868;
	background-color: #fff;
	border-color: #f96868;
}
.has-error .form-control-feedback {
	color: #f96868;
}
.has-error .form-control {
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
	        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-error .form-control:focus {
	border-color: #f96868;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(249, 104, 104, .6);
	        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(249, 104, 104, .6);
}
.has-error .form-control.focus, .has-error .form-control:focus {
	border-color: #f96868;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.form-group:last-child {
	margin-bottom: 0;
}
.form-group.has-feedback.no-label .form-control-feedback {
	top: 0;
}
.form-group.has-feedback.left-feedback .form-control-feedback {
	right: auto;
	left: 0;
}
.form-group.has-feedback.left-feedback .form-control {
	padding-right: 13px;
	padding-left: 50px;
}
.form-group .required {
	color: #d6494b;
}
.form-control.square {
	border-radius: 0;
}
.form-control.round {
	border-radius: 200px;
}
textarea.form-control.no-resize {
	resize: none;
}
.input-group-file input[type="text"] {
	background-color: #fff;
}
.input-group-file .btn-file {
	position: relative;
	overflow: hidden;
}
.input-group-file .btn-file.btn-outline {
	border: 1px solid #e4eaec;
	border-left: none;
}
.input-group-file .btn-file.btn-outline:hover {
	border-left: none;
}
.input-group-file .btn-file > .icon {
	margin: 0 3px;
}
.input-group-file .btn-file input[type="file"] {
	position: absolute;
	top: 0;
	left: 0;
	display: block;
	min-width: 100%;
	min-height: 100%;
	text-align: left;
	cursor: pointer;
	opacity: 0;
}
.help-block {
	margin-top: 7px;
	margin-bottom: 8px;
}
.help-block > .icon {
	margin: 0 5px;
}
label {
	font-weight: 400;
}
.input-search-close {
	color: #000;
	text-shadow: none;
	filter: alpha(opacity=20);
	opacity: .2;
}
.input-search-close.icon {
	font-size: inherit;
	line-height: inherit;
}
.input-search-close:hover, .input-search-close:focus {
	color: #000;
	text-decoration: none;
	cursor: pointer;
	filter: alpha(opacity=50);
	outline: none;
	opacity: .5;
}
button.input-search-close {
	-webkit-appearance: none;
	padding: 0;
	cursor: pointer;
	background: transparent;
	border: 0;
}
.input-search {
	position: relative;
}
.input-search .form-control {
	border-radius: 200px;
}
.input-search .input-search-icon, .input-search .input-search-close {
	position: absolute;
	top: 50%;
	z-index: 1;
	width: 32px;
	-webkit-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	     -o-transform: translateY(-50%);
	        transform: translateY(-50%);
}
.input-search .input-search-close {
	right: 8px;
}
.input-search .input-search-icon + .form-control {
	padding-left: 38px;
}
.input-search .input-search-icon {
	left: 8px;
	font-size: 16px;
	color: #a3afb7;
	text-align: center;
	pointer-events: none;
}
.input-search-btn + .form-control {
	padding-right: 50px;
}
.input-search-btn {
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
	padding: 0 10px;
	background: transparent;
	border: none;
	border-radius: 0 200px 200px 0;
}
.input-search-btn > .icon {
	margin: 0 3px;
}
.input-search-dark .input-search-icon {
	color: #76838f;
}
.input-search-dark .form-control {
	background: #f3f7f9;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.input-search-dark .form-control:focus {
	background-color: transparent;
}
.form-inline .form-group {
	margin: 0 20px 10px 0;
}
.form-inline .form-group:last-child {
	margin: 0 0 10px 0;
}
.form-inline .control-label {
	margin-right: 5px;
}
@media (max-width: 767px) {
	.form-inline .form-group {
		margin-right: 0;
	}
	.form-inline .form-group:last-child {
		margin: 0;
	}
}
/*@btn-floating-xs-padding:10px;*/
/*@btn-floating-sm-padding:3px;*/
/*@btn-floating-lg-padding:15px;*/
.btn {
	padding: 4px 12px;
	font-size: 14px;
	line-height: 1.6;
	border-radius: 3px;
	-webkit-transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
	     -o-transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
	        transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
}
.btn:focus, .btn:active:focus, .btn.active:focus {
	outline: 0;
}
.btn:active, .btn.active {
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.btn .icon {
	width: 1em;
	margin: 0 3px;
	line-height: inherit;
	text-align: center;
}
.btn-block {
	white-space: normal;
}
.btn-outline.btn-default {
	color: #76838f;
	background-color: transparent;
}
.btn-outline.btn-default:hover, .btn-outline.btn-default:focus, .btn-outline.btn-default:active, .btn-outline.btn-default.active, .open > .dropdown-toggle.btn-outline.btn-default {
	color: #76838f;
	background-color: rgba(118, 131, 143, .1);
	border-color: #e4eaec;
}
.btn-outline.btn-default:hover .badge, .btn-outline.btn-default:focus .badge, .btn-outline.btn-default:active .badge, .btn-outline.btn-default.active .badge, .open > .dropdown-toggle.btn-outline.btn-default .badge {
	color: #76838f;
	background-color: #76838f;
}
.btn-outline.btn-primary {
	color: #f96868;
	background-color: transparent;
}
.btn-outline.btn-primary:hover, .btn-outline.btn-primary:focus, .btn-outline.btn-primary:active, .btn-outline.btn-primary.active, .open > .dropdown-toggle.btn-outline.btn-primary {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.btn-outline.btn-primary:hover .badge, .btn-outline.btn-primary:focus .badge, .btn-outline.btn-primary:active .badge, .btn-outline.btn-primary.active .badge, .open > .dropdown-toggle.btn-outline.btn-primary .badge {
	color: #f96868;
	background-color: #fff;
}
.btn-outline.btn-success {
	color: #46be8a;
	background-color: transparent;
}
.btn-outline.btn-success:hover, .btn-outline.btn-success:focus, .btn-outline.btn-success:active, .btn-outline.btn-success.active, .open > .dropdown-toggle.btn-outline.btn-success {
	color: #fff;
	background-color: #46be8a;
	border-color: #46be8a;
}
.btn-outline.btn-success:hover .badge, .btn-outline.btn-success:focus .badge, .btn-outline.btn-success:active .badge, .btn-outline.btn-success.active .badge, .open > .dropdown-toggle.btn-outline.btn-success .badge {
	color: #46be8a;
	background-color: #fff;
}
.btn-outline.btn-info {
	color: #57c7d4;
	background-color: transparent;
}
.btn-outline.btn-info:hover, .btn-outline.btn-info:focus, .btn-outline.btn-info:active, .btn-outline.btn-info.active, .open > .dropdown-toggle.btn-outline.btn-info {
	color: #fff;
	background-color: #57c7d4;
	border-color: #57c7d4;
}
.btn-outline.btn-info:hover .badge, .btn-outline.btn-info:focus .badge, .btn-outline.btn-info:active .badge, .btn-outline.btn-info.active .badge, .open > .dropdown-toggle.btn-outline.btn-info .badge {
	color: #57c7d4;
	background-color: #fff;
}
.btn-outline.btn-warning {
	color: #f2a654;
	background-color: transparent;
}
.btn-outline.btn-warning:hover, .btn-outline.btn-warning:focus, .btn-outline.btn-warning:active, .btn-outline.btn-warning.active, .open > .dropdown-toggle.btn-outline.btn-warning {
	color: #fff;
	background-color: #f2a654;
	border-color: #f2a654;
}
.btn-outline.btn-warning:hover .badge, .btn-outline.btn-warning:focus .badge, .btn-outline.btn-warning:active .badge, .btn-outline.btn-warning.active .badge, .open > .dropdown-toggle.btn-outline.btn-warning .badge {
	color: #f2a654;
	background-color: #fff;
}
.btn-outline.btn-danger {
	color: #f96868;
	background-color: transparent;
}
.btn-outline.btn-danger:hover, .btn-outline.btn-danger:focus, .btn-outline.btn-danger:active, .btn-outline.btn-danger.active, .open > .dropdown-toggle.btn-outline.btn-danger {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.btn-outline.btn-danger:hover .badge, .btn-outline.btn-danger:focus .badge, .btn-outline.btn-danger:active .badge, .btn-outline.btn-danger.active .badge, .open > .dropdown-toggle.btn-outline.btn-danger .badge {
	color: #f96868;
	background-color: #fff;
}
.btn-outline.btn-dark {
	color: #526069;
	background-color: transparent;
}
.btn-outline.btn-dark:hover, .btn-outline.btn-dark:focus, .btn-outline.btn-dark:active, .btn-outline.btn-dark.active, .open > .dropdown-toggle.btn-outline.btn-dark {
	color: #fff;
	background-color: #526069;
	border-color: #526069;
}
.btn-outline.btn-dark:hover .badge, .btn-outline.btn-dark:focus .badge, .btn-outline.btn-dark:active .badge, .btn-outline.btn-dark.active .badge, .open > .dropdown-toggle.btn-outline.btn-dark .badge {
	color: #526069;
	background-color: #fff;
}
.btn-outline.btn-inverse {
	color: #fff;
	background-color: transparent;
}
.btn-outline.btn-inverse:hover, .btn-outline.btn-inverse:focus, .btn-outline.btn-inverse:active, .btn-outline.btn-inverse.active, .open > .dropdown-toggle.btn-outline.btn-inverse {
	color: #76838f;
	background-color: #fff;
	border-color: #fff;
}
.btn-outline.btn-inverse:hover .badge, .btn-outline.btn-inverse:focus .badge, .btn-outline.btn-inverse:active .badge, .btn-outline.btn-inverse.active .badge, .open > .dropdown-toggle.btn-outline.btn-inverse .badge {
	color: #fff;
	background-color: #76838f;
}
.btn-lg {
	padding: 6px 18px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 4px;
}
.btn-sm {
	padding: 3px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 2px;
}
.btn-xs {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 2px;
}
.btn-squared {
	border-radius: 0;
}
.btn-round {
	border-radius: 1000px;
}
.btn-default:hover, .btn-default:focus, .btn-default.focus {
	background-color: #f3f7f9;
	border-color: #f3f7f9;
}
.btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default {
	background-color: #ccd5db;
	border-color: #ccd5db;
}
.btn-default:active:hover, .btn-default.active:hover, .open > .dropdown-toggle.btn-default:hover, .btn-default:active:focus, .btn-default.active:focus, .open > .dropdown-toggle.btn-default:focus, .btn-default:active.focus, .btn-default.active.focus, .open > .dropdown-toggle.btn-default.focus {
	background-color: #ccd5db;
	border-color: #ccd5db;
}
.btn-default.disabled, .btn-default[disabled], fieldset[disabled] .btn-default, .btn-default.disabled:hover, .btn-default[disabled]:hover, fieldset[disabled] .btn-default:hover, .btn-default.disabled:focus, .btn-default[disabled]:focus, fieldset[disabled] .btn-default:focus, .btn-default.disabled.focus, .btn-default[disabled].focus, fieldset[disabled] .btn-default.focus, .btn-default.disabled:active, .btn-default[disabled]:active, fieldset[disabled] .btn-default:active, .btn-default.disabled.active, .btn-default[disabled].active, fieldset[disabled] .btn-default.active {
	color: #76838f;
	background-color: #f3f7f9;
	border-color: #f3f7f9;
}
.btn-default.btn-up:before {
	border-bottom-color: #e4eaec;
}
.btn-default.btn-up:hover:before, .btn-default.btn-up:focus:before {
	border-bottom-color: #f3f7f9;
}
.btn-default.btn-up:active:before, .btn-default.btn-up.active:before, .open > .dropdown-toggle.btn-default.btn-up:before {
	border-bottom-color: #ccd5db;
}
.btn-default.btn-right:before {
	border-left-color: #e4eaec;
}
.btn-default.btn-right:hover:before, .btn-default.btn-right:focus:before {
	border-left-color: #f3f7f9;
}
.btn-default.btn-right:active:before, .btn-default.btn-right.active:before, .open > .dropdown-toggle.btn-default.btn-right:before {
	border-left-color: #ccd5db;
}
.btn-default.btn-bottom:before {
	border-top-color: #e4eaec;
}
.btn-default.btn-bottom:hover:before, .btn-default.btn-bottom:focus:before {
	border-top-color: #f3f7f9;
}
.btn-default.btn-bottom:active:before, .btn-default.btn-bottom.active:before, .open > .dropdown-toggle.btn-default.btn-bottom:before {
	border-top-color: #ccd5db;
}
.btn-default.btn-left:before {
	border-right-color: #e4eaec;
}
.btn-default.btn-left:hover:before, .btn-default.btn-left:focus:before {
	border-right-color: #f3f7f9;
}
.btn-default.btn-left:active:before, .btn-default.btn-left.active:before, .open > .dropdown-toggle.btn-default.btn-left:before {
	border-right-color: #ccd5db;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary.focus {
	background-color: #fa7a7a;
	border-color: #fa7a7a;
}
.btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
	background-color: #e9595b;
	border-color: #e9595b;
}
.btn-primary:active:hover, .btn-primary.active:hover, .open > .dropdown-toggle.btn-primary:hover, .btn-primary:active:focus, .btn-primary.active:focus, .open > .dropdown-toggle.btn-primary:focus, .btn-primary:active.focus, .btn-primary.active.focus, .open > .dropdown-toggle.btn-primary.focus {
	background-color: #e9595b;
	border-color: #e9595b;
}
.btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled.focus, .btn-primary[disabled].focus, fieldset[disabled] .btn-primary.focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
	color: #fff;
	background-color: #fa9898;
	border-color: #fa9898;
}
.btn-primary.btn-up:before {
	border-bottom-color: #f96868;
}
.btn-primary.btn-up:hover:before, .btn-primary.btn-up:focus:before {
	border-bottom-color: #fa7a7a;
}
.btn-primary.btn-up:active:before, .btn-primary.btn-up.active:before, .open > .dropdown-toggle.btn-primary.btn-up:before {
	border-bottom-color: #e9595b;
}
.btn-primary.btn-right:before {
	border-left-color: #f96868;
}
.btn-primary.btn-right:hover:before, .btn-primary.btn-right:focus:before {
	border-left-color: #fa7a7a;
}
.btn-primary.btn-right:active:before, .btn-primary.btn-right.active:before, .open > .dropdown-toggle.btn-primary.btn-right:before {
	border-left-color: #e9595b;
}
.btn-primary.btn-bottom:before {
	border-top-color: #f96868;
}
.btn-primary.btn-bottom:hover:before, .btn-primary.btn-bottom:focus:before {
	border-top-color: #fa7a7a;
}
.btn-primary.btn-bottom:active:before, .btn-primary.btn-bottom.active:before, .open > .dropdown-toggle.btn-primary.btn-bottom:before {
	border-top-color: #e9595b;
}
.btn-primary.btn-left:before {
	border-right-color: #f96868;
}
.btn-primary.btn-left:hover:before, .btn-primary.btn-left:focus:before {
	border-right-color: #fa7a7a;
}
.btn-primary.btn-left:active:before, .btn-primary.btn-left.active:before, .open > .dropdown-toggle.btn-primary.btn-left:before {
	border-right-color: #e9595b;
}
.btn-success:hover, .btn-success:focus, .btn-success.focus {
	background-color: #5cd29d;
	border-color: #5cd29d;
}
.btn-success:active, .btn-success.active, .open > .dropdown-toggle.btn-success {
	background-color: #36ab7a;
	border-color: #36ab7a;
}
.btn-success:active:hover, .btn-success.active:hover, .open > .dropdown-toggle.btn-success:hover, .btn-success:active:focus, .btn-success.active:focus, .open > .dropdown-toggle.btn-success:focus, .btn-success:active.focus, .btn-success.active.focus, .open > .dropdown-toggle.btn-success.focus {
	background-color: #36ab7a;
	border-color: #36ab7a;
}
.btn-success.disabled, .btn-success[disabled], fieldset[disabled] .btn-success, .btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled.focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success.focus, .btn-success.disabled:active, .btn-success[disabled]:active, fieldset[disabled] .btn-success:active, .btn-success.disabled.active, .btn-success[disabled].active, fieldset[disabled] .btn-success.active {
	color: #fff;
	background-color: #7dd3ae;
	border-color: #7dd3ae;
}
.btn-success.btn-up:before {
	border-bottom-color: #46be8a;
}
.btn-success.btn-up:hover:before, .btn-success.btn-up:focus:before {
	border-bottom-color: #5cd29d;
}
.btn-success.btn-up:active:before, .btn-success.btn-up.active:before, .open > .dropdown-toggle.btn-success.btn-up:before {
	border-bottom-color: #36ab7a;
}
.btn-success.btn-right:before {
	border-left-color: #46be8a;
}
.btn-success.btn-right:hover:before, .btn-success.btn-right:focus:before {
	border-left-color: #5cd29d;
}
.btn-success.btn-right:active:before, .btn-success.btn-right.active:before, .open > .dropdown-toggle.btn-success.btn-right:before {
	border-left-color: #36ab7a;
}
.btn-success.btn-bottom:before {
	border-top-color: #46be8a;
}
.btn-success.btn-bottom:hover:before, .btn-success.btn-bottom:focus:before {
	border-top-color: #5cd29d;
}
.btn-success.btn-bottom:active:before, .btn-success.btn-bottom.active:before, .open > .dropdown-toggle.btn-success.btn-bottom:before {
	border-top-color: #36ab7a;
}
.btn-success.btn-left:before {
	border-right-color: #46be8a;
}
.btn-success.btn-left:hover:before, .btn-success.btn-left:focus:before {
	border-right-color: #5cd29d;
}
.btn-success.btn-left:active:before, .btn-success.btn-left.active:before, .open > .dropdown-toggle.btn-success.btn-left:before {
	border-right-color: #36ab7a;
}
.btn-info:hover, .btn-info:focus, .btn-info.focus {
	background-color: #77d6e1;
	border-color: #77d6e1;
}
.btn-info:active, .btn-info.active, .open > .dropdown-toggle.btn-info {
	background-color: #47b8c6;
	border-color: #47b8c6;
}
.btn-info:active:hover, .btn-info.active:hover, .open > .dropdown-toggle.btn-info:hover, .btn-info:active:focus, .btn-info.active:focus, .open > .dropdown-toggle.btn-info:focus, .btn-info:active.focus, .btn-info.active.focus, .open > .dropdown-toggle.btn-info.focus {
	background-color: #47b8c6;
	border-color: #47b8c6;
}
.btn-info.disabled, .btn-info[disabled], fieldset[disabled] .btn-info, .btn-info.disabled:hover, .btn-info[disabled]:hover, fieldset[disabled] .btn-info:hover, .btn-info.disabled:focus, .btn-info[disabled]:focus, fieldset[disabled] .btn-info:focus, .btn-info.disabled.focus, .btn-info[disabled].focus, fieldset[disabled] .btn-info.focus, .btn-info.disabled:active, .btn-info[disabled]:active, fieldset[disabled] .btn-info:active, .btn-info.disabled.active, .btn-info[disabled].active, fieldset[disabled] .btn-info.active {
	color: #fff;
	background-color: #9ae1e9;
	border-color: #9ae1e9;
}
.btn-info.btn-up:before {
	border-bottom-color: #57c7d4;
}
.btn-info.btn-up:hover:before, .btn-info.btn-up:focus:before {
	border-bottom-color: #77d6e1;
}
.btn-info.btn-up:active:before, .btn-info.btn-up.active:before, .open > .dropdown-toggle.btn-info.btn-up:before {
	border-bottom-color: #47b8c6;
}
.btn-info.btn-right:before {
	border-left-color: #57c7d4;
}
.btn-info.btn-right:hover:before, .btn-info.btn-right:focus:before {
	border-left-color: #77d6e1;
}
.btn-info.btn-right:active:before, .btn-info.btn-right.active:before, .open > .dropdown-toggle.btn-info.btn-right:before {
	border-left-color: #47b8c6;
}
.btn-info.btn-bottom:before {
	border-top-color: #57c7d4;
}
.btn-info.btn-bottom:hover:before, .btn-info.btn-bottom:focus:before {
	border-top-color: #77d6e1;
}
.btn-info.btn-bottom:active:before, .btn-info.btn-bottom.active:before, .open > .dropdown-toggle.btn-info.btn-bottom:before {
	border-top-color: #47b8c6;
}
.btn-info.btn-left:before {
	border-right-color: #57c7d4;
}
.btn-info.btn-left:hover:before, .btn-info.btn-left:focus:before {
	border-right-color: #77d6e1;
}
.btn-info.btn-left:active:before, .btn-info.btn-left.active:before, .open > .dropdown-toggle.btn-info.btn-left:before {
	border-right-color: #47b8c6;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning.focus {
	background-color: #f4b066;
	border-color: #f4b066;
}
.btn-warning:active, .btn-warning.active, .open > .dropdown-toggle.btn-warning {
	background-color: #ec9940;
	border-color: #ec9940;
}
.btn-warning:active:hover, .btn-warning.active:hover, .open > .dropdown-toggle.btn-warning:hover, .btn-warning:active:focus, .btn-warning.active:focus, .open > .dropdown-toggle.btn-warning:focus, .btn-warning:active.focus, .btn-warning.active.focus, .open > .dropdown-toggle.btn-warning.focus {
	background-color: #ec9940;
	border-color: #ec9940;
}
.btn-warning.disabled, .btn-warning[disabled], fieldset[disabled] .btn-warning, .btn-warning.disabled:hover, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning:hover, .btn-warning.disabled:focus, .btn-warning[disabled]:focus, fieldset[disabled] .btn-warning:focus, .btn-warning.disabled.focus, .btn-warning[disabled].focus, fieldset[disabled] .btn-warning.focus, .btn-warning.disabled:active, .btn-warning[disabled]:active, fieldset[disabled] .btn-warning:active, .btn-warning.disabled.active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning.active {
	color: #fff;
	background-color: #f6be80;
	border-color: #f6be80;
}
.btn-warning.btn-up:before {
	border-bottom-color: #f2a654;
}
.btn-warning.btn-up:hover:before, .btn-warning.btn-up:focus:before {
	border-bottom-color: #f4b066;
}
.btn-warning.btn-up:active:before, .btn-warning.btn-up.active:before, .open > .dropdown-toggle.btn-warning.btn-up:before {
	border-bottom-color: #ec9940;
}
.btn-warning.btn-right:before {
	border-left-color: #f2a654;
}
.btn-warning.btn-right:hover:before, .btn-warning.btn-right:focus:before {
	border-left-color: #f4b066;
}
.btn-warning.btn-right:active:before, .btn-warning.btn-right.active:before, .open > .dropdown-toggle.btn-warning.btn-right:before {
	border-left-color: #ec9940;
}
.btn-warning.btn-bottom:before {
	border-top-color: #f2a654;
}
.btn-warning.btn-bottom:hover:before, .btn-warning.btn-bottom:focus:before {
	border-top-color: #f4b066;
}
.btn-warning.btn-bottom:active:before, .btn-warning.btn-bottom.active:before, .open > .dropdown-toggle.btn-warning.btn-bottom:before {
	border-top-color: #ec9940;
}
.btn-warning.btn-left:before {
	border-right-color: #f2a654;
}
.btn-warning.btn-left:hover:before, .btn-warning.btn-left:focus:before {
	border-right-color: #f4b066;
}
.btn-warning.btn-left:active:before, .btn-warning.btn-left.active:before, .open > .dropdown-toggle.btn-warning.btn-left:before {
	border-right-color: #ec9940;
}
.btn-danger:hover, .btn-danger:focus, .btn-danger.focus {
	background-color: #fa7a7a;
	border-color: #fa7a7a;
}
.btn-danger:active, .btn-danger.active, .open > .dropdown-toggle.btn-danger {
	background-color: #e9595b;
	border-color: #e9595b;
}
.btn-danger:active:hover, .btn-danger.active:hover, .open > .dropdown-toggle.btn-danger:hover, .btn-danger:active:focus, .btn-danger.active:focus, .open > .dropdown-toggle.btn-danger:focus, .btn-danger:active.focus, .btn-danger.active.focus, .open > .dropdown-toggle.btn-danger.focus {
	background-color: #e9595b;
	border-color: #e9595b;
}
.btn-danger.disabled, .btn-danger[disabled], fieldset[disabled] .btn-danger, .btn-danger.disabled:hover, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger:hover, .btn-danger.disabled:focus, .btn-danger[disabled]:focus, fieldset[disabled] .btn-danger:focus, .btn-danger.disabled.focus, .btn-danger[disabled].focus, fieldset[disabled] .btn-danger.focus, .btn-danger.disabled:active, .btn-danger[disabled]:active, fieldset[disabled] .btn-danger:active, .btn-danger.disabled.active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger.active {
	color: #fff;
	background-color: #fa9898;
	border-color: #fa9898;
}
.btn-danger.btn-up:before {
	border-bottom-color: #f96868;
}
.btn-danger.btn-up:hover:before, .btn-danger.btn-up:focus:before {
	border-bottom-color: #fa7a7a;
}
.btn-danger.btn-up:active:before, .btn-danger.btn-up.active:before, .open > .dropdown-toggle.btn-danger.btn-up:before {
	border-bottom-color: #e9595b;
}
.btn-danger.btn-right:before {
	border-left-color: #f96868;
}
.btn-danger.btn-right:hover:before, .btn-danger.btn-right:focus:before {
	border-left-color: #fa7a7a;
}
.btn-danger.btn-right:active:before, .btn-danger.btn-right.active:before, .open > .dropdown-toggle.btn-danger.btn-right:before {
	border-left-color: #e9595b;
}
.btn-danger.btn-bottom:before {
	border-top-color: #f96868;
}
.btn-danger.btn-bottom:hover:before, .btn-danger.btn-bottom:focus:before {
	border-top-color: #fa7a7a;
}
.btn-danger.btn-bottom:active:before, .btn-danger.btn-bottom.active:before, .open > .dropdown-toggle.btn-danger.btn-bottom:before {
	border-top-color: #e9595b;
}
.btn-danger.btn-left:before {
	border-right-color: #f96868;
}
.btn-danger.btn-left:hover:before, .btn-danger.btn-left:focus:before {
	border-right-color: #fa7a7a;
}
.btn-danger.btn-left:active:before, .btn-danger.btn-left.active:before, .open > .dropdown-toggle.btn-danger.btn-left:before {
	border-right-color: #e9595b;
}
.btn-inverse {
	color: #76838f;
	background-color: #fff;
	border-color: #e4eaec;
}
.btn-inverse:focus, .btn-inverse.focus {
	color: #76838f;
	background-color: #e6e6e6;
	border-color: #99b0b7;
}
.btn-inverse:hover {
	color: #76838f;
	background-color: #e6e6e6;
	border-color: #c0ced3;
}
.btn-inverse:active, .btn-inverse.active, .open > .dropdown-toggle.btn-inverse {
	color: #76838f;
	background-color: #e6e6e6;
	border-color: #c0ced3;
}
.btn-inverse:active:hover, .btn-inverse.active:hover, .open > .dropdown-toggle.btn-inverse:hover, .btn-inverse:active:focus, .btn-inverse.active:focus, .open > .dropdown-toggle.btn-inverse:focus, .btn-inverse:active.focus, .btn-inverse.active.focus, .open > .dropdown-toggle.btn-inverse.focus {
	color: #76838f;
	background-color: #d4d4d4;
	border-color: #99b0b7;
}
.btn-inverse:active, .btn-inverse.active, .open > .dropdown-toggle.btn-inverse {
	background-image: none;
}
.btn-inverse.disabled:hover, .btn-inverse[disabled]:hover, fieldset[disabled] .btn-inverse:hover, .btn-inverse.disabled:focus, .btn-inverse[disabled]:focus, fieldset[disabled] .btn-inverse:focus, .btn-inverse.disabled.focus, .btn-inverse[disabled].focus, fieldset[disabled] .btn-inverse.focus {
	background-color: #fff;
	border-color: #e4eaec;
}
.btn-inverse .badge {
	color: #fff;
	background-color: #76838f;
}
.btn-inverse:hover, .btn-inverse:focus, .btn-inverse.focus {
	background-color: #fff;
	border-color: #f3f7f9;
}
.btn-inverse:active, .btn-inverse.active, .open > .dropdown-toggle.btn-inverse {
	background-color: #fff;
	border-color: #ccd5db;
}
.btn-inverse:active:hover, .btn-inverse.active:hover, .open > .dropdown-toggle.btn-inverse:hover, .btn-inverse:active:focus, .btn-inverse.active:focus, .open > .dropdown-toggle.btn-inverse:focus, .btn-inverse:active.focus, .btn-inverse.active.focus, .open > .dropdown-toggle.btn-inverse.focus {
	background-color: #fff;
	border-color: #ccd5db;
}
.btn-inverse.disabled, .btn-inverse[disabled], fieldset[disabled] .btn-inverse, .btn-inverse.disabled:hover, .btn-inverse[disabled]:hover, fieldset[disabled] .btn-inverse:hover, .btn-inverse.disabled:focus, .btn-inverse[disabled]:focus, fieldset[disabled] .btn-inverse:focus, .btn-inverse.disabled.focus, .btn-inverse[disabled].focus, fieldset[disabled] .btn-inverse.focus, .btn-inverse.disabled:active, .btn-inverse[disabled]:active, fieldset[disabled] .btn-inverse:active, .btn-inverse.disabled.active, .btn-inverse[disabled].active, fieldset[disabled] .btn-inverse.active {
	color: #ccd5db;
	background-color: #fff;
	border-color: #a3afb7;
}
.btn-inverse.btn-up:before {
	border-bottom-color: #fff;
}
.btn-inverse.btn-up:hover:before, .btn-inverse.btn-up:focus:before {
	border-bottom-color: #fff;
}
.btn-inverse.btn-up:active:before, .btn-inverse.btn-up.active:before, .open > .dropdown-toggle.btn-inverse.btn-up:before {
	border-bottom-color: #fff;
}
.btn-inverse.btn-right:before {
	border-left-color: #fff;
}
.btn-inverse.btn-right:hover:before, .btn-inverse.btn-right:focus:before {
	border-left-color: #fff;
}
.btn-inverse.btn-right:active:before, .btn-inverse.btn-right.active:before, .open > .dropdown-toggle.btn-inverse.btn-right:before {
	border-left-color: #fff;
}
.btn-inverse.btn-bottom:before {
	border-top-color: #fff;
}
.btn-inverse.btn-bottom:hover:before, .btn-inverse.btn-bottom:focus:before {
	border-top-color: #fff;
}
.btn-inverse.btn-bottom:active:before, .btn-inverse.btn-bottom.active:before, .open > .dropdown-toggle.btn-inverse.btn-bottom:before {
	border-top-color: #fff;
}
.btn-inverse.btn-left:before {
	border-right-color: #fff;
}
.btn-inverse.btn-left:hover:before, .btn-inverse.btn-left:focus:before {
	border-right-color: #fff;
}
.btn-inverse.btn-left:active:before, .btn-inverse.btn-left.active:before, .open > .dropdown-toggle.btn-inverse.btn-left:before {
	border-right-color: #fff;
}
.btn-dark {
	color: #fff;
	background-color: #526069;
	border-color: #526069;
}
.btn-dark:focus, .btn-dark.focus {
	color: #fff;
	background-color: #3c464c;
	border-color: #1a1f21;
}
.btn-dark:hover {
	color: #fff;
	background-color: #3c464c;
	border-color: #374147;
}
.btn-dark:active, .btn-dark.active, .open > .dropdown-toggle.btn-dark {
	color: #fff;
	background-color: #3c464c;
	border-color: #374147;
}
.btn-dark:active:hover, .btn-dark.active:hover, .open > .dropdown-toggle.btn-dark:hover, .btn-dark:active:focus, .btn-dark.active:focus, .open > .dropdown-toggle.btn-dark:focus, .btn-dark:active.focus, .btn-dark.active.focus, .open > .dropdown-toggle.btn-dark.focus {
	color: #fff;
	background-color: #2c3338;
	border-color: #1a1f21;
}
.btn-dark:active, .btn-dark.active, .open > .dropdown-toggle.btn-dark {
	background-image: none;
}
.btn-dark.disabled:hover, .btn-dark[disabled]:hover, fieldset[disabled] .btn-dark:hover, .btn-dark.disabled:focus, .btn-dark[disabled]:focus, fieldset[disabled] .btn-dark:focus, .btn-dark.disabled.focus, .btn-dark[disabled].focus, fieldset[disabled] .btn-dark.focus {
	background-color: #526069;
	border-color: #526069;
}
.btn-dark .badge {
	color: #526069;
	background-color: #fff;
}
.btn-dark:hover, .btn-dark:focus, .btn-dark.focus {
	background-color: #76838f;
	border-color: #76838f;
}
.btn-dark:active, .btn-dark.active, .open > .dropdown-toggle.btn-dark {
	background-color: #37474f;
	border-color: #37474f;
}
.btn-dark:active:hover, .btn-dark.active:hover, .open > .dropdown-toggle.btn-dark:hover, .btn-dark:active:focus, .btn-dark.active:focus, .open > .dropdown-toggle.btn-dark:focus, .btn-dark:active.focus, .btn-dark.active.focus, .open > .dropdown-toggle.btn-dark.focus {
	background-color: #37474f;
	border-color: #37474f;
}
.btn-dark.disabled, .btn-dark[disabled], fieldset[disabled] .btn-dark, .btn-dark.disabled:hover, .btn-dark[disabled]:hover, fieldset[disabled] .btn-dark:hover, .btn-dark.disabled:focus, .btn-dark[disabled]:focus, fieldset[disabled] .btn-dark:focus, .btn-dark.disabled.focus, .btn-dark[disabled].focus, fieldset[disabled] .btn-dark.focus, .btn-dark.disabled:active, .btn-dark[disabled]:active, fieldset[disabled] .btn-dark:active, .btn-dark.disabled.active, .btn-dark[disabled].active, fieldset[disabled] .btn-dark.active {
	color: #fff;
	background-color: #a3afb7;
	border-color: #a3afb7;
}
.btn-dark.btn-up:before {
	border-bottom-color: #526069;
}
.btn-dark.btn-up:hover:before, .btn-dark.btn-up:focus:before {
	border-bottom-color: #76838f;
}
.btn-dark.btn-up:active:before, .btn-dark.btn-up.active:before, .open > .dropdown-toggle.btn-dark.btn-up:before {
	border-bottom-color: #37474f;
}
.btn-dark.btn-right:before {
	border-left-color: #526069;
}
.btn-dark.btn-right:hover:before, .btn-dark.btn-right:focus:before {
	border-left-color: #76838f;
}
.btn-dark.btn-right:active:before, .btn-dark.btn-right.active:before, .open > .dropdown-toggle.btn-dark.btn-right:before {
	border-left-color: #37474f;
}
.btn-dark.btn-bottom:before {
	border-top-color: #526069;
}
.btn-dark.btn-bottom:hover:before, .btn-dark.btn-bottom:focus:before {
	border-top-color: #76838f;
}
.btn-dark.btn-bottom:active:before, .btn-dark.btn-bottom.active:before, .open > .dropdown-toggle.btn-dark.btn-bottom:before {
	border-top-color: #37474f;
}
.btn-dark.btn-left:before {
	border-right-color: #526069;
}
.btn-dark.btn-left:hover:before, .btn-dark.btn-left:focus:before {
	border-right-color: #76838f;
}
.btn-dark.btn-left:active:before, .btn-dark.btn-left.active:before, .open > .dropdown-toggle.btn-dark.btn-left:before {
	border-right-color: #37474f;
}
.btn-dark:hover, .btn-dark:focus {
	color: #fff;
}
.btn-dark:active, .btn-dark.active, .open > .dropdown-toggle.btn-dark {
	color: #fff;
}
.btn-dark.btn-flat {
	color: #526069;
}
.btn-flat {
	background: none;
	border: none;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.btn-flat.disabled {
	color: #a3afb7;
}
.btn-icon, .btn.icon {
	padding: 10px;
	line-height: 1em;
}
.btn-icon.btn-xs, .btn.icon.btn-xs {
	padding: 4px;
	font-size: 12px;
}
.btn-icon.btn-sm, .btn.icon.btn-sm {
	padding: 8px;
	font-size: 14px;
}
.btn-icon.btn-lg, .btn.icon.btn-lg {
	padding: 12px;
	font-size: 20px;
}
.btn-icon.disabled, .btn.icon.disabled {
	color: #a3afb7;
}
.btn-icon {
	padding: 8px;
}
.btn-icon .icon {
	margin: -13px 0 0;
}
.btn-icon.btn-lg {
	padding: 7.5px;
}
.btn-icon.btn-sm {
	padding: 5px;
}
.btn-icon.btn-xs {
	padding: 4px;
}
.btn-raised {
	-webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .18), 0 2px 4px rgba(0, 0, 0, .21);
	        box-shadow: 0 0 2px rgba(0, 0, 0, .18), 0 2px 4px rgba(0, 0, 0, .21);
	-webkit-transition: -webkit-box-shadow .25s cubic-bezier(.4, 0, .2, 1);
	     -o-transition:         box-shadow .25s cubic-bezier(.4, 0, .2, 1);
	        transition: -webkit-box-shadow .25s cubic-bezier(.4, 0, .2, 1);
	        transition:         box-shadow .25s cubic-bezier(.4, 0, .2, 1);
	        transition:         box-shadow .25s cubic-bezier(.4, 0, .2, 1), -webkit-box-shadow .25s cubic-bezier(.4, 0, .2, 1);
}
.btn-raised:hover, .btn-raised:active, .btn-raised.active, .open > .dropdown-toggle.btn-raised {
	-webkit-box-shadow: 0 0 3px rgba(0, 0, 0, .15), 0 3px 6px rgba(0, 0, 0, .2);
	        box-shadow: 0 0 3px rgba(0, 0, 0, .15), 0 3px 6px rgba(0, 0, 0, .2);
}
.btn-raised.disabled, .btn-raised[disabled], fieldset[disabled] .btn-raised {
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.btn-floating {
	width: 56px;
	height: 56px;
	padding: 0;
	margin: 0;
	font-size: 24px;
	text-align: center;
	border-radius: 100%;
	-webkit-box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
	        box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
}
.btn-floating.btn-xs {
	width: 30px;
	height: 30px;
	padding: 0;
	font-size: 13px;
}
.btn-floating.btn-sm {
	width: 40px;
	height: 40px;
	padding: 0;
	font-size: 15px;
}
.btn-floating.btn-lg {
	width: 70px;
	height: 70px;
	padding: 0;
	font-size: 30px;
}
.btn-floating i {
	position: relative;
	top: 0;
}
.btn-animate {
	position: relative;
	overflow: hidden;
}
.btn-animate span {
	display: block;
	width: 100%;
	height: 100%;
	-webkit-transform: translate(0px, 0);
	    -ms-transform: translate(0px, 0);
	     -o-transform: translate(0px, 0);
	        transform: translate(0px, 0);
}
.btn-animate-side {
	padding: 4px 28px;
}
.btn-animate-side span {
	-webkit-transition: -webkit-transform .2s ease-out 0s;
	     -o-transition:      -o-transform .2s ease-out 0s;
	        transition: -webkit-transform .2s ease-out 0s;
	        transition:         transform .2s ease-out 0s;
	        transition:         transform .2s ease-out 0s, -webkit-transform .2s ease-out 0s, -o-transform .2s ease-out 0s;
}
.btn-animate-side span > .icon {
	position: absolute;
	top: 50%;
	left: 0;
	display: block;
	margin-top: -1px;
	opacity: 0;
	-webkit-transition: opacity .2s ease-out 0s;
	     -o-transition: opacity .2s ease-out 0s;
	        transition: opacity .2s ease-out 0s;
	-webkit-transform: translate(-20px, -50%);
	    -ms-transform: translate(-20px, -50%);
	     -o-transform: translate(-20px, -50%);
	        transform: translate(-20px, -50%);
}
.btn-animate-side:hover span {
	-webkit-transform: translate(10px, 0);
	    -ms-transform: translate(10px, 0);
	     -o-transform: translate(10px, 0);
	        transform: translate(10px, 0);
}
.btn-animate-side:hover span > .icon {
	opacity: 1;
}
.btn-animate-side.btn-xs {
	padding: 1px 14px;
}
.btn-animate-side.btn-xs span > .icon {
	left: 5px;
}
.btn-animate-side.btn-xs:hover span {
	-webkit-transform: translate(8px, 0);
	    -ms-transform: translate(8px, 0);
	     -o-transform: translate(8px, 0);
	        transform: translate(8px, 0);
}
.btn-animate-side.btn-sm {
	padding: 3px 22px;
}
.btn-animate-side.btn-sm span > .icon {
	left: 3px;
}
.btn-animate-side.btn-sm:hover span {
	-webkit-transform: translate(8px, 0);
	    -ms-transform: translate(8px, 0);
	     -o-transform: translate(8px, 0);
	        transform: translate(8px, 0);
}
.btn-animate-side.btn-lg {
	padding: 6px 33px;
}
.btn-animate-side.btn-lg span > .icon {
	left: -6px;
}
.btn-animate-side.btn-lg:hover span {
	-webkit-transform: translate(14px, 0);
	    -ms-transform: translate(14px, 0);
	     -o-transform: translate(14px, 0);
	        transform: translate(14px, 0);
}
.btn-animate-vertical span {
	-webkit-transition: all .2s ease-out 0s;
	     -o-transition: all .2s ease-out 0s;
	        transition: all .2s ease-out 0s;
}
.btn-animate-vertical span > .icon {
	position: absolute;
	top: -3px;
	left: 50%;
	display: block;
	margin-top: 0;
	font-size: 24px;
	-webkit-transform: translate(-50%, -100%);
	    -ms-transform: translate(-50%, -100%);
	     -o-transform: translate(-50%, -100%);
	        transform: translate(-50%, -100%);
}
.btn-animate-vertical:hover span {
	-webkit-transform: translate(0, 150%);
	    -ms-transform: translate(0, 150%);
	     -o-transform: translate(0, 150%);
	        transform: translate(0, 150%);
}
.btn-animate-vertical.btn-xs span > .icon {
	top: -5px;
	font-size: 16px;
}
.btn-animate-vertical.btn-sm span > .icon {
	top: -3px;
	font-size: 20px;
}
.btn-animate-vertical.btn-lg span > .icon {
	top: -5px;
	font-size: 28px;
}
.btn-labeled {
	padding: 0;
	padding-right: 8px;
}
.btn-labeled .btn-label {
	padding: 4px 8px;
	margin-right: 5px;
}
.btn-labeled.btn-xs {
	padding-right: 5px;
}
.btn-labeled.btn-xs .btn-label {
	padding: 1px 4px;
	margin-right: 2px;
}
.btn-labeled.btn-sm {
	padding-right: 10px;
}
.btn-labeled.btn-sm .btn-label {
	padding: 3px 6px;
	margin-right: 7px;
}
.btn-labeled.btn-lg {
	padding-right: 14px;
}
.btn-labeled.btn-lg .btn-label {
	padding: 6px 14px;
	margin-right: 11px;
}
.btn-labeled.btn-block {
	text-align: left;
}
.btn-label {
	display: inline-block;
	background-color: rgba(0, 0, 0, .15);
	border-radius: 3px 0 0 3px;
}
.btn-pill-left {
	border-radius: 500px 0 0 500px;
}
.btn-pill-right {
	border-radius: 0 500px 500px 0;
}
.btn-direction {
	position: relative;
}
.btn-direction:before {
	position: absolute;
	line-height: 0;
	content: "";
	border: 6px solid transparent;
}
.btn-up:before {
	top: -12px;
	left: 50%;
	margin-left: -6px;
	border-bottom-color: #e4eaec;
}
.btn-right:before {
	top: 50%;
	right: -12px;
	margin-top: -6px;
	border-left-color: #e4eaec;
}
.btn-bottom:before {
	bottom: -12px;
	left: 50%;
	margin-left: -6px;
	border-top-color: #e4eaec;
}
.btn-left:before {
	top: 50%;
	left: -12px;
	margin-top: -6px;
	border-right-color: #e4eaec;
}
.btn-pure, .btn-pure:hover, .btn-pure:focus, .btn-pure:active, .btn-pure.active, .open > .dropdown-toggle.btn-pure, .btn-pure[disabled], fieldset[disabled] .btn-pure {
	background-color: transparent;
	border-color: transparent;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.btn-pure:hover, .btn-pure:hover:hover, .btn-pure:focus:hover, .btn-pure:active:hover, .btn-pure.active:hover, .open > .dropdown-toggle.btn-pure:hover, .btn-pure[disabled]:hover, fieldset[disabled] .btn-pure:hover, .btn-pure:focus, .btn-pure:hover:focus, .btn-pure:focus:focus, .btn-pure:active:focus, .btn-pure.active:focus, .open > .dropdown-toggle.btn-pure:focus, .btn-pure[disabled]:focus, fieldset[disabled] .btn-pure:focus, .btn-pure.focus, .btn-pure:hover.focus, .btn-pure:focus.focus, .btn-pure:active.focus, .btn-pure.active.focus, .open > .dropdown-toggle.btn-pure.focus, .btn-pure[disabled].focus, fieldset[disabled] .btn-pure.focus {
	background-color: transparent;
	border-color: transparent;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.btn-pure.btn-default {
	color: #a3afb7;
}
.btn-pure.btn-default:hover, .btn-pure.btn-default:focus, .btn-pure.btn-default:active, .btn-pure.btn-default.active, .open > .dropdown-toggle.btn-pure.btn-default {
	color: #526069;
}
.btn-pure.btn-default:hover:hover, .btn-pure.btn-default:focus:hover, .btn-pure.btn-default:active:hover, .btn-pure.btn-default.active:hover, .open > .dropdown-toggle.btn-pure.btn-default:hover, .btn-pure.btn-default:hover:focus, .btn-pure.btn-default:focus:focus, .btn-pure.btn-default:active:focus, .btn-pure.btn-default.active:focus, .open > .dropdown-toggle.btn-pure.btn-default:focus, .btn-pure.btn-default:hover.focus, .btn-pure.btn-default:focus.focus, .btn-pure.btn-default:active.focus, .btn-pure.btn-default.active.focus, .open > .dropdown-toggle.btn-pure.btn-default.focus {
	color: #526069;
}
.btn-pure.btn-default:hover .badge, .btn-pure.btn-default:focus .badge, .btn-pure.btn-default:active .badge, .btn-pure.btn-default.active .badge, .open > .dropdown-toggle.btn-pure.btn-default .badge {
	color: #526069;
}
.btn-pure.btn-primary {
	color: #f96868;
}
.btn-pure.btn-primary:hover, .btn-pure.btn-primary:focus, .btn-pure.btn-primary:active, .btn-pure.btn-primary.active, .open > .dropdown-toggle.btn-pure.btn-primary {
	color: #d6494b;
}
.btn-pure.btn-primary:hover:hover, .btn-pure.btn-primary:focus:hover, .btn-pure.btn-primary:active:hover, .btn-pure.btn-primary.active:hover, .open > .dropdown-toggle.btn-pure.btn-primary:hover, .btn-pure.btn-primary:hover:focus, .btn-pure.btn-primary:focus:focus, .btn-pure.btn-primary:active:focus, .btn-pure.btn-primary.active:focus, .open > .dropdown-toggle.btn-pure.btn-primary:focus, .btn-pure.btn-primary:hover.focus, .btn-pure.btn-primary:focus.focus, .btn-pure.btn-primary:active.focus, .btn-pure.btn-primary.active.focus, .open > .dropdown-toggle.btn-pure.btn-primary.focus {
	color: #d6494b;
}
.btn-pure.btn-primary:hover .badge, .btn-pure.btn-primary:focus .badge, .btn-pure.btn-primary:active .badge, .btn-pure.btn-primary.active .badge, .open > .dropdown-toggle.btn-pure.btn-primary .badge {
	color: #d6494b;
}
.btn-pure.btn-success {
	color: #46be8a;
}
.btn-pure.btn-success:hover, .btn-pure.btn-success:focus, .btn-pure.btn-success:active, .btn-pure.btn-success.active, .open > .dropdown-toggle.btn-pure.btn-success {
	color: #279566;
}
.btn-pure.btn-success:hover:hover, .btn-pure.btn-success:focus:hover, .btn-pure.btn-success:active:hover, .btn-pure.btn-success.active:hover, .open > .dropdown-toggle.btn-pure.btn-success:hover, .btn-pure.btn-success:hover:focus, .btn-pure.btn-success:focus:focus, .btn-pure.btn-success:active:focus, .btn-pure.btn-success.active:focus, .open > .dropdown-toggle.btn-pure.btn-success:focus, .btn-pure.btn-success:hover.focus, .btn-pure.btn-success:focus.focus, .btn-pure.btn-success:active.focus, .btn-pure.btn-success.active.focus, .open > .dropdown-toggle.btn-pure.btn-success.focus {
	color: #279566;
}
.btn-pure.btn-success:hover .badge, .btn-pure.btn-success:focus .badge, .btn-pure.btn-success:active .badge, .btn-pure.btn-success.active .badge, .open > .dropdown-toggle.btn-pure.btn-success .badge {
	color: #279566;
}
.btn-pure.btn-info {
	color: #57c7d4;
}
.btn-pure.btn-info:hover, .btn-pure.btn-info:focus, .btn-pure.btn-info:active, .btn-pure.btn-info.active, .open > .dropdown-toggle.btn-pure.btn-info {
	color: #37a9b7;
}
.btn-pure.btn-info:hover:hover, .btn-pure.btn-info:focus:hover, .btn-pure.btn-info:active:hover, .btn-pure.btn-info.active:hover, .open > .dropdown-toggle.btn-pure.btn-info:hover, .btn-pure.btn-info:hover:focus, .btn-pure.btn-info:focus:focus, .btn-pure.btn-info:active:focus, .btn-pure.btn-info.active:focus, .open > .dropdown-toggle.btn-pure.btn-info:focus, .btn-pure.btn-info:hover.focus, .btn-pure.btn-info:focus.focus, .btn-pure.btn-info:active.focus, .btn-pure.btn-info.active.focus, .open > .dropdown-toggle.btn-pure.btn-info.focus {
	color: #37a9b7;
}
.btn-pure.btn-info:hover .badge, .btn-pure.btn-info:focus .badge, .btn-pure.btn-info:active .badge, .btn-pure.btn-info.active .badge, .open > .dropdown-toggle.btn-pure.btn-info .badge {
	color: #37a9b7;
}
.btn-pure.btn-warning {
	color: #f2a654;
}
.btn-pure.btn-warning:hover, .btn-pure.btn-warning:focus, .btn-pure.btn-warning:active, .btn-pure.btn-warning.active, .open > .dropdown-toggle.btn-pure.btn-warning {
	color: #e98f2e;
}
.btn-pure.btn-warning:hover:hover, .btn-pure.btn-warning:focus:hover, .btn-pure.btn-warning:active:hover, .btn-pure.btn-warning.active:hover, .open > .dropdown-toggle.btn-pure.btn-warning:hover, .btn-pure.btn-warning:hover:focus, .btn-pure.btn-warning:focus:focus, .btn-pure.btn-warning:active:focus, .btn-pure.btn-warning.active:focus, .open > .dropdown-toggle.btn-pure.btn-warning:focus, .btn-pure.btn-warning:hover.focus, .btn-pure.btn-warning:focus.focus, .btn-pure.btn-warning:active.focus, .btn-pure.btn-warning.active.focus, .open > .dropdown-toggle.btn-pure.btn-warning.focus {
	color: #e98f2e;
}
.btn-pure.btn-warning:hover .badge, .btn-pure.btn-warning:focus .badge, .btn-pure.btn-warning:active .badge, .btn-pure.btn-warning.active .badge, .open > .dropdown-toggle.btn-pure.btn-warning .badge {
	color: #e98f2e;
}
.btn-pure.btn-danger {
	color: #f96868;
}
.btn-pure.btn-danger:hover, .btn-pure.btn-danger:focus, .btn-pure.btn-danger:active, .btn-pure.btn-danger.active, .open > .dropdown-toggle.btn-pure.btn-danger {
	color: #d6494b;
}
.btn-pure.btn-danger:hover:hover, .btn-pure.btn-danger:focus:hover, .btn-pure.btn-danger:active:hover, .btn-pure.btn-danger.active:hover, .open > .dropdown-toggle.btn-pure.btn-danger:hover, .btn-pure.btn-danger:hover:focus, .btn-pure.btn-danger:focus:focus, .btn-pure.btn-danger:active:focus, .btn-pure.btn-danger.active:focus, .open > .dropdown-toggle.btn-pure.btn-danger:focus, .btn-pure.btn-danger:hover.focus, .btn-pure.btn-danger:focus.focus, .btn-pure.btn-danger:active.focus, .btn-pure.btn-danger.active.focus, .open > .dropdown-toggle.btn-pure.btn-danger.focus {
	color: #d6494b;
}
.btn-pure.btn-danger:hover .badge, .btn-pure.btn-danger:focus .badge, .btn-pure.btn-danger:active .badge, .btn-pure.btn-danger.active .badge, .open > .dropdown-toggle.btn-pure.btn-danger .badge {
	color: #d6494b;
}
.btn-pure.btn-dark {
	color: #526069;
}
.btn-pure.btn-dark:hover, .btn-pure.btn-dark:focus, .btn-pure.btn-dark:active, .btn-pure.btn-dark.active, .open > .dropdown-toggle.btn-pure.btn-dark {
	color: #37474f;
}
.btn-pure.btn-dark:hover:hover, .btn-pure.btn-dark:focus:hover, .btn-pure.btn-dark:active:hover, .btn-pure.btn-dark.active:hover, .open > .dropdown-toggle.btn-pure.btn-dark:hover, .btn-pure.btn-dark:hover:focus, .btn-pure.btn-dark:focus:focus, .btn-pure.btn-dark:active:focus, .btn-pure.btn-dark.active:focus, .open > .dropdown-toggle.btn-pure.btn-dark:focus, .btn-pure.btn-dark:hover.focus, .btn-pure.btn-dark:focus.focus, .btn-pure.btn-dark:active.focus, .btn-pure.btn-dark.active.focus, .open > .dropdown-toggle.btn-pure.btn-dark.focus {
	color: #37474f;
}
.btn-pure.btn-dark:hover .badge, .btn-pure.btn-dark:focus .badge, .btn-pure.btn-dark:active .badge, .btn-pure.btn-dark.active .badge, .open > .dropdown-toggle.btn-pure.btn-dark .badge {
	color: #37474f;
}
.btn-pure.btn-inverse {
	color: #fff;
}
.btn-pure.btn-inverse:hover, .btn-pure.btn-inverse:focus, .btn-pure.btn-inverse:active, .btn-pure.btn-inverse.active, .open > .dropdown-toggle.btn-pure.btn-inverse {
	color: #fff;
}
.btn-pure.btn-inverse:hover:hover, .btn-pure.btn-inverse:focus:hover, .btn-pure.btn-inverse:active:hover, .btn-pure.btn-inverse.active:hover, .open > .dropdown-toggle.btn-pure.btn-inverse:hover, .btn-pure.btn-inverse:hover:focus, .btn-pure.btn-inverse:focus:focus, .btn-pure.btn-inverse:active:focus, .btn-pure.btn-inverse.active:focus, .open > .dropdown-toggle.btn-pure.btn-inverse:focus, .btn-pure.btn-inverse:hover.focus, .btn-pure.btn-inverse:focus.focus, .btn-pure.btn-inverse:active.focus, .btn-pure.btn-inverse.active.focus, .open > .dropdown-toggle.btn-pure.btn-inverse.focus {
	color: #fff;
}
.btn-pure.btn-inverse:hover .badge, .btn-pure.btn-inverse:focus .badge, .btn-pure.btn-inverse:active .badge, .btn-pure.btn-inverse.active .badge, .open > .dropdown-toggle.btn-pure.btn-inverse .badge {
	color: #fff;
}
.caret {
	border-top: 4px solid;
	-webkit-transition: .25s;
	     -o-transition: .25s;
	        transition: .25s;
	-webkit-transform: scale(1.001);
	    -ms-transform: scale(1.001);
	     -o-transform: scale(1.001);
	        transform: scale(1.001);
}
.btn-group .btn + .dropdown-toggle .caret {
	margin-left: 0;
}
.dropdown-toggle.btn .caret {
	margin-left: .3em;
}
.dropdown-toggle.btn.btn-xs .caret {
	margin-left: 0;
}
.btn-group > .btn + .dropdown-toggle {
	padding-right: .8em;
	padding-left: .8em;
}
.dropdown-menu {
	padding: 1px 0;
	margin-top: 3px;
	border-radius: 0;
	-webkit-box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
	        box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
	-webkit-transition: .25s;
	     -o-transition: .25s;
	        transition: .25s;
}
.dropdown-menu .divider {
	margin: 6px 0;
}
.dropdown-menu > li {
	padding: 0 3px;
	margin: 2px 0;
}
.dropdown-menu > li > a {
	padding: 6px 10px;
	-webkit-transition: background-color .25s;
	     -o-transition: background-color .25s;
	        transition: background-color .25s;
}
.dropdown-menu li .icon:first-child, .dropdown-menu li > a .icon:first-child {
	width: 1em;
	margin-right: .5em;
	text-align: center;
}
.dropdown-menu.bullet {
	margin-top: 12px;
}
.dropdown-menu.bullet:before, .dropdown-menu.bullet:after {
	position: absolute;
	left: 10px;
	display: inline-block;
	width: 0;
	height: 0;
	content: "";
	border: 7px solid transparent;
	border-top-width: 0;
}
.dropdown-menu.bullet:before {
	top: -7px;
	border-bottom-color: #e4eaec;
}
.dropdown-menu.bullet:after {
	top: -6px;
	border-bottom-color: #fff;
}
.dropdown-menu-right.bullet:before, .dropdown-menu-right.bullet:after {
	right: 10px;
	left: auto;
}
.dropdown-menu.animate {
	overflow: hidden;
}
.dropdown-menu.animate > li {
	-webkit-animation-name: slide-left;
	     -o-animation-name: slide-left;
	        animation-name: slide-left;
	-webkit-animation-duration: .5s;
	     -o-animation-duration: .5s;
	        animation-duration: .5s;

	-webkit-animation-fill-mode: both;
	     -o-animation-fill-mode: both;
	        animation-fill-mode: both;
}
.dropdown-menu.animate > li:nth-child(1) {
	-webkit-animation-delay: .02s;
	     -o-animation-delay: .02s;
	        animation-delay: .02s;
}
.dropdown-menu.animate > li:nth-child(2) {
	-webkit-animation-delay: .04s;
	     -o-animation-delay: .04s;
	        animation-delay: .04s;
}
.dropdown-menu.animate > li:nth-child(3) {
	-webkit-animation-delay: .06s;
	     -o-animation-delay: .06s;
	        animation-delay: .06s;
}
.dropdown-menu.animate > li:nth-child(4) {
	-webkit-animation-delay: .08s;
	     -o-animation-delay: .08s;
	        animation-delay: .08s;
}
.dropdown-menu.animate > li:nth-child(5) {
	-webkit-animation-delay: .1s;
	     -o-animation-delay: .1s;
	        animation-delay: .1s;
}
.dropdown-menu.animate > li:nth-child(6) {
	-webkit-animation-delay: .12s;
	     -o-animation-delay: .12s;
	        animation-delay: .12s;
}
.dropdown-menu.animate > li:nth-child(7) {
	-webkit-animation-delay: .14s;
	     -o-animation-delay: .14s;
	        animation-delay: .14s;
}
.dropdown-menu.animate > li:nth-child(8) {
	-webkit-animation-delay: .16s;
	     -o-animation-delay: .16s;
	        animation-delay: .16s;
}
.dropdown-menu.animate > li:nth-child(9) {
	-webkit-animation-delay: .18s;
	     -o-animation-delay: .18s;
	        animation-delay: .18s;
}
.dropdown-menu.animate > li:nth-child(10) {
	-webkit-animation-delay: .2s;
	     -o-animation-delay: .2s;
	        animation-delay: .2s;
}
.dropdown-menu.animate > li.divider {
	-webkit-animation-name: none;
	     -o-animation-name: none;
	        animation-name: none;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(1) {
	-webkit-animation-delay: .02s;
	     -o-animation-delay: .02s;
	        animation-delay: .02s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(2) {
	-webkit-animation-delay: .04s;
	     -o-animation-delay: .04s;
	        animation-delay: .04s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(3) {
	-webkit-animation-delay: .06s;
	     -o-animation-delay: .06s;
	        animation-delay: .06s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(4) {
	-webkit-animation-delay: .08s;
	     -o-animation-delay: .08s;
	        animation-delay: .08s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(5) {
	-webkit-animation-delay: .1s;
	     -o-animation-delay: .1s;
	        animation-delay: .1s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(6) {
	-webkit-animation-delay: .12s;
	     -o-animation-delay: .12s;
	        animation-delay: .12s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(7) {
	-webkit-animation-delay: .14s;
	     -o-animation-delay: .14s;
	        animation-delay: .14s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(8) {
	-webkit-animation-delay: .16s;
	     -o-animation-delay: .16s;
	        animation-delay: .16s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(9) {
	-webkit-animation-delay: .18s;
	     -o-animation-delay: .18s;
	        animation-delay: .18s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(10) {
	-webkit-animation-delay: .2s;
	     -o-animation-delay: .2s;
	        animation-delay: .2s;
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
	margin-bottom: 6px;
	-webkit-box-shadow: 0 -3px 12px rgba(0, 0, 0, .05);
	        box-shadow: 0 -3px 12px rgba(0, 0, 0, .05);
}
.dropup .dropdown-menu.bullet, .navbar-fixed-bottom .dropdown .dropdown-menu.bullet {
	margin-bottom: 12px;
}
.dropup .dropdown-menu.bullet:before, .navbar-fixed-bottom .dropdown .dropdown-menu.bullet:before, .dropup .dropdown-menu.bullet:after, .navbar-fixed-bottom .dropdown .dropdown-menu.bullet:after {
	top: auto;
	border-top-width: 7px;
	border-bottom-width: 0;
}
.dropup .dropdown-menu.bullet:before, .navbar-fixed-bottom .dropdown .dropdown-menu.bullet:before {
	bottom: -7px;
	border-top-color: #e4eaec;
}
.dropup .dropdown-menu.bullet:after, .navbar-fixed-bottom .dropdown .dropdown-menu.bullet:after {
	bottom: -6px;
	border-top-color: #fff;
}
.dropdown-menu > .dropdown-header {
	padding: 8px 20px 6px;
	font-size: 14px;
	font-weight: 700;
	cursor: default;
}
.dropdown-menu > .dropdown-submenu {
	position: relative;
}
.dropdown-menu > .dropdown-submenu > a {
	position: relative;
}
.dropdown-menu > .dropdown-submenu > a:after {
	position: absolute;
	right: 10px;
	display: inline-block;
	width: 0;
	height: 0;
	margin-top: 6px;
	vertical-align: middle;
	content: "";
	border-top: 4px solid transparent;
	border-bottom: 4px solid transparent;
	border-left: 4px dashed;
}
.dropdown-menu > .dropdown-submenu .dropdown-menu {
	left: 100%;
	margin: 0;
}
.dropdown-menu > .dropdown-submenu.dropdown-menu-left .dropdown-menu {
	left: -100%;
}
.dropdown-menu > .dropdown-submenu:hover .dropdown-menu {
	display: block;
}
.dropdown .dropdown-submenu .dropdown-menu {
	top: 0;
}
.dropup .dropdown-submenu .dropdown-menu {
	bottom: 0;
}
.dropdown-menu-media {
	width: 360px;
	padding-top: 0;
	padding-bottom: 0;
}
.dropdown-menu-media > li {
	padding: 0;
	margin: 0;
}
.dropdown-menu-media .dropdown-menu-header {
	position: relative;
	padding: 20px 20px;
	background-color: #fff;
	border-bottom: 1px solid #e4eaec;
}
.dropdown-menu-media .dropdown-menu-header > h3, .dropdown-menu-media .dropdown-menu-header > h4, .dropdown-menu-media .dropdown-menu-header > h5 {
	margin: 0;
}
.dropdown-menu-media .dropdown-menu-header .badge, .dropdown-menu-media .dropdown-menu-header .label {
	position: absolute;
	top: 50%;
	right: 20px;
	-webkit-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	     -o-transform: translateY(-50%);
	        transform: translateY(-50%);
}
.dropdown-menu-media .list-group {
	max-height: 270px;
	margin: 0;
	font-size: 12px;
	border-radius: 0;
}
.dropdown-menu-media .list-group-item {
	padding: 0 20px;
	border: none;
	border-radius: 0 !important;
}
.dropdown-menu-media .list-group-item .media {
	padding: 15px 0;
	border-top: 1px solid #e4eaec;
}
.dropdown-menu-media .list-group-item:first-child .media {
	border-top: none;
}
.dropdown-menu-media > .dropdown-menu-footer {
	background-color: #f3f7f9;
	border-top: 1px solid #e4eaec;
}
.dropdown-menu-media > .dropdown-menu-footer > a {
	padding: 15px 20px !important;
	color: #a3afb7 !important;
}
.dropdown-menu-media > .dropdown-menu-footer > a:hover {
	color: #fa7a7a !important;
	background-color: transparent !important;
}
.dropdown-menu-media > .dropdown-menu-footer > .dropdown-menu-footer-btn {
	position: absolute;
	right: 0;
}
.dropdown-menu-media > .dropdown-menu-footer > .dropdown-menu-footer-btn:hover {
	color: #fa7a7a !important;
	background-color: transparent !important;
}
.dropdown-menu-primary > .active > a, .dropdown-menu-primary > .active > a:hover, .dropdown-menu-primary > .active > a:focus {
	color: #fff;
	background-color: #f96868;
}
.dropdown-menu-success > .active > a, .dropdown-menu-success > .active > a:hover, .dropdown-menu-success > .active > a:focus {
	color: #fff;
	background-color: #46be8a;
}
.dropdown-menu-info > .active > a, .dropdown-menu-info > .active > a:hover, .dropdown-menu-info > .active > a:focus {
	color: #fff;
	background-color: #57c7d4;
}
.dropdown-menu-warning > .active > a, .dropdown-menu-warning > .active > a:hover, .dropdown-menu-warning > .active > a:focus {
	color: #fff;
	background-color: #f2a654;
}
.dropdown-menu-danger > .active > a, .dropdown-menu-danger > .active > a:hover, .dropdown-menu-danger > .active > a:focus {
	color: #fff;
	background-color: #f96868;
}
.dropdown-menu-dark > .active > a, .dropdown-menu-dark > .active > a:hover, .dropdown-menu-dark > .active > a:focus {
	color: #fff;
	background-color: #526069;
}
.btn-group.open .dropdown-toggle {
	-webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
	        box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
}
.btn-group:focus .dropdown-toggle {
	-webkit-transition: .25s;
	     -o-transition: .25s;
	        transition: .25s;
}
.input-group-addon {
	-webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
	     -o-transition: border .25s linear, color .25s linear, background-color .25s linear;
	        transition: border .25s linear, color .25s linear, background-color .25s linear;
}
.input-group-btn .btn > .icon {
	vertical-align: bottom;
}
.input-group-btn .dropdown-toggle.btn .caret {
	margin-left: 2px;
}
.input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group {
	z-index: 1;
}
.nav > li > a {
	overflow: hidden;
}
.nav > li > a:focus {
	outline: none;
}
.nav > li > a .close {
	display: inline-block;
	margin-left: 10px;
	line-height: .6;
}
.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
	border-color: transparent;
}
.nav-quick {
	padding: 0;
	margin-right: 0;
	margin-bottom: 22px;
	margin-left: 0;
	background-color: #fff;
	border-radius: 3px;
	-webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
	        box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}
.nav-quick li {
	position: relative;
	display: block;
	padding: 0;
	text-align: center;
	list-style: none;
}
.nav-quick a {
	display: block;
	padding: 16px 0;
	color: #76838f;
	text-decoration: none;
}
.nav-quick a .icon {
	display: block;
	margin-bottom: .2em;
	font-size: 32px;
}
.nav-quick a:hover {
	background-color: #f3f7f9;
}
.nav-quick .label, .nav-quick .badge {
	position: absolute;
	top: 0;
	right: 0;
}
.nav-quick-sm a {
	padding: 12px 0;
}
.nav-quick-sm a .icon {
	font-size: 24px;
}
.nav-quick-lg a {
	padding: 22px 0;
}
.nav-quick-lg a .icon {
	font-size: 40px;
}
.nav-quick-bordered {
	border-top: 1px solid #e4eaec;
	border-left: 1px solid #e4eaec;
}
.nav-quick-bordered li {
	border-right: 1px solid #e4eaec;
	border-bottom: 1px solid #e4eaec;
}
.nav-pills > li > a {
	-webkit-transition: border .2s linear, color .2s linear, background-color .2s linear;
	     -o-transition: border .2s linear, color .2s linear, background-color .2s linear;
	        transition: border .2s linear, color .2s linear, background-color .2s linear;
}
.nav-pills-rounded > li > a {
	padding-right: 20px;
	padding-left: 20px;
	margin-right: 5px;
	margin-left: 5px;
	border-radius: 1000px;
}
.nav-tabs > li > a {
	padding: 10px 20px;
	line-height: 1.3;
	color: #76838f;
	-webkit-transition: .25s;
	     -o-transition: .25s;
	        transition: .25s;
}
.nav-tabs > li > a > .icon {
	width: 1em;
	margin-right: .5em;
	line-height: 1;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
	color: #fff;
	background-color: #f96868;
	border-color: transparent;
	border-bottom-color: #f96868;
}
.nav-tabs > li .dropdown-menu {
	margin-top: 3px;
}
.nav-tabs.nav-justified > li > a {
	border-radius: 4px 4px 0 0;
}
.nav-tabs.nav-justified > li.active > a, .nav-tabs.nav-justified > li.active > a:hover, .nav-tabs.nav-justified > li.active > a:focus {
	border-color: transparent;
	border-bottom-color: #f96868;
}
.nav-tabs.nav-tabs-bottom {
	border-top: 1px solid #e4eaec;
	border-bottom: none;
}
.nav-tabs.nav-tabs-bottom > li {
	margin-top: -1px;
	margin-bottom: 0;
}
.nav-tabs.nav-tabs-bottom > li > a {
	border-radius: 0 0 4px 4px;
}
.nav-tabs.nav-tabs-bottom > li > a:hover, .nav-tabs.nav-tabs-bottom > li > a:focus {
	border-top-color: #e4eaec;
	border-bottom-color: transparent;
}
.nav-tabs.nav-tabs-bottom.nav-justified {
	border-top: none;
}
.nav-tabs.nav-tabs-bottom.nav-justified > li > a {
	border-top-color: #e4eaec;
	border-bottom-color: transparent;
}
.nav-tabs.nav-tabs-bottom.nav-justified > li.active > a, .nav-tabs.nav-tabs-bottom.nav-justified > li.active > a:hover, .nav-tabs.nav-tabs-bottom.nav-justified > li.active > a:focus {
	border-top: 1px solid #f96868;
}
.nav-tabs-reverse > li {
	float: right;
}
.nav-tabs-reverse > li > a {
	margin-right: 0;
	margin-left: 2px;
}
.nav-tabs-solid {
	border-bottom-color: #f3f7f9;
}
.nav-tabs-solid > li > a:hover {
	border-color: transparent;
}
.nav-tabs-solid > li.active > a, .nav-tabs-solid > li.active > a:hover, .nav-tabs-solid > li.active > a:focus {
	color: #76838f;
	background-color: #f3f7f9;
	border-color: transparent;
}
.nav-tabs-solid ~ .tab-content {
	padding: 20px;
	background-color: #f3f7f9;
}
.nav-tabs-solid.nav-justified > li > a {
	border: none;
}
.nav-tabs-solid.nav-justified > li.active > a, .nav-tabs-solid.nav-justified > li.active > a:hover, .nav-tabs-solid.nav-justified > li.active > a:focus {
	border: none;
}
.nav-tabs-solid.nav-tabs-bottom > li.active > a, .nav-tabs-solid.nav-tabs-bottom > li.active > a:hover, .nav-tabs-solid.nav-tabs-bottom > li.active > a:focus {
	border: none;
}
.nav-tabs-line > li > a {
	padding: 10px 20px;
	border-bottom: 2px solid transparent;
}
.nav-tabs-line > li > a:hover, .nav-tabs-line > li > a:focus {
	background-color: transparent;
}
.nav-tabs-line > li > a:hover {
	border-bottom-color: #ccd5db;
}
.nav-tabs-line > li.active > a, .nav-tabs-line > li.active > a:hover, .nav-tabs-line > li.active > a:focus {
	color: #f96868;
	background-color: transparent;
	border-bottom: 2px solid #f96868;
}
.nav-tabs-line .open > a, .nav-tabs-line .open > a:hover, .nav-tabs-line .open > a:focus {
	border-color: transparent;
	border-bottom-color: #ccd5db;
}
.nav-tabs-line.nav-tabs-bottom > li > a {
	border-top: 2px solid transparent;
	border-bottom: none;
}
.nav-tabs-line.nav-tabs-bottom > li > a:hover {
	border-top-color: #ccd5db;
	border-bottom-color: transparent;
}
.nav-tabs-line.nav-tabs-bottom > li.active > a, .nav-tabs-line.nav-tabs-bottom > li.active > a:hover, .nav-tabs-line.nav-tabs-bottom > li.active > a:focus {
	border-top: 2px solid #f96868;
	border-bottom: none;
}
.nav-tabs-line.nav-justified > li > a {
	border-bottom: 2px solid #e4eaec;
}
.nav-tabs-line.nav-justified > li > a:hover {
	border-bottom-color: #ccd5db;
}
.nav-tabs-line.nav-justified > li.active > a, .nav-tabs-line.nav-justified > li.active > a:hover, .nav-tabs-line.nav-justified > li.active > a:focus {
	border-color: transparent;
	border-bottom: 2px solid #f96868;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom {
	border-top: none;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li > a {
	border-top: 2px solid #e4eaec;
	border-bottom: none;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li > a:hover {
	border-top-color: #ccd5db;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a, .nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a:hover, .nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a:focus {
	border-top-color: #f96868;
	border-bottom: none;
}
.nav-tabs-vertical:before, .nav-tabs-vertical:after {
	display: table;
	content: " ";
}
.nav-tabs-vertical:after {
	clear: both;
}
.nav-tabs-vertical .nav-tabs {
	float: left;
	border-right: 1px solid #e4eaec;
	border-bottom: none;
}
.nav-tabs-vertical .nav-tabs > li {
	float: none;
	margin-right: -1px;
	margin-bottom: 0;
}
.nav-tabs-vertical .nav-tabs > li > a {
	padding: 10px 20px;
	margin-right: 0;
	margin-bottom: 2px;
	border-radius: 4px 0 0 4px;
}
.nav-tabs-vertical .nav-tabs > li > a:hover {
	border-right-color: #e4eaec;
	border-bottom-color: transparent;
}
.nav-tabs-vertical .nav-tabs > li.active > a, .nav-tabs-vertical .nav-tabs > li.active > a:focus, .nav-tabs-vertical .nav-tabs > li.active > a:hover {
	border-right-color: #f96868;
}
.nav-tabs-vertical .nav-tabs-reverse {
	float: right;
	border-right: none;
	border-left: 1px solid #e4eaec;
}
.nav-tabs-vertical .nav-tabs-reverse > li {
	margin-right: 0;
	margin-left: -1px;
}
.nav-tabs-vertical .nav-tabs-reverse > li > a {
	margin-left: 0;
	border-radius: 0 4px 4px 0;
}
.nav-tabs-vertical .nav-tabs-reverse > li > a:hover {
	border-right-color: transparent;
	border-left-color: #e4eaec;
}
.nav-tabs-vertical .nav-tabs-reverse > li.active > a, .nav-tabs-vertical .nav-tabs-reverse > li.active > a:focus, .nav-tabs-vertical .nav-tabs-reverse > li.active > a:hover {
	border-left-color: #f96868;
}
.nav-tabs-vertical .nav-tabs-solid {
	border-right-color: #f3f7f9;
}
.nav-tabs-vertical .nav-tabs-solid > li > a:hover {
	border-color: transparent;
}
.nav-tabs-vertical .nav-tabs-solid > li.active > a, .nav-tabs-vertical .nav-tabs-solid > li.active > a:focus, .nav-tabs-vertical .nav-tabs-solid > li.active > a:hover {
	border-color: transparent;
}
.nav-tabs-vertical .nav-tabs-solid + .tab-content {
	padding: 20px;
}
.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
	border-left-color: #f3f7f9;
}
.nav-tabs-vertical .nav-tabs-line > li > a {
	border-right: 2px solid transparent;
	border-bottom: none;
}
.nav-tabs-vertical .nav-tabs-line > li > a:hover {
	border-right-color: #ccd5db;
}
.nav-tabs-vertical .nav-tabs-line > li.active > a, .nav-tabs-vertical .nav-tabs-line > li.active > a:hover, .nav-tabs-vertical .nav-tabs-line > li.active > a:focus {
	border-right: 2px solid #f96868;
	border-bottom: none;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li > a {
	border-right-width: 1px;
	border-left: 2px solid transparent;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li > a:hover {
	border-color: transparent;
	border-left-color: #ccd5db;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a, .nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a:hover, .nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a:focus {
	border-right: 1px solid transparent;
	border-left: 2px solid #f96868;
}
.nav-tabs-vertical .tab-content {
	overflow: hidden;
}
.nav-tabs-inverse .nav-tabs-solid {
	border-bottom-color: #fff;
}
.nav-tabs-inverse .nav-tabs-solid > li.active > a, .nav-tabs-inverse .nav-tabs-solid > li.active > a:hover, .nav-tabs-inverse .nav-tabs-solid > li.active > a:focus {
	color: #76838f;
	background-color: #fff;
}
.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid {
	border-right-color: #fff;
}
.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
	border-left-color: #fff;
}
.nav-tabs-inverse .tab-content {
	background: #fff;
}
.nav-tabs-animate .tab-content {
	overflow: hidden;
}
.nav-tabs-lg > li > a {
	padding: 12px 20px;
	font-size: 18px;
	line-height: 1.3333333;
}
.nav-tabs-sm > li > a {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
}
.navbar-toggle {
	height: 60px;
	padding: 19px 12px;
	margin-top: 13px;
	margin-top: 0;
	margin-bottom: 13px;
	margin-bottom: 0;
	line-height: 22px;
	background: transparent !important;
	-webkit-transition: color .25s linear;
	     -o-transition: color .25s linear;
	        transition: color .25s linear;
}
.navbar-toggle .icon {
	margin-top: -1px;
}
.navbar-toggle:hover {
	background: transparent !important;
}
.navbar-toggle-left {
	float: left;
	margin-right: 0;
	margin-left: 12px;
}
.navbar {
	border: none;
	-webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, .08);
	        box-shadow: 0 2px 4px rgba(0, 0, 0, .08);
}
.navbar-fixed-top, .navbar-fixed-bottom {
	width: 100%;
}
@media (min-width: 768px) {
	.hidden-float {
		display: block;
	}
}
@media (max-width: 767px) {
	.hidden-float {
		display: none !important;
	}
}
.navbar-brand {
	padding: 19px 20px;
	font-weight: 700;
}
.navbar-brand > .navbar-brand-logo {
	display: inline-block;
}
.navbar-brand-logo {
	height: 32px;
	margin-top: -5px;
}
.navbar-brand-text {
	margin-left: 6px;
}
@media (max-width: 767px) {
	.navbar-brand-center {
		position: absolute;
		left: 50%;
		-webkit-transform: translate(-50%, 0);
		    -ms-transform: translate(-50%, 0);
		     -o-transform: translate(-50%, 0);
		        transform: translate(-50%, 0);
	}
}
@media (min-width: 768px) {
	.navbar-mega .container, .navbar-mega .container-fluid {
		position: relative;
	}
}
.navbar-mega .dropdown-menu {
	left: auto;
}
.navbar-mega .dropdown-mega {
	position: static;
}
.navbar-mega .mega-content {
	padding: 20px 30px;
}
.navbar-mega .mega-menu {
	min-width: 150px;
	max-width: 100%;
}
.navbar-mega .mega-menu > ul {
	padding-left: 0;
}
.navbar-mega .mega-menu .list-icons {
	margin-bottom: 6px;
}
.navbar-mega .dropdown.dropdown-fw .dropdown-menu {
	right: 5px;
	left: 5px;
}
@media (max-width: 767px) {
	.navbar-mega .dropdown.dropdown-fw .dropdown-menu {
		right: 0;
		left: 0;
	}
}
.navbar-nav > li > a.navbar-avatar, .navbar-toolbar > li > a.navbar-avatar {
	padding-top: 15px;
	padding-bottom: 15px;
}
@media (max-width: 767px) {
	.navbar-nav > li > a.navbar-avatar {
		padding-top: 6px;
		padding-bottom: 6px;
	}
}
.navbar-avatar .avatar {
	width: 30px;
}
.navbar-form .icon {
	font-size: 16px;
	color: rgba(55, 71, 79, .4);
}
.navbar-form .form-control {
	background-color: #f3f7f9;
	border: none;
	border-radius: 38px;
}
@media (min-width: 768px) {
	.navbar-search.collapse {
		display: block !important;
		height: auto !important;
		overflow: visible !important;
		visibility: visible !important;
	}
}
@media (max-width: 767px) {
	.navbar-search {
		padding-right: 12px;
		padding-left: 12px;
	}
}
@media (max-width: 767px) {
	.navbar-search .navbar-form {
		margin-top: 0;
		margin-bottom: 0;
		border-bottom: none;
	}
}
.container > .navbar-search, .container-fluid > .navbar-search {
	margin-right: -12px;
	margin-left: -12px;
}
@media (min-width: 768px) {
	.container > .navbar-search, .container-fluid > .navbar-search {
		margin-right: 0;
		margin-left: 0;
	}
}
.navbar-search-overlap {
	position: absolute !important;
	top: 0;
	right: 0;
	left: 0;
	background-color: #fff;
}
.navbar-search-overlap .form-group, .navbar-search-overlap .form-control {
	display: block !important;
	margin: 0;
}
.navbar-search-overlap .form-control {
	height: 60px !important;
	background-color: transparent !important;
	border-radius: 0;
}
.navbar-search-overlap .form-control:focus {
	border-color: transparent;
}
.navbar-collapse-toolbar.in {
	overflow-y: visible;
}
.navbar-toolbar {
	float: left;
}
.navbar-toolbar:before, .navbar-toolbar:after {
	display: table;
	content: " ";
}
.navbar-toolbar:after {
	clear: both;
}
.navbar-toolbar > li {
	float: left;
}
.navbar-toolbar > li:before, .navbar-toolbar > li:after {
	display: table;
	content: " ";
}
.navbar-toolbar > li:after {
	clear: both;
}
.navbar-toolbar > li > a {
	padding-top: 19px;
	padding-bottom: 19px;
	line-height: 22px;
}
.navbar-toolbar .dropdown-menu {
	-webkit-transform-origin: 100% 0;
	    -ms-transform-origin: 100% 0;
	     -o-transform-origin: 100% 0;
	        transform-origin: 100% 0;
	-webkit-animation-duration: .3s;
	     -o-animation-duration: .3s;
	        animation-duration: .3s;
}
@media (max-width: 767px) {
	.navbar-toolbar .dropdown-menu:not(.dropdown-menu-media) {
		max-height: 400px;
		overflow-x: hidden;
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;
		-webkit-transform: translate3d(0, 0, 0);
		    -ms-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
	.navbar-toolbar .open {
		position: static;
	}
	.navbar-toolbar .open .dropdown-menu {
		right: 0;
		left: 0;
		float: none;
		width: auto;
		margin-top: 0;
		border-top-left-radius: 0;
		border-top-right-radius: 0;
	}
}
@media (max-width: 767px) and (max-device-width: 480px) and (orientation: landscape) {
	.navbar-toolbar .dropdown-menu:not(.dropdown-menu-media) {
		max-height: 200px;
	}
}
@media (max-width: 767px) {
	.navbar-toolbar-left {
		float: left !important;
	}
	.navbar-toolbar-right {
		float: right !important;
	}
}
.icon-fullscreen {
	font-family: "Web Icons";
}
.icon-fullscreen:before {
	content: "\f11d";
}
.icon-fullscreen.active:before {
	content: "\f11e";
}
.icon-menubar {
	font-family: "Web Icons";
}
.icon-menubar:before {
	content: "\f119";
}
.icon-menubar.active:before {
	content: "\f119";
}
.navbar-default .navbar-toolbar > li > a {
	color: #76838f;
}
.navbar-default .navbar-toolbar > li > a:hover, .navbar-default .navbar-toolbar > li > a:focus {
	color: #526069;
	background-color: rgba(243, 247, 249, .3);
}
.navbar-default .navbar-toolbar > .active > a, .navbar-default .navbar-toolbar > .active > a:hover, .navbar-default .navbar-toolbar > .active > a:focus {
	color: #526069;
	background-color: rgba(243, 247, 249, .6);
}
.navbar-default .navbar-toolbar > .disabled > a, .navbar-default .navbar-toolbar > .disabled > a:hover, .navbar-default .navbar-toolbar > .disabled > a:focus {
	color: #ccd5db;
	background-color: transparent;
}
.navbar-default .navbar-toggle {
	color: #76838f;
}
.navbar-default .navbar-toolbar > .open > a, .navbar-default .navbar-toolbar > .open > a:hover, .navbar-default .navbar-toolbar > .open > a:focus {
	color: #526069;
	background-color: rgba(243, 247, 249, .6);
}
.navbar-inverse .navbar-toolbar > li > a {
	color: #fff;
}
.navbar-inverse .navbar-toolbar > li > a:hover, .navbar-inverse .navbar-toolbar > li > a:focus {
	color: #fff;
	background-color: rgba(0, 0, 0, .1);
}
.navbar-inverse .navbar-toolbar > .active > a, .navbar-inverse .navbar-toolbar > .active > a:hover, .navbar-inverse .navbar-toolbar > .active > a:focus {
	color: #fff;
	background-color: rgba(0, 0, 0, .1);
}
.navbar-inverse .navbar-toolbar > .disabled > a, .navbar-inverse .navbar-toolbar > .disabled > a:hover, .navbar-inverse .navbar-toolbar > .disabled > a:focus {
	color: #fff;
	background-color: transparent;
}
.navbar-inverse .navbar-toggle {
	color: #fff;
}
.navbar-inverse .navbar-toolbar > .open > a, .navbar-inverse .navbar-toolbar > .open > a:hover, .navbar-inverse .navbar-toolbar > .open > a:focus {
	color: #fff;
	background-color: rgba(0, 0, 0, .1);
}
.breadcrumb {
	margin-bottom: 10px;
}
.breadcrumb li + li:before {
	padding: 0 5px;
}
.breadcrumb li .icon {
	text-decoration: none;
}
.breadcrumb li .icon:before {
	margin-right: 10px;
}
.breadcrumb-arrow > li + li:before {
	content: "\00bb\00a0";
}
.pagination {
	margin: 0;
}
.pagination li > a, .pagination li > span {
	padding: 4px 10px;
	-webkit-transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
	     -o-transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
	        transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
}
.pagination li > a:hover, .pagination li > span:hover, .pagination li > a:focus, .pagination li > span:focus {
	-webkit-transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
	     -o-transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
	        transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
}
.pagination li .icon {
	margin-top: -1px;
}
.pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus, .pagination > .disabled > a, .pagination > .disabled > a:hover, .pagination > .disabled > a:focus {
	color: #ccd5db;
	cursor: not-allowed;
	background-color: transparent;
	border-color: #e4eaec;
}
.pagination-gap > li > a {
	margin: 0 3px;
	border-radius: 3px;
}
.pagination-gap > li > a:hover {
	background-color: transparent;
	border-color: #f96868;
}
.pagination-gap > li:first-child > a, .pagination-gap > li:last-child > a {
	border-radius: 3px;
}
.pagination-no-border > li > a {
	border: none;
}
.pagination-lg > li > a, .pagination-lg > li > span {
	padding: 6px 14px;
	font-size: 18px;
	line-height: 1.3333333;
}
.pagination-lg > li:first-child > a, .pagination-lg > li:first-child > span {
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
.pagination-lg > li:last-child > a, .pagination-lg > li:last-child > span {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.pagination-sm > li > a, .pagination-sm > li > span {
	padding: 3px 8px;
	font-size: 14px;
	line-height: 1.3;
}
.pagination-sm > li:first-child > a, .pagination-sm > li:first-child > span {
	border-top-left-radius: 2px;
	border-bottom-left-radius: 2px;
}
.pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span {
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
}
.pager li > a, .pager li > span {
	padding: 4px 15px;
	color: #76838f;
	-webkit-transition: all .2s ease;
	     -o-transition: all .2s ease;
	        transition: all .2s ease;
}
.pager li > a:hover, .pager li > a:focus {
	color: #f96868;
}
.pager li .icon {
	margin-top: -1px;
}
.pager li > a:hover, .pager li > a:focus {
	border-color: #f96868;
}
.pager li:first-child {
	margin-right: 5px;
}
.pager .disabled > a, .pager .disabled > a:hover, .pager .disabled > a:focus, .pager .disabled > span {
	border-color: #e4eaec;
}
.pager-round li > a, .pager-round li > span {
	border-radius: 1000px;
}
.label {
	padding: .25em .6em .25em;
	font-weight: 400;
	border-radius: .3em;
}
.label.label-outline {
	color: #f3f7f9;
	background-color: transparent;
	border-color: #f3f7f9;
}
.label-outline {
	border: 1px solid transparent;
}
.label-round {
	border-radius: 1em;
}
.label-default {
	color: #76838f;
	background-color: #e4eaec;
}
.label-default[href]:hover, .label-default[href]:focus {
	background-color: #f3f7f9;
}
.label-default.label-outline {
	color: #e4eaec;
	background-color: transparent;
	border-color: #e4eaec;
}
.label-default[href]:hover, .label-default[href]:focus {
	color: #a3afb7;
}
.label-default.label-outline {
	color: #76838f;
}
.label-primary {
	background-color: #f96868;
}
.label-primary[href]:hover, .label-primary[href]:focus {
	background-color: #fa7a7a;
}
.label-primary.label-outline {
	color: #f96868;
	background-color: transparent;
	border-color: #f96868;
}
.label-success {
	background-color: #46be8a;
}
.label-success[href]:hover, .label-success[href]:focus {
	background-color: #5cd29d;
}
.label-success.label-outline {
	color: #46be8a;
	background-color: transparent;
	border-color: #46be8a;
}
.label-info {
	background-color: #57c7d4;
}
.label-info[href]:hover, .label-info[href]:focus {
	background-color: #77d6e1;
}
.label-info.label-outline {
	color: #57c7d4;
	background-color: transparent;
	border-color: #57c7d4;
}
.label-warning {
	background-color: #f2a654;
}
.label-warning[href]:hover, .label-warning[href]:focus {
	background-color: #f4b066;
}
.label-warning.label-outline {
	color: #f2a654;
	background-color: transparent;
	border-color: #f2a654;
}
.label-danger {
	background-color: #f96868;
}
.label-danger[href]:hover, .label-danger[href]:focus {
	background-color: #fa7a7a;
}
.label-danger.label-outline {
	color: #f96868;
	background-color: transparent;
	border-color: #f96868;
}
.label-dark {
	background-color: #526069;
}
.label-dark[href]:hover, .label-dark[href]:focus {
	background-color: #76838f;
}
.label-dark.label-outline {
	color: #526069;
	background-color: transparent;
	border-color: #526069;
}
.label-lg {
	font-size: 16px;
}
.label-sm {
	padding: .1em .5em .1em;
	font-size: 10px;
}
.badge {
	padding: 2px 6px;
}
.btn .badge {
	top: 0;
}
.badge.up {
	position: relative;
	top: -10px;
	margin: 0 -.8em;
	border-radius: 15px;
}
.badge.badge-absolute {
	position: absolute;
	top: -8px;
	right: -10px;
	z-index: 5;
}
.badge-radius {
	border-radius: 3px;
}
.badge-primary {
	color: #fff;
	background-color: #f96868;
}
.badge-primary[href]:hover, .badge-primary[href]:focus {
	color: #fff;
	background-color: #f73737;
}
.list-group-item.active > .badge-primary, .nav-pills > .active > a > .badge-primary {
	color: #fff;
	background-color: #f96868;
}
.badge-success {
	color: #fff;
	background-color: #46be8a;
}
.badge-success[href]:hover, .badge-success[href]:focus {
	color: #fff;
	background-color: #369b6f;
}
.list-group-item.active > .badge-success, .nav-pills > .active > a > .badge-success {
	color: #fff;
	background-color: #46be8a;
}
.badge-info {
	color: #fff;
	background-color: #57c7d4;
}
.badge-info[href]:hover, .badge-info[href]:focus {
	color: #fff;
	background-color: #33b6c5;
}
.list-group-item.active > .badge-info, .nav-pills > .active > a > .badge-info {
	color: #fff;
	background-color: #57c7d4;
}
.badge-warning {
	color: #fff;
	background-color: #f2a654;
}
.badge-warning[href]:hover, .badge-warning[href]:focus {
	color: #fff;
	background-color: #ee8d25;
}
.list-group-item.active > .badge-warning, .nav-pills > .active > a > .badge-warning {
	color: #fff;
	background-color: #f2a654;
}
.badge-danger {
	color: #fff;
	background-color: #f96868;
}
.badge-danger[href]:hover, .badge-danger[href]:focus {
	color: #fff;
	background-color: #f73737;
}
.list-group-item.active > .badge-danger, .nav-pills > .active > a > .badge-danger {
	color: #fff;
	background-color: #f96868;
}
.badge-dark {
	color: #fff;
	background-color: #526069;
}
.badge-dark[href]:hover, .badge-dark[href]:focus {
	color: #fff;
	background-color: #3c464c;
}
.list-group-item.active > .badge-dark, .nav-pills > .active > a > .badge-dark {
	color: #fff;
	background-color: #526069;
}
.badge-lg {
	padding: 5px 9px;
	font-size: 16px;
}
.badge-sm {
	padding: 2px 5px;
	font-size: 10px;
}
.jumbotron {
	padding: 0;
	border-radius: 3px;
}
.jumbotron > .jumbotron-photo img {
	width: 100%;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}
.jumbotron-contents {
	padding: 20px;
}
.jumbotron .carousel, .jumbotron .carousel-inner, .jumbotron .carousel-inner > .item.active img {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}
.jumbotron .carousel-inner > .item > a > img, .jumbotron .carousel-inner > .item > img {
	width: 100%;
}
.jumbotron h1, .jumbotron .h1 {
	font-size: 28px;
}
.jumbotron h2, .jumbotron .h2 {
	font-size: 24px;
}
@media screen and (min-width: 768px) {
	.jumbotron, .container .jumbotron {
		padding: 0;
	}
	.jumbotron h1, .jumbotron .h1 {
		font-size: 28px;
	}
}
.thumbnail {
	padding: 0;
	border: none;
	-webkit-transition: all .25s ease-in-out;
	     -o-transition: all .25s ease-in-out;
	        transition: all .25s ease-in-out;
}
.thumbnail .caption {
	position: relative;
	display: block;
	padding-right: 0;
	padding-left: 0;
}
.alert {
	padding-right: 10px;
	padding-left: 10px;
}
.alert ul {
	padding-left: 17px;
}
.alert ul li {
	padding-left: 0;
}
.panel > .alert {
	margin: 0;
}
.alert h1, .alert h2, .alert h3, .alert h4, .alert h5, .alert h6 {
	margin-bottom: 5px;
}
.alert p:last-child {
	margin-bottom: 0;
}
.alert-alt {
	color: #76838f;
	background-color: rgba(243, 247, 249, .8);
	border: none;
	border-left: 3px solid transparent;
}
.alert-alt a, .alert-alt .alert-link {
	text-decoration: none;
}
.alert-dismissible {
	padding-right: 30px;
}
.alert-dismissible .close {
	top: -2px;
	text-decoration: none;
	opacity: .6;
}
.alert-dismissible .close:hover, .alert-dismissible .close:focus {
	opacity: 1;
}
.alert-dismissible.alert-alt .close {
	color: #a3afb7;
	opacity: .6;
}
.alert-dismissible.alert-alt .close:hover, .alert-dismissible.alert-alt .close:focus {
	color: #a3afb7;
	opacity: 1;
}
.alert-icon {
	position: relative;
	padding-left: 40px;
}
.alert-icon > .icon {
	position: absolute;
	top: 13px;
	left: 10px;
	width: 1em;
	font-size: 16px;
	text-align: center;
}
.alert-avatar {
	position: relative;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 70px;
}
.alert-avatar > .avatar {
	position: absolute;
	top: 12px;
	left: 15px;
}
.page-alert .alert-wrap {
	max-height: 0;
	padding: 0;
	margin: 0;
	overflow: hidden;
	-webkit-transition: max-height .7s linear 0s;
	     -o-transition: max-height .7s linear 0s;
	        transition: max-height .7s linear 0s;
}
.page-alert .alert-wrap.in {
	max-height: 500px;
	-webkit-transition: max-height 1s linear 0s;
	     -o-transition: max-height 1s linear 0s;
	        transition: max-height 1s linear 0s;
}
.page-alert .alert-wrap .alert {
	margin: 0;
	text-align: left;
	border-radius: 0;
}
.alert-primary {
	color: #f96868;
	background-color: rgba(255, 234, 234, .8);
	border-color: #ffeaea;
}
.alert-primary hr {
	border-top-color: #ffd0d0;
}
.alert-primary .alert-link {
	color: #f73737;
}
.alert-primary .close {
	color: #f96868;
}
.alert-primary .close:hover, .alert-primary .close:focus {
	color: #f96868;
}
.alert-primary .alert-link {
	color: #e9595b;
}
.alert-alt.alert-primary {
	border-color: #f96868;
}
.alert-alt.alert-primary a, .alert-alt.alert-primary .alert-link {
	color: #f96868;
}
.alert-success .alert-link {
	color: #36ab7a;
}
.alert-alt.alert-success {
	border-color: #46be8a;
}
.alert-alt.alert-success a, .alert-alt.alert-success .alert-link {
	color: #46be8a;
}
.alert-info .alert-link {
	color: #47b8c6;
}
.alert-alt.alert-info {
	border-color: #57c7d4;
}
.alert-alt.alert-info a, .alert-alt.alert-info .alert-link {
	color: #57c7d4;
}
.alert-warning .alert-link {
	color: #ec9940;
}
.alert-alt.alert-warning {
	border-color: #f2a654;
}
.alert-alt.alert-warning a, .alert-alt.alert-warning .alert-link {
	color: #f2a654;
}
.alert-danger .alert-link {
	color: #e9595b;
}
.alert-alt.alert-danger {
	border-color: #f96868;
}
.alert-alt.alert-danger a, .alert-alt.alert-danger .alert-link {
	color: #f96868;
}
.alert-social {
	position: relative;
	padding-left: 65px;
}
.alert-social > .icon {
	position: absolute;
	top: 17px;
	bottom: 0;
	left: 15px;
	width: 1em;
	font-size: 30px;
	text-align: center;
}
.alert-wechat {
	color: #fff;
	background-color: #51c332;
	border-color: #51c332;
}
.alert-wechat hr {
	border-top-color: #49af2d;
}
.alert-wechat .alert-link {
	color: #e6e6e6;
}
.alert-wechat .close {
	color: #fff;
}
.alert-wechat .close:hover, .alert-wechat .close:focus {
	color: #fff;
}
.alert-wechat .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-qq {
	color: #fff;
	background-color: #12b7f5;
	border-color: #12b7f5;
}
.alert-qq hr {
	border-top-color: #0aa8e4;
}
.alert-qq .alert-link {
	color: #e6e6e6;
}
.alert-qq .close {
	color: #fff;
}
.alert-qq .close:hover, .alert-qq .close:focus {
	color: #fff;
}
.alert-qq .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-weibo {
	color: #fff;
	background-color: #e6624b;
	border-color: #e6624b;
}
.alert-weibo hr {
	border-top-color: #e34e35;
}
.alert-weibo .alert-link {
	color: #e6e6e6;
}
.alert-weibo .close {
	color: #fff;
}
.alert-weibo .close:hover, .alert-weibo .close:focus {
	color: #fff;
}
.alert-weibo .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-facebook {
	color: #fff;
	background-color: #3b5998;
	border-color: #3b5998;
}
.alert-facebook hr {
	border-top-color: #344e86;
}
.alert-facebook .alert-link {
	color: #e6e6e6;
}
.alert-facebook .close {
	color: #fff;
}
.alert-facebook .close:hover, .alert-facebook .close:focus {
	color: #fff;
}
.alert-facebook .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-twitter {
	color: #fff;
	background-color: #55acee;
	border-color: #55acee;
}
.alert-twitter hr {
	border-top-color: #3ea1ec;
}
.alert-twitter .alert-link {
	color: #e6e6e6;
}
.alert-twitter .close {
	color: #fff;
}
.alert-twitter .close:hover, .alert-twitter .close:focus {
	color: #fff;
}
.alert-twitter .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-google-plus {
	color: #fff;
	background-color: #dd4b39;
	border-color: #dd4b39;
}
.alert-google-plus hr {
	border-top-color: #d73925;
}
.alert-google-plus .alert-link {
	color: #e6e6e6;
}
.alert-google-plus .close {
	color: #fff;
}
.alert-google-plus .close:hover, .alert-google-plus .close:focus {
	color: #fff;
}
.alert-google-plus .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-linkedin {
	color: #fff;
	background-color: #0976b4;
	border-color: #0976b4;
}
.alert-linkedin hr {
	border-top-color: #08669c;
}
.alert-linkedin .alert-link {
	color: #e6e6e6;
}
.alert-linkedin .close {
	color: #fff;
}
.alert-linkedin .close:hover, .alert-linkedin .close:focus {
	color: #fff;
}
.alert-linkedin .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-flickr {
	color: #fff;
	background-color: #ff0084;
	border-color: #ff0084;
}
.alert-flickr hr {
	border-top-color: #e60077;
}
.alert-flickr .alert-link {
	color: #e6e6e6;
}
.alert-flickr .close {
	color: #fff;
}
.alert-flickr .close:hover, .alert-flickr .close:focus {
	color: #fff;
}
.alert-flickr .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-tumblr {
	color: #fff;
	background-color: #35465c;
	border-color: #35465c;
}
.alert-tumblr hr {
	border-top-color: #2c3a4c;
}
.alert-tumblr .alert-link {
	color: #e6e6e6;
}
.alert-tumblr .close {
	color: #fff;
}
.alert-tumblr .close:hover, .alert-tumblr .close:focus {
	color: #fff;
}
.alert-tumblr .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-github {
	color: #fff;
	background-color: #4183c4;
	border-color: #4183c4;
}
.alert-github hr {
	border-top-color: #3876b4;
}
.alert-github .alert-link {
	color: #e6e6e6;
}
.alert-github .close {
	color: #fff;
}
.alert-github .close:hover, .alert-github .close:focus {
	color: #fff;
}
.alert-github .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-dribbble {
	color: #fff;
	background-color: #c32361;
	border-color: #c32361;
}
.alert-dribbble hr {
	border-top-color: #ad1f56;
}
.alert-dribbble .alert-link {
	color: #e6e6e6;
}
.alert-dribbble .close {
	color: #fff;
}
.alert-dribbble .close:hover, .alert-dribbble .close:focus {
	color: #fff;
}
.alert-dribbble .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert-youtube {
	color: #fff;
	background-color: #b31217;
	border-color: #b31217;
}
.alert-youtube hr {
	border-top-color: #9c1014;
}
.alert-youtube .alert-link {
	color: #e6e6e6;
}
.alert-youtube .close {
	color: #fff;
}
.alert-youtube .close:hover, .alert-youtube .close:focus {
	color: #fff;
}
.alert-youtube .alert-link {
	font-weight: 700;
	color: #fff;
}
.alert.dark .alert-link {
	font-weight: 700;
	color: #fff !important;
}
.alert.dark .alert-left-border {
	border: none;
	border-left: 3px solid transparent;
}
.alert.dark.alert-dismissible.alert-alt .close {
	color: #fff;
}
.alert.dark.alert-dismissible.alert-alt .close:hover, .alert.dark.alert-dismissible.alert-alt .close:focus {
	color: #fff;
}
.alert.dark.alert-primary {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.alert.dark.alert-primary hr {
	border-top-color: #f84f4f;
}
.alert.dark.alert-primary .alert-link {
	color: #e6e6e6;
}
.alert.dark.alert-primary .close {
	color: #fff;
}
.alert.dark.alert-primary .close:hover, .alert.dark.alert-primary .close:focus {
	color: #fff;
}
.alert-alt.alert.dark.alert-primary {
	border-color: #d91d1f;
}
.alert-alt.alert.dark.alert-primary a, .alert-alt.alert.dark.alert-primary .alert-link {
	color: #fff;
}
.alert.dark.alert-success {
	color: #fff;
	background-color: #46be8a;
	border-color: #46be8a;
}
.alert.dark.alert-success hr {
	border-top-color: #3dae7d;
}
.alert.dark.alert-success .alert-link {
	color: #e6e6e6;
}
.alert.dark.alert-success .close {
	color: #fff;
}
.alert.dark.alert-success .close:hover, .alert.dark.alert-success .close:focus {
	color: #fff;
}
.alert-alt.alert.dark.alert-success {
	border-color: #247151;
}
.alert-alt.alert.dark.alert-success a, .alert-alt.alert.dark.alert-success .alert-link {
	color: #fff;
}
.alert.dark.alert-info {
	color: #fff;
	background-color: #57c7d4;
	border-color: #57c7d4;
}
.alert.dark.alert-info hr {
	border-top-color: #43c0cf;
}
.alert.dark.alert-info .alert-link {
	color: #e6e6e6;
}
.alert.dark.alert-info .close {
	color: #fff;
}
.alert.dark.alert-info .close:hover, .alert.dark.alert-info .close:focus {
	color: #fff;
}
.alert-alt.alert.dark.alert-info {
	border-color: #2e8893;
}
.alert-alt.alert.dark.alert-info a, .alert-alt.alert.dark.alert-info .alert-link {
	color: #fff;
}
.alert.dark.alert-warning {
	color: #fff;
	background-color: #f2a654;
	border-color: #f2a654;
}
.alert.dark.alert-warning hr {
	border-top-color: #f09a3c;
}
.alert.dark.alert-warning .alert-link {
	color: #e6e6e6;
}
.alert.dark.alert-warning .close {
	color: #fff;
}
.alert.dark.alert-warning .close:hover, .alert.dark.alert-warning .close:focus {
	color: #fff;
}
.alert-alt.alert.dark.alert-warning {
	border-color: #cb7314;
}
.alert-alt.alert.dark.alert-warning a, .alert-alt.alert.dark.alert-warning .alert-link {
	color: #fff;
}
.alert.dark.alert-danger {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.alert.dark.alert-danger hr {
	border-top-color: #f84f4f;
}
.alert.dark.alert-danger .alert-link {
	color: #e6e6e6;
}
.alert.dark.alert-danger .close {
	color: #fff;
}
.alert.dark.alert-danger .close:hover, .alert.dark.alert-danger .close:focus {
	color: #fff;
}
.alert-alt.alert.dark.alert-danger {
	border-color: #d91d1f;
}
.alert-alt.alert.dark.alert-danger a, .alert-alt.alert.dark.alert-danger .alert-link {
	color: #fff;
}
.progress {
	height: 12px;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.progress-bar {
	line-height: 12px;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.progress-square {
	border-radius: 0;
}
.progress-circle {
	border-radius: 1000px;
}
.progress-vertical {
	position: relative;
	display: inline-block;
	width: 12px;
	height: 250px;
	min-height: 250px;
	margin-right: 30px;
	margin-bottom: 0;
}
.progress-vertical .progress-bar {
	width: 100%;
}
.progress-vertical .progress-label {
	display: block;
	-webkit-transform: rotate(90deg);
	    -ms-transform: rotate(90deg);
	     -o-transform: rotate(90deg);
	        transform: rotate(90deg);
}
.progress-bar-indicating.active {
	position: relative;
	-webkit-animation: none;
	     -o-animation: none;
	        animation: none;
}
.progress-bar-indicating.active:before {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	content: "";
	background-color: #fff;
	border-radius: inherit;
	opacity: 0;
	-webkit-animation: progress-active 3s ease 0s infinite;
	     -o-animation: progress-active 3s ease 0s infinite;
	        animation: progress-active 3s ease 0s infinite;
}
.progress-vertical .progress-bar-indicating.active:before {
	-webkit-animation-name: progress-vertical-active;
	     -o-animation-name: progress-vertical-active;
	        animation-name: progress-vertical-active;
}
.progress-skill {
	position: relative;
}
.progress-skill .progress-bar > span {
	position: absolute;
	top: 0;
	right: 10px;
	color: #526069;
}
.progress-lg {
	height: 22px;
}
.progress-lg.progress-vertical {
	width: 16px;
}
.progress-lg .progress-bar {
	line-height: 22px;
}
.progress-sm {
	height: 8px;
}
.progress-sm.progress-vertical {
	width: 8px;
}
.progress-sm .progress-bar {
	line-height: 8px;
}
.progress-xs {
	height: 6px;
	border-radius: 1px;
}
.progress-xs.progress-vertical {
	width: 6px;
}
.progress-xs .progress-bar {
	line-height: 6px;
}
.contextual-progress {
	margin: 20px 0;
}
.contextual-progress .progress-title {
	float: left;
}
.contextual-progress .progress-label {
	position: relative;
	float: right;
}
.contextual-progress .progress {
	height: 2px;
	margin: 5px 0;
}
.contextual-progress .progress .progress-label {
	display: none;
}
@-webkit-keyframes progress-active {
	0% {
		width: 0;
		opacity: .4;
	}
	100% {
		width: 100%;
		opacity: 0;
	}
}
@-o-keyframes progress-active {
	0% {
		width: 0;
		opacity: .4;
	}
	100% {
		width: 100%;
		opacity: 0;
	}
}
@keyframes progress-active {
	0% {
		width: 0;
		opacity: .4;
	}
	100% {
		width: 100%;
		opacity: 0;
	}
}
@-webkit-keyframes progress-vertical-active {
	0% {
		top: 0;
		opacity: 0;
	}
	100% {
		top: 175px;
		opacity: .4;
	}
}
@-o-keyframes progress-vertical-active {
	0% {
		top: 0;
		opacity: 0;
	}
	100% {
		top: 175px;
		opacity: .4;
	}
}
@keyframes progress-vertical-active {
	0% {
		top: 0;
		opacity: 0;
	}
	100% {
		top: 175px;
		opacity: .4;
	}
}
.media {
	padding: 15px 0;
	margin-top: 0;
	border-bottom: 1px solid #e4eaec;
}
.media-object {
	width: 120px;
}
.media-left, .media > .pull-left {
	padding-right: 10px;
}
.media-right, .media > .pull-right {
	padding-left: 10px;
}
.media-body {
	overflow: visible;
}
.media .media {
	padding-bottom: 0;
	border-bottom: none;
}
.media-meta {
	margin-bottom: 3px;
	font-size: 12px;
	color: #526069;
}
.media-lg .media-object {
	width: 160px;
}
.media-lg .media {
	margin-left: -110px;
}
.media-sm .media-object {
	width: 80px;
}
.media-sm .media {
	margin-left: -70px;
}
.media-xs .media-object {
	width: 60px;
}
.media-xs .media {
	margin-left: -60px;
}
@media screen and (min-width: 768px) {
	.media .media {
		margin-left: 0;
	}
}
.list-group .media {
	padding: 2px 0;
	border-bottom: 0;
}
.list-group .media .pull-left, .list-group .media .media-left {
	padding-right: 20px;
}
.list-group .media .pull-right, .list-group .media .media-right {
	padding-left: 20px;
}
.list-group .media .pull-right .status {
	margin-top: 15px;
	margin-right: 5px;
}
.list-group .media .media-heading {
	font-size: 14px;
}
.list-group .media .status {
	margin-top: 2px;
}
.list-group-full > .list-group-item {
	padding-right: 0;
	padding-left: 0;
}
a.list-group-item {
	border-radius: 3px;
}
a.list-group-item.disabled, a.list-group-item.disabled:hover, a.list-group-item.disabled:focus {
	color: #ccd5db;
	background-color: #f3f7f9;
}
a.list-group-item.active, a.list-group-item.active:hover, a.list-group-item.active:focus {
	color: #fff;
	background-color: #f96868;
}
.list-group-item {
	padding: 7px 10px;
}
.list-group-item .icon {
	margin-right: 10px;
}
.list-group-item .badge {
	margin-top: 3px;
}
.list-group.bg-inherit {
	border-radius: 3px;
}
.list-group.bg-inherit .list-group-item {
	background-color: transparent;
	border-bottom-color: rgba(0, 0, 0, .075);
}
.list-group.bg-inherit .list-group-item:last-child {
	border-bottom-color: transparent;
}
.list-group.bg-inherit .list-group-item:hover {
	background-color: rgba(0, 0, 0, .075);
	border-color: transparent;
}
.list-group-bordered .list-group-item {
	border-color: #e4eaec;
}
.list-group-bordered .list-group-item.active, .list-group-bordered .list-group-item.active:hover, .list-group-bordered .list-group-item.active:focus {
	color: #fff;
	background-color: #e9595b;
	border-color: #e9595b;
}
.list-group-dividered .list-group-item {
	border-top-color: #e4eaec;
}
.list-group-dividered .list-group-item.active:hover {
	border-top-color: #e4eaec;
}
.list-group-dividered .list-group-item:last-child {
	border-bottom-color: #e4eaec;
}
.list-group-dividered .list-group-item:first-child {
	border-top-color: transparent;
}
.list-group-dividered .list-group-item:first-child.active:hover {
	border-top-color: transparent;
}
.list-group-gap .list-group-item {
	margin-bottom: 2px;
	border-radius: 3px;
}
.list-group-full .list-group-item {
	padding-right: 0;
	padding-left: 0;
}
.list-group-item-dark {
	color: #fff;
	background-color: #526069;
}
a.list-group-item-dark, button.list-group-item-dark {
	color: #fff;
}
a.list-group-item-dark .list-group-item-heading, button.list-group-item-dark .list-group-item-heading {
	color: inherit;
}
a.list-group-item-dark:hover, button.list-group-item-dark:hover, a.list-group-item-dark:focus, button.list-group-item-dark:focus {
	color: #fff;
	background-color: #47535b;
}
a.list-group-item-dark.active, button.list-group-item-dark.active, a.list-group-item-dark.active:hover, button.list-group-item-dark.active:hover, a.list-group-item-dark.active:focus, button.list-group-item-dark.active:focus {
	color: #fff;
	background-color: #fff;
	border-color: #fff;
}
.panel {
	position: relative;
	margin-bottom: 24px;
	border: none;
}
.panel > .nav-tabs-vertical .nav-tabs {
	margin-left: -1px;
}
.panel > .nav-tabs-vertical .nav-tabs > li > a {
	border-left: none;
	border-radius: 0;
}
.panel > .nav-tabs-vertical .nav-tabs.nav-tabs-reverse {
	margin-right: -1px;
}
.panel > .nav-tabs-vertical .nav-tabs.nav-tabs-reverse > li > a {
	border-right: none;
	border-radius: 0;
}
.panel:hover .panel-actions .show-on-hover {
	display: inline-block;
}
.panel .panel-actions .show-on-hover {
	display: none;
}
.panel.is-fullscreen {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	z-index: 9999;
	width: 100%;
	margin-bottom: 0;
	border-radius: 0;
}
.panel.is-fullscreen .panel-loading {
	border-radius: 0;
}
.panel.is-fullscreen .panel-actions [data-toggle=collapse] {
	display: none;
}
.panel.is-fullscreen .panel-body {
	max-height: 100%;
	overflow: auto;
}
.panel.is-close {
	display: none;
}
.panel.is-collapse .panel-body {
	display: none;
	height: 0;
}
.panel > .alert {
	padding-right: 20px;
	padding-left: 20px;
}
.panel > .alert-dismissible {
	padding-right: 40px;
}
@media screen and (max-width: 480px) {
	.panel > .alert {
		padding-right: 20px;
		padding-left: 20px;
	}
	.panel > .alert-dismissible {
		padding-right: 40px;
	}
}
.panel > .table > tr > td:first-child, .panel > .table-responsive > .table > tr > td:first-child, .panel > .table > thead > tr > td:first-child, .panel > .table-responsive > .table > thead > tr > td:first-child, .panel > .table > tbody > tr > td:first-child, .panel > .table-responsive > .table > tbody > tr > td:first-child, .panel > .table > tfoot > tr > td:first-child, .panel > .table-responsive > .table > tfoot > tr > td:first-child, .panel > .table > tr > th:first-child, .panel > .table-responsive > .table > tr > th:first-child, .panel > .table > thead > tr > th:first-child, .panel > .table-responsive > .table > thead > tr > th:first-child, .panel > .table > tbody > tr > th:first-child, .panel > .table-responsive > .table > tbody > tr > th:first-child, .panel > .table > tfoot > tr > th:first-child, .panel > .table-responsive > .table > tfoot > tr > th:first-child {
	padding-left: 20px;
}
@media screen and (max-width: 480px) {
	.panel > .table > tr > td:first-child, .panel > .table-responsive > .table > tr > td:first-child, .panel > .table > thead > tr > td:first-child, .panel > .table-responsive > .table > thead > tr > td:first-child, .panel > .table > tbody > tr > td:first-child, .panel > .table-responsive > .table > tbody > tr > td:first-child, .panel > .table > tfoot > tr > td:first-child, .panel > .table-responsive > .table > tfoot > tr > td:first-child, .panel > .table > tr > th:first-child, .panel > .table-responsive > .table > tr > th:first-child, .panel > .table > thead > tr > th:first-child, .panel > .table-responsive > .table > thead > tr > th:first-child, .panel > .table > tbody > tr > th:first-child, .panel > .table-responsive > .table > tbody > tr > th:first-child, .panel > .table > tfoot > tr > th:first-child, .panel > .table-responsive > .table > tfoot > tr > th:first-child {
		padding-left: 20px;
	}
}
.panel > .table > tr > td:last-child, .panel > .table-responsive > .table > tr > td:last-child, .panel > .table > thead > tr > td:last-child, .panel > .table-responsive > .table > thead > tr > td:last-child, .panel > .table > tbody > tr > td:last-child, .panel > .table-responsive > .table > tbody > tr > td:last-child, .panel > .table > tfoot > tr > td:last-child, .panel > .table-responsive > .table > tfoot > tr > td:last-child, .panel > .table > tr > th:last-child, .panel > .table-responsive > .table > tr > th:last-child, .panel > .table > thead > tr > th:last-child, .panel > .table-responsive > .table > thead > tr > th:last-child, .panel > .table > tbody > tr > th:last-child, .panel > .table-responsive > .table > tbody > tr > th:last-child, .panel > .table > tfoot > tr > th:last-child, .panel > .table-responsive > .table > tfoot > tr > th:last-child {
	padding-right: 20px;
}
@media screen and (max-width: 480px) {
	.panel > .table > tr > td:last-child, .panel > .table-responsive > .table > tr > td:last-child, .panel > .table > thead > tr > td:last-child, .panel > .table-responsive > .table > thead > tr > td:last-child, .panel > .table > tbody > tr > td:last-child, .panel > .table-responsive > .table > tbody > tr > td:last-child, .panel > .table > tfoot > tr > td:last-child, .panel > .table-responsive > .table > tfoot > tr > td:last-child, .panel > .table > tr > th:last-child, .panel > .table-responsive > .table > tr > th:last-child, .panel > .table > thead > tr > th:last-child, .panel > .table-responsive > .table > thead > tr > th:last-child, .panel > .table > tbody > tr > th:last-child, .panel > .table-responsive > .table > tbody > tr > th:last-child, .panel > .table > tfoot > tr > th:last-child, .panel > .table-responsive > .table > tfoot > tr > th:last-child {
		padding-right: 20px;
	}
}
.panel > .table > tbody:first-child > tr:first-child th, .panel > .table > tbody:first-child > tr:first-child td {
	border-top: 1px solid #e4eaec;
}
.panel > .list-group > .list-group-item {
	padding-right: 20px;
	padding-left: 20px;
}
@media screen and (max-width: 480px) {
	.panel > .list-group > .list-group-item {
		padding-right: 20px;
		padding-left: 20px;
	}
}
.panel-content > .row {
	padding-right: 30px;
	padding-left: 30px;
}
.panel-content > .row > [class*="col-"] {
	padding-right: 30px;
	padding-left: 30px;
}
.panel-heading {
	position: relative;
	padding: 0;
	border-bottom: 1px solid transparent;
}
.panel-heading + .alert {
	border-radius: 0;
}
.panel-heading > .nav-tabs {
	border-bottom: none;
}
.panel-heading + .nav-tabs {
	margin-top: -10px;
}
.panel-body {
	position: relative;
}
.panel-heading + .panel-body {
	padding-top: 0;
}
.panel-body h1:first-child, .panel-body h2:first-child, .panel-body h3:first-child, .panel-body h4:first-child, .panel-body h5:first-child, .panel-body h6:first-child, .panel-body .h1:first-child, .panel-body .h2:first-child, .panel-body .h3:first-child, .panel-body .h4:first-child, .panel-body .h5:first-child, .panel-body .h6:first-child {
	margin-top: 0;
}
.panel-body > *:last-child {
	margin-bottom: 0;
}
.panel-body > .list-group-dividered:only-child > .list-group-item:last-child {
	border-bottom-color: transparent;
}
.panel-footer {
	border-top: 1px solid transparent;
}
.table + .panel-footer {
	padding-top: 10px;
	border-color: #e4eaec;
}
.panel-title {
	display: block;
	padding: 16px 20px;
	font-size: 16px;
	color: #37474f;
}
.panel-title > .icon {
	margin-right: 10px;
}
.panel-title > .label {
	margin-left: 10px;
}
.panel-title small {
	color: #76838f;
}
.panel-desc {
	display: block;
	padding: 5px 0 0;
	margin: 0;
	font-size: 14px;
	color: #76838f;
}
.panel-actions {
	position: absolute;
	top: 50%;
	right: 20px;
	z-index: 1;
	margin: auto;
	-webkit-transform: translate(0%, -50%);
	    -ms-transform: translate(0%, -50%);
	     -o-transform: translate(0%, -50%);
	        transform: translate(0%, -50%);
}
@media screen and (max-width: 480px) {
	.panel-actions {
		right: 20px;
	}
}
ul.panel-actions {
	list-style: none;
}
ul.panel-actions > li {
	display: inline-block;
	margin-left: 8px;
}
ul.panel-actions > li:first-child {
	margin-left: 0;
}
.panel-actions a {
	color: inherit;
}
.panel-actions a.dropdown-toggle {
	text-decoration: none;
}
.panel-actions .dropdown {
	display: inline-block;
}
.panel-actions .dropdown-toggle {
	display: inline-block;
}
.panel-actions .panel-action {
	display: inline-block;
	padding: 8px 10px;
	color: #a3afb7;
	text-decoration: none;
	cursor: pointer;
	background-color: transparent;
}
.panel-actions .panel-action:hover {
	color: #526069;
}
.panel-actions .panel-action:active {
	color: #526069;
}
.panel-actions .panel-action:focus {
	outline: none;
}
.panel-actions .progress {
	width: 100px;
	margin: 0;
}
.panel-actions .pagination {
	margin: 0;
}
.panel-toolbar {
	padding: 5px 15px;
	margin: 0;
	background-color: transparent;
	border-top: 1px solid #e4eaec;
	border-bottom: 1px solid #e4eaec;
}
.panel-bordered .panel-toolbar {
	border-top-color: transparent;
}
.panel-toolbar .btn {
	padding: 5px 10px;
	color: #a3afb7;
}
.panel-toolbar .btn.icon {
	width: 1em;
	text-align: center;
}
.panel-toolbar .btn:hover, .panel-toolbar .btn:active, .panel-toolbar .btn.active {
	color: #76838f;
}
.panel-loading {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 6;
	display: none;
	width: 100%;
	height: 100%;
	border-radius: 1px;
	opacity: .6;
}
.panel-loading .loader {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	    -ms-transform: translate(-50%, -50%);
	     -o-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
}
.panel > *:not(.panel-loading):not(.collapsing) {
	-webkit-transition: opacity .3s;
	     -o-transition: opacity .3s;
	        transition: opacity .3s;
}
.panel.is-loading > *:not(.panel-loading) {
	opacity: .3;
}
.panel.is-loading .panel-loading {
	display: block;
	opacity: 1;
}
.panel-footer-chart {
	padding: 0;
}
.panel-control {
	padding: 0;
	border: none;
	border-radius: 0;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.panel-body.scrollable-vertical {
	padding-right: 0 !important;
	padding-left: 0 !important;
}
.panel-body.scrollable-vertical > .scrollable-container > .scrollable-content {
	padding-right: 20px;
	padding-left: 20px;
}
@media screen and (max-width: 480px) {
	.panel-body.scrollable-vertical > .scrollable-container > .scrollable-content {
		padding-right: 20px;
		padding-left: 20px;
	}
}
.panel-body.scrollable-vertical > .scrollable-bar {
	height: -webkit-calc(100% - 20px);
	height:         calc(100% - 20px);
	margin-top: 0;
	margin-bottom: 20px;
	-webkit-transform: translateX(-16px);
	    -ms-transform: translateX(-16px);
	     -o-transform: translateX(-16px);
	        transform: translateX(-16px);
}
.panel-bordered > .panel-body.scrollable-vertical > .scrollable-bar {
	height: -webkit-calc(100% - 40px);
	height:         calc(100% - 40px);
	margin-bottom: 20px;
}
.panel-body.scrollable-horizontal {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}
.panel-body.scrollable-horizontal > .scrollable-container > .scrollable-content {
	padding-top: 0;
	padding-bottom: 20px;
}
.panel-bordered > .panel-body.scrollable-horizontal > .scrollable-container > .scrollable-content {
	padding-top: 20px;
	padding-bottom: 20px;
}
.panel-body.scrollable-horizontal > .scrollable-bar {
	width: -webkit-calc(100% - 40px);
	width:         calc(100% - 40px);
	margin-right: 20px;
	margin-left: 0;
	-webkit-transform: translateY(-16px);
	    -ms-transform: translateY(-16px);
	     -o-transform: translateY(-16px);
	        transform: translateY(-16px);
}
@media screen and (max-width: 480px) {
	.panel-body.scrollable-horizontal > .scrollable-bar {
		width: -webkit-calc(100% - 40px);
		width:         calc(100% - 40px);
		margin-right: 20px;
	}
}
.panel-bordered > .panel-body.scrollable-horizontal > .scrollable-bar {
	-webkit-transform: translateY(-16px);
	    -ms-transform: translateY(-16px);
	     -o-transform: translateY(-16px);
	        transform: translateY(-16px);
}
.panel-bordered > .panel-heading {
	border-bottom: 1px solid #e4eaec;
}
.panel-bordered > .panel-heading > .panel-title {
	padding-bottom: 16px;
}
.panel-bordered > .panel-footer {
	padding-top: 10px;
	border-top: 1px solid #e4eaec;
}
.panel-bordered > .panel-body {
	padding-top: 20px;
}
.panel-bordered > .table > tbody:first-child > tr:first-child th, .panel-bordered > .table > tbody:first-child > tr:first-child td {
	border-top: 0;
}
.panel.is-dragging {
	opacity: .8;
}
.panel.is-dragging {
	cursor: move;
}
.panel.panel-transparent {
	background: transparent;
	border-color: transparent;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.panel.panel-transparent > .panel-heading, .panel.panel-transparent > .panel-footer {
	border-color: transparent;
}
.panel-dark {
	border-color: #526069;
}
.panel-dark > .panel-heading {
	color: #fff;
	background-color: #526069;
	border-color: #526069;
}
.panel-dark > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: #526069;
}
.panel-dark > .panel-heading .badge {
	color: #526069;
	background-color: #fff;
}
.panel-dark > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: #526069;
}
.panel-primary, .panel-info, .panel-success, .panel-warning, .panel-danger, .panel-dark {
	border: none;
}
.panel-primary .panel-heading, .panel-info .panel-heading, .panel-success .panel-heading, .panel-warning .panel-heading, .panel-danger .panel-heading, .panel-dark .panel-heading {
	border: none;
}
.panel-primary .panel-title, .panel-info .panel-title, .panel-success .panel-title, .panel-warning .panel-title, .panel-danger .panel-title, .panel-dark .panel-title {
	color: #fff;
}
.panel-primary .panel-action, .panel-info .panel-action, .panel-success .panel-action, .panel-warning .panel-action, .panel-danger .panel-action, .panel-dark .panel-action {
	color: #fff;
}
@media screen and (max-width: 480px) {
	.panel-actions {
		right: 20px;
	}
	.panel-actions .progress {
		min-width: 80px;
	}
	.panel-actions .show-on-hover {
		display: none;
	}
	.panel-title, .panel-body, .panel-footer {
		padding-right: 20px;
		padding-left: 20px;
	}
}
.well {
	padding: 20px;
}
.well-lg {
	padding: 24px;
}
.well-sm {
	padding: 12px;
}
.well {
	-webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, .02);
	        box-shadow: inset 0 0 1px rgba(0, 0, 0, .02);
}
.well-primary {
	color: #fff;
	background-color: #f96868;
}
.well-success {
	color: #fff;
	background-color: #46be8a;
}
.well-info {
	color: #fff;
	background-color: #57c7d4;
}
.well-warning {
	color: #fff;
	background-color: #f2a654;
}
.well-danger {
	color: #fff;
	background-color: #f96868;
}
.close {
	font-weight: 300;
}
.close.icon {
	font-size: inherit;
}
.modal-content {
	border: none;
	border-radius: 0;
	-webkit-box-shadow: 1px 1px 15px rgba(0, 0, 0, .3);
	        box-shadow: 1px 1px 15px rgba(0, 0, 0, .3);
}
.modal-content > iframe {
	float: left;
	width: 100%;
	height: 0;
	margin: 0;
	border: none;
}
.modal-header {
	padding: 10px 20px;
	background-color: #f3f7f9;
}
.modal-header .close {
	margin-top: 1px;
}
.modal-body {
	padding: 20px;
}
.modal-body > *:last-child {
	margin-bottom: 0;
}
.modal-footer {
	padding: 10px 20px 20px;
	border-top: none;
}
.modal-top {
	margin: 0 auto;
}
.modal-center {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: -webkit-box;
	display:         flex;
	height: 100%;
	margin: 0 auto !important;

	-webkit-justify-content: center;
	-ms-flex-pack: center;
	-webkit-box-pack: center;
	        justify-content: center;
	-webkit-flex-flow: column nowrap;
	    -ms-flex-flow: column nowrap;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	        flex-flow: column nowrap;
	-webkit-align-content: stretch;
	-ms-flex-line-pack: center;
	        align-content: center;
}
.modal-bottom {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: -webkit-box;
	display:         flex;
	height: 100%;
	margin: 0 auto !important;

	-webkit-justify-content: flex-end;
	-ms-flex-pack: end;
	-webkit-box-pack: end;
	        justify-content: flex-end;
	-webkit-flex-flow: column nowrap;
	    -ms-flex-flow: column nowrap;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	        flex-flow: column nowrap;
	-webkit-align-content: stretch;
	-ms-flex-line-pack: center;
	        align-content: center;
}
.modal-sidebar {
	position: absolute;
	right: 0;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: -webkit-box;
	display:         flex;
	height: 100%;
	margin: 0 !important;
	overflow-y: auto;
	background-color: #fff;

	-webkit-justify-content: center;
	-ms-flex-pack: center;
	-webkit-box-pack: center;
	        justify-content: center;
	-webkit-flex-flow: column nowrap;
	    -ms-flex-flow: column nowrap;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	        flex-flow: column nowrap;
	-webkit-align-content: stretch;
	-ms-flex-line-pack: center;
	        align-content: center;
}
.modal-sidebar .modal-content {
	position: static;
	overflow: auto;
	background-color: transparent;
	border-radius: 0;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.modal-sidebar .modal-header {
	background-color: transparent;
	border-bottom: none;
}
.modal-sidebar .modal-footer {
	border-top: none;
}
.modal.fade .modal-dialog.modal-sidebar {
	-webkit-transform: translate(25%, 0);
	    -ms-transform: translate(25%, 0);
	     -o-transform: translate(25%, 0);
	        transform: translate(25%, 0);
}
.modal.in .modal-dialog.modal-sidebar {
	-webkit-transform: translate(0, 0);
	    -ms-transform: translate(0, 0);
	     -o-transform: translate(0, 0);
	        transform: translate(0, 0);
}
.modal-fill-in {
	background-color: transparent;
}
.modal-fill-in.in {
	background-color: rgba(255, 255, 255, .95);
	opacity: 1;
}
.modal-fill-in .modal-dialog {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: -webkit-box;
	display:         flex;
	width: 100%;
	height: 100%;
	margin: 0 auto;

	-webkit-justify-content: center;
	-ms-flex-pack: center;
	-webkit-box-pack: center;
	        justify-content: center;
	-webkit-flex-flow: column nowrap;
	    -ms-flex-flow: column nowrap;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	        flex-flow: column nowrap;
	-webkit-align-content: stretch;
	-ms-flex-line-pack: center;
	        align-content: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	-webkit-box-align: center;
	        align-items: center;
}
@media (min-width: 768px) {
	.modal-fill-in .modal-dialog > * {
		width: 500px;
	}
	.modal-fill-in .modal-dialog.modal-sm > * {
		width: 300px;
	}
	.modal-fill-in .modal-dialog button.close {
		position: fixed;
		top: 20px;
		right: 20px;
		z-index: 1;
		filter: alpha(opacity=50);
		opacity: .5;
		-webkit-transform: translate(0%, 0%);
		    -ms-transform: translate(0%, 0%);
		     -o-transform: translate(0%, 0%);
		        transform: translate(0%, 0%);
	}
}
@media (min-width: 992px) {
	.modal-fill-in .modal-dialog.modal-lg > * {
		width: 900px;
	}
}
.modal-fill-in .modal-content {
	background-color: transparent;
	border-radius: 0;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.modal-fill-in .modal-header {
	background-color: transparent;
	border-bottom: none;
}
.modal-fill-in .modal-footer {
	border-top: none;
}
.modal-primary .modal-header {
	background-color: #f96868;
	border-radius: 0 0 0 0;
}
.modal-primary .modal-header * {
	color: #fff;
}
.modal-primary .modal-header .close {
	opacity: .6;
}
.modal-success .modal-header {
	background-color: #46be8a;
	border-radius: 0 0 0 0;
}
.modal-success .modal-header * {
	color: #fff;
}
.modal-success .modal-header .close {
	opacity: .6;
}
.modal-info .modal-header {
	background-color: #57c7d4;
	border-radius: 0 0 0 0;
}
.modal-info .modal-header * {
	color: #fff;
}
.modal-info .modal-header .close {
	opacity: .6;
}
.modal-warning .modal-header {
	background-color: #f2a654;
	border-radius: 0 0 0 0;
}
.modal-warning .modal-header * {
	color: #fff;
}
.modal-warning .modal-header .close {
	opacity: .6;
}
.modal-danger .modal-header {
	background-color: #f96868;
	border-radius: 0 0 0 0;
}
.modal-danger .modal-header * {
	color: #fff;
}
.modal-danger .modal-header .close {
	opacity: .6;
}
.modal.modal-fade-in-scale-up .modal-dialog {
	opacity: 0;
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: scale(.7);
	    -ms-transform: scale(.7);
	     -o-transform: scale(.7);
	        transform: scale(.7);
}
.modal.modal-fade-in-scale-up.in .modal-dialog {
	opacity: 1;
	-webkit-transform: scale(1);
	    -ms-transform: scale(1);
	     -o-transform: scale(1);
	        transform: scale(1);
}
.modal.modal-slide-in-right .modal-dialog {
	opacity: 0;
	-webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9);
	     -o-transition: all .3s cubic-bezier(.25, .5, .5, .9);
	        transition: all .3s cubic-bezier(.25, .5, .5, .9);
	-webkit-transform: translate(20%, 0%);
	    -ms-transform: translate(20%, 0%);
	     -o-transform: translate(20%, 0%);
	        transform: translate(20%, 0%);
}
.modal.modal-slide-in-right.in .modal-dialog {
	opacity: 1;
	-webkit-transform: translate(0px, 0);
	    -ms-transform: translate(0px, 0);
	     -o-transform: translate(0px, 0);
	        transform: translate(0px, 0);
}
.modal.modal-slide-from-bottom .modal-dialog {
	opacity: 0;
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: translate(0%, 20%);
	    -ms-transform: translate(0%, 20%);
	     -o-transform: translate(0%, 20%);
	        transform: translate(0%, 20%);
}
.modal.modal-slide-from-bottom.in .modal-dialog {
	opacity: 1;
	-webkit-transform: translate(0px, 0);
	    -ms-transform: translate(0px, 0);
	     -o-transform: translate(0px, 0);
	        transform: translate(0px, 0);
}
.modal.modal-newspaper .modal-dialog {
	opacity: 0;
	-webkit-transition: all .5s ease 0s;
	     -o-transition: all .5s ease 0s;
	        transition: all .5s ease 0s;
	-webkit-transform: scale(0) rotate(720deg);
	    -ms-transform: scale(0) rotate(720deg);
	     -o-transform: scale(0) rotate(720deg);
	        transform: scale(0) rotate(720deg);
}
.modal.modal-newspaper.in .modal-dialog {
	opacity: 1;
	-webkit-transform: scale(1) rotate(0deg);
	    -ms-transform: scale(1) rotate(0deg);
	     -o-transform: scale(1) rotate(0deg);
	        transform: scale(1) rotate(0deg);
}
.modal.modal-fall {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-fall .modal-dialog {
	opacity: 0;
	-webkit-transform: translateZ(600px) rotateX(20deg);
	    -ms-transform: translateZ(600px) rotateX(20deg);
	        transform: translateZ(600px) rotateX(20deg);

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-fall.in .modal-dialog {
	opacity: 1;
	-webkit-transition: all .3s ease-in 0s;
	     -o-transition: all .3s ease-in 0s;
	        transition: all .3s ease-in 0s;
	-webkit-transform: translateZ(0px) rotateX(0deg);
	    -ms-transform: translateZ(0px) rotateX(0deg);
	        transform: translateZ(0px) rotateX(0deg);
}
.modal.modal-side-fall {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-side-fall .modal-dialog {
	-webkit-transform: translate(30%) translateZ(600px) rotate(10deg);
	    -ms-transform: translate(30%) translateZ(600px) rotate(10deg);
	        transform: translate(30%) translateZ(600px) rotate(10deg);

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-side-fall.in .modal-dialog {
	-webkit-transition: all .3s ease-in 0s;
	     -o-transition: all .3s ease-in 0s;
	        transition: all .3s ease-in 0s;
	-webkit-transform: translate(0%) translateZ(0px) rotate(0deg);
	    -ms-transform: translate(0%) translateZ(0px) rotate(0deg);
	        transform: translate(0%) translateZ(0px) rotate(0deg);
}
.modal.modal-3d-flip-horizontal {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-3d-flip-horizontal .modal-dialog {
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: rotateY(-70deg);
	    -ms-transform: rotateY(-70deg);
	     -o-transform: rotateY(-70deg);
	        transform: rotateY(-70deg);

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-3d-flip-horizontal.in .modal-dialog {
	-webkit-transform: rotateY(0deg);
	    -ms-transform: rotateY(0deg);
	     -o-transform: rotateY(0deg);
	        transform: rotateY(0deg);
}
.modal.modal-3d-flip-vertical {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-3d-flip-vertical .modal-dialog {
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: rotateX(-70deg);
	    -ms-transform: rotateX(-70deg);
	     -o-transform: rotateX(-70deg);
	        transform: rotateX(-70deg);

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-3d-flip-vertical.in .modal-dialog {
	-webkit-transform: rotateX(0deg);
	    -ms-transform: rotateX(0deg);
	     -o-transform: rotateX(0deg);
	        transform: rotateX(0deg);
}
.modal.modal-3d-sign {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-3d-sign .modal-dialog {
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: rotateX(-60deg);
	    -ms-transform: rotateX(-60deg);
	     -o-transform: rotateX(-60deg);
	        transform: rotateX(-60deg);
	-webkit-transform-origin: 50% 0 0;
	   -moz-transform-origin: 50% 0 0;
	    -ms-transform-origin: 50% 0 0;
	     -o-transform-origin: 50% 0 0;
	        transform-origin: 50% 0 0;

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-3d-sign.in .modal-dialog {
	-webkit-transform: rotateX(0deg);
	    -ms-transform: rotateX(0deg);
	     -o-transform: rotateX(0deg);
	        transform: rotateX(0deg);
}
.modal.modal-super-scaled .modal-dialog {
	opacity: 0;
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: scale(2);
	    -ms-transform: scale(2);
	     -o-transform: scale(2);
	        transform: scale(2);
}
.modal.modal-super-scaled.in .modal-dialog {
	opacity: 1;
	-webkit-transform: scale(1);
	    -ms-transform: scale(1);
	     -o-transform: scale(1);
	        transform: scale(1);
}
.modal.modal-just-me .modal-dialog {
	opacity: 0;
	-webkit-transition: all .3s ease 0s;
	     -o-transition: all .3s ease 0s;
	        transition: all .3s ease 0s;
	-webkit-transform: scale(.8);
	    -ms-transform: scale(.8);
	     -o-transform: scale(.8);
	        transform: scale(.8);
}
.modal.modal-just-me .modal-backdrop {
	background-color: #fff;
}
.modal.modal-just-me.in {
	background: #fff;
}
.modal.modal-just-me.in .modal-dialog {
	opacity: 1;
	-webkit-transform: scale(1);
	    -ms-transform: scale(1);
	     -o-transform: scale(1);
	        transform: scale(1);
}
.modal.modal-just-me.in .modal-backdrop {
	opacity: 1;
}
.modal.modal-3d-slit {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-3d-slit .modal-dialog {
	opacity: 0;
	-webkit-transition: all .5s ease 0s;
	     -o-transition: all .5s ease 0s;
	        transition: all .5s ease 0s;
	-webkit-transform: translateZ(-3000px) rotateY(90deg);
	    -ms-transform: translateZ(-3000px) rotateY(90deg);
	        transform: translateZ(-3000px) rotateY(90deg);

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-3d-slit.in .modal-dialog {
	-webkit-animation-name: slit;
	     -o-animation-name: slit;
	        animation-name: slit;
	-webkit-animation-duration: .7s;
	     -o-animation-duration: .7s;
	        animation-duration: .7s;
	-webkit-animation-timing-function: ease-out;
	     -o-animation-timing-function: ease-out;
	        animation-timing-function: ease-out;

	-webkit-animation-fill-mode: forwards;
	     -o-animation-fill-mode: forwards;
	        animation-fill-mode: forwards;
}
.modal.modal-rotate-from-bottom {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-rotate-from-bottom .modal-dialog {
	-webkit-transition: all .3s ease-out 0s;
	     -o-transition: all .3s ease-out 0s;
	        transition: all .3s ease-out 0s;
	-webkit-transform: translateY(100%) rotateX(90deg);
	    -ms-transform: translateY(100%) rotateX(90deg);
	        transform: translateY(100%) rotateX(90deg);
	-webkit-transform-origin: 0 100% 0;
	   -moz-transform-origin: 0 100% 0;
	    -ms-transform-origin: 0 100% 0;
	     -o-transform-origin: 0 100% 0;
	        transform-origin: 0 100% 0;

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-rotate-from-bottom.in .modal-dialog {
	-webkit-transform: translateY(0%) rotateX(0deg);
	    -ms-transform: translateY(0%) rotateX(0deg);
	        transform: translateY(0%) rotateX(0deg);
}
.modal.modal-rotate-from-left {
	-webkit-perspective: 1300px;
	   -moz-perspective: 1300px;
	        perspective: 1300px;
}
.modal.modal-rotate-from-left .modal-dialog {
	-webkit-transition: all .3s ease-out 0s;
	     -o-transition: all .3s ease-out 0s;
	        transition: all .3s ease-out 0s;
	-webkit-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
	    -ms-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
	        transform: translateZ(100px) translateX(-30%) rotateY(90deg);
	-webkit-transform-origin: 0 100% 0;
	   -moz-transform-origin: 0 100% 0;
	    -ms-transform-origin: 0 100% 0;
	     -o-transform-origin: 0 100% 0;
	        transform-origin: 0 100% 0;

	    -ms-transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	        transform-style: preserve-3d;
}
.modal.modal-rotate-from-left.in .modal-dialog {
	-webkit-transform: translateZ(0px) translateX(0%) rotateY(0deg);
	    -ms-transform: translateZ(0px) translateX(0%) rotateY(0deg);
	        transform: translateZ(0px) translateX(0%) rotateY(0deg);
}
@-webkit-keyframes slit {
	50% {
		opacity: .5;
		-webkit-transform: translateZ(-250px) rotateY(89deg);
		    -ms-transform: translateZ(-250px) rotateY(89deg);
		        transform: translateZ(-250px) rotateY(89deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: translateZ(0px) rotateY(0deg);
		    -ms-transform: translateZ(0px) rotateY(0deg);
		        transform: translateZ(0px) rotateY(0deg);
	}
}
@-o-keyframes slit {
	50% {
		opacity: .5;
		-webkit-transform: translateZ(-250px) rotateY(89deg);
		    -ms-transform: translateZ(-250px) rotateY(89deg);
		        transform: translateZ(-250px) rotateY(89deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: translateZ(0px) rotateY(0deg);
		    -ms-transform: translateZ(0px) rotateY(0deg);
		        transform: translateZ(0px) rotateY(0deg);
	}
}
@keyframes slit {
	50% {
		opacity: .5;
		-webkit-transform: translateZ(-250px) rotateY(89deg);
		    -ms-transform: translateZ(-250px) rotateY(89deg);
		        transform: translateZ(-250px) rotateY(89deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: translateZ(0px) rotateY(0deg);
		    -ms-transform: translateZ(0px) rotateY(0deg);
		        transform: translateZ(0px) rotateY(0deg);
	}
}
@media (min-width: 768px) {
	.modal-dialog {
		margin: 90px auto;
	}
}
.tooltip-inner {
	padding: 6px 12px;
	white-space: nowrap;
}
.tooltip-primary + .tooltip .tooltip-inner {
	color: #fff;
	background-color: #f96868;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
	border-top-color: #f96868;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
	border-right-color: #f96868;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
	border-bottom-color: #f96868;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
	border-left-color: #f96868;
}
.tooltip-success + .tooltip .tooltip-inner {
	color: #fff;
	background-color: #46be8a;
}
.tooltip-success + .tooltip.top .tooltip-arrow {
	border-top-color: #46be8a;
}
.tooltip-success + .tooltip.right .tooltip-arrow {
	border-right-color: #46be8a;
}
.tooltip-success + .tooltip.bottom .tooltip-arrow {
	border-bottom-color: #46be8a;
}
.tooltip-success + .tooltip.left .tooltip-arrow {
	border-left-color: #46be8a;
}
.tooltip-info + .tooltip .tooltip-inner {
	color: #fff;
	background-color: #57c7d4;
}
.tooltip-info + .tooltip.top .tooltip-arrow {
	border-top-color: #57c7d4;
}
.tooltip-info + .tooltip.right .tooltip-arrow {
	border-right-color: #57c7d4;
}
.tooltip-info + .tooltip.bottom .tooltip-arrow {
	border-bottom-color: #57c7d4;
}
.tooltip-info + .tooltip.left .tooltip-arrow {
	border-left-color: #57c7d4;
}
.tooltip-warning + .tooltip .tooltip-inner {
	color: #fff;
	background-color: #f2a654;
}
.tooltip-warning + .tooltip.top .tooltip-arrow {
	border-top-color: #f2a654;
}
.tooltip-warning + .tooltip.right .tooltip-arrow {
	border-right-color: #f2a654;
}
.tooltip-warning + .tooltip.bottom .tooltip-arrow {
	border-bottom-color: #f2a654;
}
.tooltip-warning + .tooltip.left .tooltip-arrow {
	border-left-color: #f2a654;
}
.tooltip-danger + .tooltip .tooltip-inner {
	color: #fff;
	background-color: #f96868;
}
.tooltip-danger + .tooltip.top .tooltip-arrow {
	border-top-color: #f96868;
}
.tooltip-danger + .tooltip.right .tooltip-arrow {
	border-right-color: #f96868;
}
.tooltip-danger + .tooltip.bottom .tooltip-arrow {
	border-bottom-color: #f96868;
}
.tooltip-danger + .tooltip.left .tooltip-arrow {
	border-left-color: #f96868;
}
.tooltip-rotate + .tooltip {
	opacity: 0;
	-webkit-animation: tooltip-rotate3d 1s ease .1s forwards;
	     -o-animation: tooltip-rotate3d 1s ease .1s forwards;
	        animation: tooltip-rotate3d 1s ease .1s forwards;
}
@-webkit-keyframes tooltip-rotate3d {
	0% {
		opacity: 0;
		-webkit-transform: rotate(15deg);
		        transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
}
@-o-keyframes tooltip-rotate3d {
	0% {
		opacity: 0;
		-o-transform: rotate(15deg);
		   transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-o-transform: rotate(0deg);
		   transform: rotate(0deg);
	}
}
@keyframes tooltip-rotate3d {
	0% {
		opacity: 0;
		-webkit-transform: rotate(15deg);
		     -o-transform: rotate(15deg);
		        transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
}
.tooltip-scale + .tooltip {
	-webkit-animation: tooltip-scale3d 1s ease 0s forwards;
	     -o-animation: tooltip-scale3d 1s ease 0s forwards;
	        animation: tooltip-scale3d 1s ease 0s forwards;
}
@-webkit-keyframes tooltip-scale3d {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
		        transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
		        transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
@-o-keyframes tooltip-scale3d {
	0% {
		opacity: 0;
		transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
@keyframes tooltip-scale3d {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
		        transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
		        transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
.popover {
	padding: 0;
	-webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
	        box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
}
.popover.bottom > .arrow:after {
	border-bottom-color: #f3f7f9;
}
.popover-content {
	padding: 20px;
}
.popover-primary + .popover .popover-title {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.popover-primary + .popover.bottom .arrow {
	border-bottom-color: #f96868;
}
.popover-primary + .popover.bottom .arrow:after {
	border-bottom-color: #f96868;
}
.popover-success + .popover .popover-title {
	color: #fff;
	background-color: #46be8a;
	border-color: #46be8a;
}
.popover-success + .popover.bottom .arrow {
	border-bottom-color: #46be8a;
}
.popover-success + .popover.bottom .arrow:after {
	border-bottom-color: #46be8a;
}
.popover-info + .popover .popover-title {
	color: #fff;
	background-color: #57c7d4;
	border-color: #57c7d4;
}
.popover-info + .popover.bottom .arrow {
	border-bottom-color: #57c7d4;
}
.popover-info + .popover.bottom .arrow:after {
	border-bottom-color: #57c7d4;
}
.popover-warning + .popover .popover-title {
	color: #fff;
	background-color: #f2a654;
	border-color: #f2a654;
}
.popover-warning + .popover.bottom .arrow {
	border-bottom-color: #f2a654;
}
.popover-warning + .popover.bottom .arrow:after {
	border-bottom-color: #f2a654;
}
.popover-danger + .popover .popover-title {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.popover-danger + .popover.bottom .arrow {
	border-bottom-color: #f96868;
}
.popover-danger + .popover.bottom .arrow:after {
	border-bottom-color: #f96868;
}
.popover-rotate + .popover {
	opacity: 0;
	-webkit-animation: popover-rotate3d 1s ease .1s forwards;
	     -o-animation: popover-rotate3d 1s ease .1s forwards;
	        animation: popover-rotate3d 1s ease .1s forwards;
}
@-webkit-keyframes popover-rotate3d {
	0% {
		opacity: 0;
		-webkit-transform: rotate(15deg);
		        transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
}
@-o-keyframes popover-rotate3d {
	0% {
		opacity: 0;
		-o-transform: rotate(15deg);
		   transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-o-transform: rotate(0deg);
		   transform: rotate(0deg);
	}
}
@keyframes popover-rotate3d {
	0% {
		opacity: 0;
		-webkit-transform: rotate(15deg);
		     -o-transform: rotate(15deg);
		        transform: rotate(15deg);
	}
	100% {
		opacity: 1;
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
}
.popover-scale + .popover {
	-webkit-animation: popover-scale3d 1s ease 0s forwards;
	     -o-animation: popover-scale3d 1s ease 0s forwards;
	        animation: popover-scale3d 1s ease 0s forwards;
}
@-webkit-keyframes popover-scale3d {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
		        transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
		        transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
@-o-keyframes popover-scale3d {
	0% {
		opacity: 0;
		transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
@keyframes popover-scale3d {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
		        transform: scale3d(.7, .3, 1) translate(50%, 50%);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
		        transform: scale3d(1, 1, 1) translate(50%, 50%);
	}
}
.carousel-control {
	min-width: 50px;
}
.carousel-control:hover, .carousel-control:focus {
	filter: alpha(opacity=40);
	opacity: .4;
}
.carousel-control .icon {
	position: absolute;
	top: 50%;
	z-index: 5;
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-top: -8px;
}
.carousel-control.left .icon {
	left: 50%;
	margin-left: -8px;
}
.carousel-control.right .icon {
	right: 50%;
	margin-right: -8px;
}
.carousel-caption h1, .carousel-caption h2, .carousel-caption h3, .carousel-caption h4, .carousel-caption h5, .carousel-caption h6 {
	color: inherit;
}
.carousel-indicators {
	margin-bottom: 0;
}
.carousel-indicators li {
	margin: 3px;
	background-color: rgba(255, 255, 255, .3);
	border: none;
}
.carousel-indicators .active {
	width: 10px;
	height: 10px;
	margin: 3px;
}
.carousel-indicators-scaleup li {
	border: none;
	-webkit-transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	     -o-transition: background-color .3s ease 0s, -o-transform .3s ease 0s;
	        transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
}
.carousel-indicators-scaleup .active {
	-webkit-transform: scale(1.5);
	    -ms-transform: scale(1.5);
	     -o-transform: scale(1.5);
	        transform: scale(1.5);
}
.carousel-indicators-fillin li {
	background-color: transparent;
	-webkit-box-shadow: 0 0 0 2px #fff inset;
	        box-shadow: 0 0 0 2px #fff inset;
	-webkit-transition: -webkit-box-shadow .3s ease 0s;
	     -o-transition:         box-shadow .3s ease 0s;
	        transition: -webkit-box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s, -webkit-box-shadow .3s ease 0s;
}
.carousel-indicators-fillin .active {
	-webkit-box-shadow: 0 0 0 8px #fff inset;
	        box-shadow: 0 0 0 8px #fff inset;
}
.carousel-indicators-fall li {
	position: relative;
	-webkit-transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	     -o-transition: background-color .3s ease 0s, -o-transform .3s ease 0s;
	        transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
}
.carousel-indicators-fall li:after {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	visibility: hidden;
	content: "";
	background-color: rgba(0, 0, 0, .3);
	border-radius: 50%;
	opacity: 0;
	-webkit-transition: opacity .3s ease 0s, visibility 0s ease .3s;
	     -o-transition: opacity .3s ease 0s, visibility 0s ease .3s;
	        transition: opacity .3s ease 0s, visibility 0s ease .3s;
	-webkit-transform: translate(0%, -200%);
	    -ms-transform: translate(0%, -200%);
	     -o-transform: translate(0%, -200%);
	        transform: translate(0%, -200%);
}
.carousel-indicators-fall .active {
	background-color: transparent;
	-webkit-transform: translate(0, 200%);
	    -ms-transform: translate(0, 200%);
	     -o-transform: translate(0, 200%);
	        transform: translate(0, 200%);
}
.carousel-indicators-fall .active:after {
	visibility: visible;
	opacity: 1;
	-webkit-transition: opacity .3s ease 0s;
	     -o-transition: opacity .3s ease 0s;
	        transition: opacity .3s ease 0s;
}
@media screen and (min-width: 768px) {
	.carousel-control .icon {
		width: 24px;
		height: 24px;
		margin-top: -12px;
		font-size: 24px;
	}
	.carousel-control.left .icon {
		margin-left: -12px;
	}
	.carousel-control.right .icon {
		margin-right: -12px;
	}
}
.vertical-align {
	font-size: 0;
}
.vertical-align:before {
	display: inline-block;
	height: 100%;
	vertical-align: middle;
	content: "";
}
.vertical-align-middle, .vertical-align-bottom {
	display: inline-block;
	max-width: 100%;
	font-size: 14px;
}
.vertical-align-middle {
	vertical-align: middle;
}
.vertical-align-bottom {
	vertical-align: bottom;
}
.inline {
	display: inline !important;
}
.inline-block {
	display: inline-block !important;
}
.block {
	display: block !important;
}
.text-truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	word-wrap: normal;
	/* for IE */
	white-space: nowrap;
}
.text-break {
	-webkit-hyphens: auto;
	   -moz-hyphens: auto;
	        hyphens: auto;
	word-wrap: break-word;
	white-space: normal;

	    -ms-hyphens: auto;
}
.text-nowrap {
	white-space: nowrap;
}
.text-top {
	vertical-align: top !important;
}
.text-middle {
	vertical-align: middle !important;
}
.text-bottom {
	vertical-align: bottom !important;
}
.text-left {
	text-align: left !important;
}
.text-right {
	text-align: right !important;
}
.text-center {
	text-align: center !important;
}
.text-justify {
	text-align: justify !important;
}
.text-lowercase {
	text-transform: lowercase !important;
}
.text-uppercase {
	text-transform: uppercase !important;
}
.text-capitalize {
	text-transform: capitalize !important;
}
.font-weight-unset {
	font-weight: unset !important;
}
.font-weight-100 {
	font-weight: 100 !important;
}
.font-weight-200 {
	font-weight: 200 !important;
}
.font-weight-300 {
	font-weight: 300 !important;
}
.font-weight-400 {
	font-weight: 400 !important;
}
.font-weight-500 {
	font-weight: 500 !important;
}
.font-weight-600 {
	font-weight: 600 !important;
}
.font-weight-700 {
	font-weight: 700 !important;
}
.font-weight-800 {
	font-weight: 800 !important;
}
.font-weight-900 {
	font-weight: 900 !important;
}
.font-weight-light {
	font-weight: 300 !important;
}
.font-weight-normal {
	font-weight: 400 !important;
}
.font-weight-medium {
	font-weight: 500 !important;
}
.font-weight-bold {
	font-weight: 700 !important;
}
.font-size-0 {
	font-size: 0 !important;
}
.font-size-10 {
	font-size: 10px !important;
}
.font-size-12 {
	font-size: 12px !important;
}
.font-size-14 {
	font-size: 14px !important;
}
.font-size-16 {
	font-size: 16px !important;
}
.font-size-18 {
	font-size: 18px !important;
}
.font-size-20 {
	font-size: 20px !important;
}
.font-size-24 {
	font-size: 24px !important;
}
.font-size-26 {
	font-size: 26px !important;
}
.font-size-30 {
	font-size: 30px !important;
}
.font-size-40 {
	font-size: 40px !important;
}
.font-size-50 {
	font-size: 50px !important;
}
.font-size-60 {
	font-size: 60px !important;
}
.font-size-70 {
	font-size: 70px !important;
}
.font-size-80 {
	font-size: 80px !important;
}
.visible-xlg {
	display: none !important;
}
.visible-xlg-block, .visible-xlg-inline, .visible-xlg-inline-block {
	display: none !important;
}
@media (min-width: 1600px) {
	.visible-xlg {
		display: block !important;
	}
	table.visible-xlg {
		display: table !important;
	}
	tr.visible-xlg {
		display: table-row !important;
	}
	th.visible-xlg, td.visible-xlg {
		display: table-cell !important;
	}
}
@media (min-width: 1600px) {
	.visible-xlg-block {
		display: block !important;
	}
}
@media (min-width: 1600px) {
	.visible-xlg-inline {
		display: inline !important;
	}
}
@media (min-width: 1200px) {
	.visible-xlg-inline-block {
		display: inline-block !important;
	}
}
@media (min-width: 1600px) {
	.hidden-xlg {
		display: none !important;
	}
}
body.layout-full {
	height: 100vh;
	overflow-x: hidden;
}
.page {
	position: relative;
	min-height: 100vh;
	overflow: visible;
	background: #f1f4f5;
}
.page-dark.layout-full {
	color: #fff;
}
.page-dark.layout-full:before {
	position: fixed;
	top: 0;
	left: 0;
	z-index: -1;
	width: 100%;
	height: 100%;
	content: "";
	background-position: center top;
	-webkit-background-size: cover;
	        background-size: cover;
}
.page-dark.layout-full:after {
	position: fixed;
	top: 0;
	left: 0;
	z-index: -1;
	width: 100%;
	height: 100%;
	content: "";
	background-color: rgba(38, 50, 56, .6);
}
.page-dark.layout-full .brand {
	margin-bottom: 22px;
}
.page-dark.layout-full .brand-text {
	font-size: 18px;
	color: #fff;
	text-transform: uppercase;
}
.page-nav-tabs {
	padding: 0 20px;
}
.page-content {
	padding: 20px 20px;
}
.page-content > *:not(script):last-child {
	margin-bottom: 0 !important;
}
.page-content-actions {
	padding: 0 20px 20px;
}
.page-content-actions .dropdown {
	display: inline-block;
}
.page-content-actions:before, .page-content-actions:after {
	display: table;
	content: " ";
}
.page-content-actions:after {
	clear: both;
}
.page-content-table {
	max-width: 100%;
	padding: 0;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}
.page-content-table .table > thead > tr > th, .page-content-table .table > tbody > tr > th, .page-content-table .table > thead > tr > td, .page-content-table .table > tbody > tr > td {
	padding-top: 20px;
	padding-bottom: 20px;
}
.page-content-table .table > thead > tr > th:first-child, .page-content-table .table > tbody > tr > th:first-child, .page-content-table .table > thead > tr > td:first-child, .page-content-table .table > tbody > tr > td:first-child {
	padding-left: 20px;
}
.page-content-table .table > thead > tr > th:last-child, .page-content-table .table > tbody > tr > th:last-child, .page-content-table .table > thead > tr > td:last-child, .page-content-table .table > tbody > tr > td:last-child {
	padding-right: 20px;
}
.page-content-table .table > tbody > tr:hover > td {
	background-color: #f3f7f9;
}
.page-content-table .table > tbody > tr > td {
	cursor: pointer;
}
.page-content-table .table > tbody > tr:last-child td {
	border-bottom: 1px solid #e4eaec;
}
.page-content-table .table.is-indent > thead > tr > th.pre-cell, .page-content-table .table.is-indent > tbody > tr > th.pre-cell, .page-content-table .table.is-indent > thead > tr > td.pre-cell, .page-content-table .table.is-indent > tbody > tr > td.pre-cell, .page-content-table .table.is-indent > thead > tr > th.suf-cell, .page-content-table .table.is-indent > tbody > tr > th.suf-cell, .page-content-table .table.is-indent > thead > tr > td.suf-cell, .page-content-table .table.is-indent > tbody > tr > td.suf-cell {
	width: 30px;
	padding: 0;
	border-top: none;
	border-bottom: none;
}
.page-content-table .table.is-indent > tbody > tr:first-child td {
	border-top: none;
}
.page-content-table .table.is-indent > tbody > tr:last-child td.pre-cell, .page-content-table .table.is-indent > tbody > tr:last-child td.suf-cell {
	border-bottom: none;
}
.page-content-table .table.is-indent > tfoot > tr > td {
	border-top: none;
}
.page-content-table .pagination {
	margin-right: 20px;
	margin-left: 20px;
}
.page-copyright {
	margin-top: 60px;
	font-size: 12px;
	color: #37474f;
	letter-spacing: 1px;
}
.page-copyright .social a {
	margin: 0 10px;
	text-decoration: none;
}
.page-copyright .social .icon {
	font-size: 16px;
	color: rgba(55, 71, 79, .6);
}
.page-copyright .social .icon:hover, .page-copyright .social .icon:focus {
	color: rgba(55, 71, 79, .8);
}
.page-copyright .social .icon.active, .page-copyright .social .icon:active {
	color: #37474f;
}
.page-copyright-inverse {
	color: #fff;
}
.page-copyright-inverse .social .icon {
	color: #fff;
}
.page-copyright-inverse .social .icon:hover, .page-copyright-inverse .social .icon:active {
	color: rgba(255, 255, 255, .8);
}
.page-header + .page-content {
	padding-top: 0;
}
.page-title {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 18px;
}
.page-title > .icon {
	margin-right: .3em;
}
.page-description {
	margin-top: 6px;
	color: #a3afb7;
}
.page-header {
	position: relative;
	padding: 20px 20px;
	margin-top: 0;
	margin-bottom: 0;
	background: transparent;
	border-bottom: none;
}
.page-header-actions {
	position: absolute;
	top: 50%;
	right: 20px;
	z-index: 1;
	margin: auto;
	-webkit-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	     -o-transform: translateY(-50%);
	        transform: translateY(-50%);
}
.page-header-actions .btn-icon {
	margin-left: 6px;
}
.page-header-actions > * {
	margin-bottom: 0;
}
.page-header .breadcrumb {
	padding: 0;
	margin: 6px 0 0;
}
.page-header-bordered {
	padding-top: 20px;
	padding-bottom: 20px;
	margin-bottom: 20px;
	background-color: #fff;
	border-bottom: 1px solid transparent;
}
.page-header-tabs {
	padding-bottom: 0;
}
.page-header-tabs .nav-tabs-line {
	margin-top: 5px;
	border-bottom-color: transparent;
}
.page-header-tabs .nav-tabs-line > li > a {
	padding: 5px 20px;
}
.page-aside {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 220px;
	overflow-y: hidden;
	background: #fff;
	border-right: 1px solid #e4eaec;
	-webkit-transition: visibility .1s ease, top .3s ease, left .5s ease;
	     -o-transition: visibility .1s ease, top .3s ease, left .5s ease;
	        transition: visibility .1s ease, top .3s ease, left .5s ease;
}
.page-aside + .page-main {
	height: 100%;
	margin-left: 220px;
	overflow-y: auto;
}
.page-aside .list-group-item.active, .page-aside .list-group-item.active:focus, .page-aside .list-group-item.active:hover {
	z-index: 0;
}
.page-aside-inner {
	height: 100%;
}
.page-aside-section {
	position: relative;
}
.page-aside-section:first-child {
	padding-top: 11px;
}
.page-aside-section:last-child {
	margin-bottom: 11px;
}
.page-aside-section:after {
	position: relative;
	display: block;
	margin: 11px;
	content: "";
	border-bottom: 1px solid #e4eaec;
}
.page-aside-section:last-child:after {
	display: none;
}
.page-aside-switch {
	position: absolute;
	top: -webkit-calc(50% - 40px);
	top:         calc(50% - 40px);
	left: -webkit-calc(100% - 1px);
	left:         calc(100% - 1px);
	display: none;
	padding: 25px 16px 25px 4px;
	line-height: 1;
	cursor: pointer;
	background-color: white;
	border-radius: 0 100px 100px 0;
	-webkit-box-shadow: 1px 0 3px rgba(0, 0, 0, .2);
	        box-shadow: 1px 0 3px rgba(0, 0, 0, .2);
}
.page-aside-switch .wb-chevron-right {
	display: inline;
}
.page-aside-switch .wb-chevron-left {
	display: none;
}
.page-aside-title {
	padding: 10px 20px;
	overflow: hidden;
	font-weight: 400;
	color: #526069;
	text-overflow: ellipsis;
	white-space: nowrap;
	cursor: default;
}
.page-aside .list-group {
	margin-bottom: 22px;
}
.page-aside .list-group-item {
	padding: 13px 20px;
	margin-bottom: 1px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	border: none;
	border-radius: 0;
}
.page-aside .list-group-item .icon {
	color: #a3afb7;
}
.page-aside .list-group-item .item-right {
	float: right;
}
.page-aside .list-group-item:hover, .page-aside .list-group-item:focus {
	color: #f96868;
	background-color: #f3f7f9;
	border: none;
}
.page-aside .list-group-item:hover > .icon, .page-aside .list-group-item:focus > .icon {
	color: #f96868;
}
.page-aside .list-group-item.active {
	color: #f96868;
	background-color: transparent;
}
.page-aside .list-group-item.active > .icon {
	color: #f96868;
}
.page-aside .list-group-item.active:hover, .page-aside .list-group-item.active:focus {
	color: #f96868;
	background-color: #f3f7f9;
	border: none;
}
.page-aside .list-group-item.active:hover > .icon, .page-aside .list-group-item.active:focus > .icon {
	color: #f96868;
}
.page-aside .list-group.has-actions .list-group-item {
	padding-top: 6px;
	padding-bottom: 6px;
	line-height: 32px;
	cursor: pointer;
}
.page-aside .list-group.has-actions .list-group-item .list-editable {
	position: relative;
	display: none;
}
.page-aside .list-group.has-actions .list-group-item .list-editable .input-editable-close {
	position: absolute;
	top: 50%;
	right: 0;
	z-index: 1;
	-webkit-appearance: none;
	padding: 0;
	margin: 0;
	cursor: pointer;
	background: 0 0;
	border: 0;
	outline: none;
	-webkit-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	     -o-transform: translateY(-50%);
	        transform: translateY(-50%);
}
.page-aside .list-group.has-actions .list-group-item .list-editable .form-group {
	margin: 0;
}
.page-aside .list-group.has-actions .list-group-item .item-actions {
	position: absolute;
	top: 6px;
	right: 20px;
	display: none;
}
.page-aside .list-group.has-actions .list-group-item .item-actions .btn-icon {
	padding-right: 2px;
	padding-left: 2px;
	background-color: transparent;
}
.page-aside .list-group.has-actions .list-group-item .item-actions .btn-icon:hover .icon {
	color: #f96868;
}
.page-aside .list-group.has-actions .list-group-item .item-actions .icon {
	margin: 0;
}
.page-aside .list-group.has-actions .list-group-item:hover .item-right {
	display: none;
}
.page-aside .list-group.has-actions .list-group-item:hover .item-actions {
	display: block;
}
.page-aside .list-group.has-actions .list-group-item:hover .item-actions .icon {
	color: #76838f;
}
@media (max-width: 1199px) {
	.page-aside {
		width: 220px;
	}
	.page-aside + .page-main {
		margin-left: 220px;
	}
}
@media (max-width: 767px) {
	.page-aside {
		position: fixed;
		top: 100px;
		left: -220px;
		z-index: 900;
		width: 220px;
		height: -webkit-calc(100% - 140px);
		height:         calc(100% - 140px);
		overflow-y: visible;
		visibility: visible;
	}
	.site-navbar-collapse-show .page-aside {
		top: 120px;
		height: -webkit-calc(100% - 120px);
		height:         calc(100% - 120px);
	}
	.site-menubar-changing .page-aside, .site-menubar-open .page-aside {
		height: 100%;
	}
	.page-aside .page-aside-inner {
		background-color: white;
	}
	.page-aside.open {
		left: 0;
	}
	.site-menubar-changing .page-aside.open, .site-menubar-open .page-aside.open {
		visibility: hidden;
	}
	.page-aside.open .page-aside-switch .wb-chevron-right {
		display: none;
	}
	.page-aside.open .page-aside-switch .wb-chevron-left {
		display: inline;
	}
	.page-aside + .page-main {
		margin-left: 0;
	}
	.page-aside-switch {
		display: block;
	}
}
@media (max-width: 480px) {
	.page-aside {
		top: 60px;
		height: -webkit-calc(100% - 60px);
		height:         calc(100% - 60px);
	}
}
.site-action {
	position: fixed;
	right: 25px;
	bottom: 65px;
	z-index: 1290;
	z-index: 1030;
	-webkit-animation-duration: 3s;
	     -o-animation-duration: 3s;
	        animation-duration: 3s;
}
.site-action input {
	display: none;
}
.site-action .btn {
	-webkit-box-shadow: 0 0 10px 0 rgba(60, 60, 60, .1);
	        box-shadow: 0 0 10px 0 rgba(60, 60, 60, .1);
}
.site-action .front-icon {
	display: block;
}
.site-action .back-icon {
	display: none;
}
.site-action-buttons {
	position: absolute;
	bottom: 56px;
	left: 0;
	display: none;
	width: 100%;
	text-align: center;
}
.site-action-buttons .btn {
	display: block;
	margin: 0 auto;
	margin-bottom: 10px;
	-webkit-animation-delay: 100ms;
	     -o-animation-delay: 100ms;
	        animation-delay: 100ms;
}
.site-action.active .front-icon {
	display: none;
}
.site-action.active .back-icon {
	display: block;
}
.site-action.active .site-action-buttons {
	display: block;
}
@media (max-width: 767px) {
	.site-action .btn-floating {
		width: 46px;
		height: 46px;
		padding: 0;
		font-size: 16px;
		-webkit-box-shadow: 0 6px 6px 0 rgba(60, 60, 60, .1);
		        box-shadow: 0 6px 6px 0 rgba(60, 60, 60, .1);
	}
	.site-action-buttons {
		bottom: 46px;
	}
}
@media (max-width: 480px) {
	.site-action {
		bottom: 25px;
	}
}
.radio-custom, .checkbox-custom {
	position: relative;
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
}
.radio-custom label, .checkbox-custom label {
	min-height: 22px;
	margin-bottom: 0;
	font-weight: 400;
	cursor: pointer;
}
.radio-custom input[type="radio"], .checkbox-custom input[type="checkbox"] {
	position: absolute;
	margin-top: 0;
	margin-bottom: 0;
	margin-left: -20px;
}
.radio-custom.disabled label, .checkbox-custom.disabled label, fieldset[disabled] .radio-custom label, fieldset[disabled] .checkbox-custom label {
	cursor: not-allowed;
}
.input-group-addon .radio-custom, .input-group-addon .checkbox-custom {
	margin: 0;
}
.checkbox-custom {
	padding-left: 20px;
}
.checkbox-custom label {
	position: relative;
	display: inline-block;
	padding-left: 10px;
	vertical-align: middle;
}
.checkbox-custom label:empty {
	padding-left: 0;
}
.checkbox-custom label::before {
	position: absolute;
	left: 0;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin-left: -20px;
	content: "";
	background-color: #fff;
	border: 1px solid #e4eaec;
	border-radius: 3px;
	-webkit-transition: all .3s ease-in-out 0s;
	     -o-transition: all .3s ease-in-out 0s;
	        transition: all .3s ease-in-out 0s;
}
.checkbox-custom label::after {
	position: absolute;
	top: 0;
	left: 0;
	display: inline-block;
	width: 20px;
	height: 20px;
	padding-top: 1px;
	margin-left: -20px;
	font-size: 12px;
	line-height: 20px;
	color: #76838f;
	text-align: center;
}
.checkbox-custom input[type="checkbox"], .checkbox-custom input[type="radio"] {
	z-index: 1;
	width: 20px;
	height: 20px;
	opacity: 0;
}
.checkbox-custom input[type="checkbox"]:focus + label::before, .checkbox-custom input[type="radio"]:focus + label::before {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline: none;
	outline-offset: -2px;
}
.checkbox-custom input[type="checkbox"]:checked + label::before, .checkbox-custom input[type="radio"]:checked + label::before {
	border-color: #e4eaec;
	border-width: 10px;
	-webkit-transition: all 300ms ease-in-out 0s;
	     -o-transition: all 300ms ease-in-out 0s;
	        transition: all 300ms ease-in-out 0s;
}
.checkbox-custom input[type="checkbox"]:checked + label::after, .checkbox-custom input[type="radio"]:checked + label::after {
	font-family: "Web Icons";
	content: "\f192";
}
.checkbox-custom input[type="checkbox"]:disabled + label, .checkbox-custom input[type="radio"]:disabled + label {
	opacity: .65;
}
.checkbox-custom input[type="checkbox"]:disabled + label::before, .checkbox-custom input[type="radio"]:disabled + label::before {
	cursor: not-allowed;
	background-color: #f3f7f9;
	border-color: #e4eaec;
	border-width: 1px;
}
.checkbox-custom.checkbox-circle label::before {
	border-radius: 50%;
}
.checkbox-custom.checkbox-inline {
	display: inline-block;
	margin-top: 0;
	margin-bottom: 0;
}
.checkbox-inline + .checkbox-inline {
	margin-left: 20px;
}
.checkbox-default input[type="checkbox"]:checked + label::before, .checkbox-default input[type="radio"]:checked + label::before {
	background-color: #fff;
	border-color: #e4eaec;
	border-width: 1px;
}
.checkbox-default input[type="checkbox"]:checked + label::after, .checkbox-default input[type="radio"]:checked + label::after {
	color: #f96868;
}
.checkbox-primary input[type="checkbox"]:checked + label::before, .checkbox-primary input[type="radio"]:checked + label::before {
	background-color: #f96868;
	border-color: #f96868;
}
.checkbox-primary input[type="checkbox"]:checked + label::after, .checkbox-primary input[type="radio"]:checked + label::after {
	color: #fff;
}
.checkbox-danger input[type="checkbox"]:checked + label::before, .checkbox-danger input[type="radio"]:checked + label::before {
	background-color: #f96868;
	border-color: #f96868;
}
.checkbox-danger input[type="checkbox"]:checked + label::after, .checkbox-danger input[type="radio"]:checked + label::after {
	color: #fff;
}
.checkbox-info input[type="checkbox"]:checked + label::before, .checkbox-info input[type="radio"]:checked + label::before {
	background-color: #57c7d4;
	border-color: #57c7d4;
}
.checkbox-info input[type="checkbox"]:checked + label::after, .checkbox-info input[type="radio"]:checked + label::after {
	color: #fff;
}
.checkbox-warning input[type="checkbox"]:checked + label::before, .checkbox-warning input[type="radio"]:checked + label::before {
	background-color: #f2a654;
	border-color: #f2a654;
}
.checkbox-warning input[type="checkbox"]:checked + label::after, .checkbox-warning input[type="radio"]:checked + label::after {
	color: #fff;
}
.checkbox-success input[type="checkbox"]:checked + label::before, .checkbox-success input[type="radio"]:checked + label::before {
	background-color: #46be8a;
	border-color: #46be8a;
}
.checkbox-success input[type="checkbox"]:checked + label::after, .checkbox-success input[type="radio"]:checked + label::after {
	color: #fff;
}
.checkbox-sm {
	padding-left: 18px;
}
.checkbox-sm label {
	padding-left: 8px;
}
.checkbox-sm label:empty {
	padding-left: 0;
}
.checkbox-sm label::before, .checkbox-sm label::after {
	width: 18px;
	height: 18px;
	margin-left: -18px;
}
.checkbox-sm label::after {
	font-size: 10px;
	line-height: 18px;
}
.checkbox-sm input[type="checkbox"], .checkbox-sm input[type="radio"] {
	width: 18px;
	height: 18px;
}
.checkbox-sm input[type="checkbox"]:checked + label::before, .checkbox-sm input[type="radio"]:checked + label::before {
	border-width: 9px;
}
.checkbox-lg {
	padding-left: 24px;
}
.checkbox-lg label {
	padding-left: 12px;
}
.checkbox-lg label:empty {
	padding-left: 0;
}
.checkbox-lg label::before, .checkbox-lg label::after {
	width: 24px;
	height: 24px;
	margin-left: -24px;
}
.checkbox-lg label::after {
	font-size: 14px;
	line-height: 24px;
}
.checkbox-lg input[type="checkbox"], .checkbox-lg input[type="radio"] {
	width: 24px;
	height: 24px;
}
.checkbox-lg input[type="checkbox"]:checked + label::before, .checkbox-lg input[type="radio"]:checked + label::before {
	border-width: 12px;
}
.radio-custom {
	padding-left: 20px;
}
.radio-custom label {
	position: relative;
	display: inline-block;
	padding-left: 10px;
	vertical-align: middle;
}
.radio-custom label:empty {
	padding-left: 0;
}
.radio-custom label::before {
	position: absolute;
	left: 0;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin-left: -20px;
	content: "";
	background-color: #fff;
	border: 1px solid #e4eaec;
	border-radius: 50%;
	-webkit-transition: border 300ms ease-in-out 0s, color 300ms ease-in-out 0s;
	     -o-transition: border 300ms ease-in-out 0s, color 300ms ease-in-out 0s;
	        transition: border 300ms ease-in-out 0s, color 300ms ease-in-out 0s;
}
.radio-custom label::after {
	position: absolute;
	top: 7px;
	left: 7px;
	display: inline-block;
	width: 6px;
	height: 6px;
	margin-left: -20px;
	content: " ";
	background-color: transparent;
	border: 2px solid #76838f;
	border-radius: 50%;
	-webkit-transform: scale(0, 0);
	    -ms-transform: scale(0, 0);
	     -o-transform: scale(0, 0);
	        transform: scale(0, 0);

	transition-transform: .1s cubic-bezier(.8, -.33, .2, 1.33);
}
.radio-custom input[type="radio"] {
	z-index: 1;
	width: 20px;
	height: 20px;
	opacity: 0;
}
.radio-custom input[type="radio"]:focus + label::before {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline: none;
	outline-offset: -2px;
}
.radio-custom input[type="radio"]:checked + label::before {
	border-color: #e4eaec;
	border-width: 10px;
}
.radio-custom input[type="radio"]:checked + label::after {
	-webkit-transform: scale(1, 1);
	    -ms-transform: scale(1, 1);
	     -o-transform: scale(1, 1);
	        transform: scale(1, 1);
}
.radio-custom input[type="radio"]:disabled + label {
	opacity: .65;
}
.radio-custom input[type="radio"]:disabled + label::before {
	cursor: not-allowed;
}
.radio-custom.radio-inline {
	display: inline-block;
	margin-top: 0;
	margin-bottom: 0;
}
.radio-inline + .radio-inline {
	margin-left: 20px;
}
.radio-default input[type="radio"]:checked + label::before {
	background-color: #fff;
	border-color: #e4eaec;
	border-width: 1px;
}
.radio-default input[type="radio"]:checked + label::after {
	border-color: #f96868;
}
.radio-primary input[type="radio"]:checked + label::before {
	border-color: #f96868;
}
.radio-primary input[type="radio"]:checked + label::after {
	border-color: #fff;
}
.radio-danger input[type="radio"]:checked + label::before {
	border-color: #f96868;
}
.radio-danger input[type="radio"]:checked + label::after {
	border-color: #fff;
}
.radio-info input[type="radio"]:checked + label::before {
	border-color: #57c7d4;
}
.radio-info input[type="radio"]:checked + label::after {
	border-color: #fff;
}
.radio-warning input[type="radio"]:checked + label::before {
	border-color: #f2a654;
}
.radio-warning input[type="radio"]:checked + label::after {
	border-color: #fff;
}
.radio-success input[type="radio"]:checked + label::before {
	border-color: #46be8a;
}
.radio-success input[type="radio"]:checked + label::after {
	border-color: #fff;
}
.radio-sm {
	padding-left: 18px;
}
.radio-sm label {
	padding-left: 8px;
}
.radio-sm label:empty {
	padding-left: 0;
}
.radio-sm label::before {
	width: 18px;
	height: 18px;
	margin-left: -20px;
}
.radio-sm label::after {
	top: 7px;
	left: 7px;
	width: 4px;
	height: 4px;
	margin-left: -20px;
	border-width: 2px;
}
.radio-sm input[type="radio"] {
	width: 18px;
	height: 18px;
}
.radio-sm input[type="radio"]:checked + label::before {
	border-width: 9px;
}
.radio-lg {
	padding-left: 24px;
}
.radio-lg label {
	padding-left: 12px;
}
.radio-lg label:empty {
	padding-left: 0;
}
.radio-lg label::before {
	width: 24px;
	height: 24px;
	margin-left: -20px;
}
.radio-lg label::after {
	top: 8px;
	left: 8px;
	width: 8px;
	height: 8px;
	margin-left: -20px;
	border-width: 2px;
}
.radio-lg input[type="radio"] {
	width: 24px;
	height: 24px;
}
.radio-lg input[type="radio"]:checked + label::before {
	border-width: 12px;
}
@media (min-width: 768px) {
	.form-inline .radio-custom, .form-inline .checkbox-custom {
		display: inline-block;
		margin-top: 0;
		margin-bottom: 0;
		vertical-align: middle;
	}
	.form-inline .radio-custom label, .form-inline .checkbox-custom label {
		padding-left: 0;
	}
	.form-inline .radio-custom input[type="radio"], .form-inline .checkbox-custom input[type="checkbox"] {
		position: relative;
		margin-left: 0;
	}
	.form-inline .radio-custom label {
		padding-left: 10px;
	}
	.form-inline .checkbox-custom label {
		padding-left: 10px;
	}
	.form-inline .checkbox-custom input[type="checkbox"] {
		position: absolute;
		margin-left: -20px;
	}
	.form-inline .radio-custom input[type="radio"] {
		position: absolute;
		margin-left: -20px;
	}
}
.form-horizontal .radio-custom, .form-horizontal .checkbox-custom {
	padding-top: 5px;
	margin-top: 0;
	margin-bottom: 0;
}
.form-horizontal .radio-custom, .form-horizontal .checkbox-custom {
	min-height: 27px;
}
.form-horizontal .input-group-addon .radio-custom, .form-horizontal .input-group-addon .checkbox-custom {
	padding-top: 0;
}
.form-horizontal .input-group-addon .radio-custom, .form-horizontal .input-group-addon .checkbox-custom {
	min-height: 22px;
}
.form-material {
	position: relative;
}
.form-material.floating {
	margin-top: 20px;
	margin-bottom: 20px;
}
.form-material.floating + .form-material.floating {
	margin-top: 40px;
}
.form-material .form-control {
	padding: 0;
	background-color: transparent;
	background-color: rgba(0, 0, 0, 0);
	background-repeat: no-repeat;
	background-position: center bottom, center -webkit-calc(100% - 1px);
	background-position: center bottom, center calc(100% - 1px);
	-webkit-background-size: 0 2px, 100% 1px;
	        background-size: 0 2px, 100% 1px;
	-webkit-transition: background 0s ease-out;
	     -o-transition: background 0s ease-out;
	        transition: background 0s ease-out;
}
.form-material .form-control, .form-material .form-control:focus, .form-material .form-control.focus {
	float: none;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f96868), to(#f96868)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f96868, #f96868), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#f96868, #f96868), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f96868, #f96868), linear-gradient(#e4eaec, #e4eaec);
	border: 0;
	border-radius: 0;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.no-cssgradients .form-material .form-control {
	border-bottom: 2px solid #e4eaec;
}
.form-material .form-control::-webkit-input-placeholder {
	color: #a3afb7;
}
.form-material .form-control::-moz-placeholder {
	color: #a3afb7;
}
.form-material .form-control:-ms-input-placeholder {
	color: #a3afb7;
}
.form-material .form-control:disabled::-webkit-input-placeholder {
	color: #ccd5db;
}
.form-material .form-control:disabled::-moz-placeholder {
	color: #ccd5db;
}
.form-material .form-control:disabled:-ms-input-placeholder {
	color: #ccd5db;
}
.form-material .form-control:focus, .form-material .form-control.focus {
	-webkit-background-size: 100% 2px, 100% 1px;
	        background-size: 100% 2px, 100% 1px;
	outline: none;
	-webkit-transition-duration: .3s;
	     -o-transition-duration: .3s;
	        transition-duration: .3s;
}
.no-cssgradients .form-material .form-control:focus, .no-cssgradients .form-material .form-control.focus {
	background: transparent;
	border-bottom: 2px solid #f96868;
}
.form-material .form-control:disabled, .form-material .form-control[disabled], fieldset[disabled] .form-material .form-control {
	background: transparent;
	background: rgba(0, 0, 0, 0);
	border-bottom: 1px dashed #ccd5db;
}
.form-material .form-control:disabled ~ .floating-label, .form-material .form-control[disabled] ~ .floating-label, fieldset[disabled] .form-material .form-control ~ .floating-label {
	color: #ccd5db;
}
.form-material .control-label {
	margin-bottom: 0;
}
.form-material .floating-label {
	position: absolute;
	left: 0;
	font-size: 14px;
	color: #76838f;
	pointer-events: none;
	-webkit-transition: .3s ease all;
	     -o-transition: .3s ease all;
	        transition: .3s ease all;
}
.form-material .floating-label.floating-label-static {
	position: relative;
	top: auto;
	display: block;
}
.form-material [class*="col-"] > .floating-label {
	left: 12px;
}
.form-material .form-control ~ .floating-label {
	top: 8.4px;
	font-size: 14px;
}
.form-material .form-control:focus ~ .floating-label, .form-material .form-control.focus ~ .floating-label, .form-material .form-control:not(.empty) ~ .floating-label {
	top: -11.2px;
	font-size: 11.2px;
}
.form-material .form-control:-webkit-autofill ~ .floating-label {
	top: -11.2px;
	font-size: 11.2px;
}
.form-material .form-control.input-sm ~ .floating-label {
	top: 6px;
	font-size: 12px;
}
.form-material .form-control.input-sm:focus ~ .floating-label, .form-material .form-control.input-sm.focus ~ .floating-label, .form-material .form-control.input-sm:not(.empty) ~ .floating-label {
	top: -9.6px;
	font-size: 9.6px;
}
.form-material .form-control.input-sm:-webkit-autofill ~ .floating-label {
	top: -9.6px;
	font-size: 9.6px;
}
.form-material .form-control.input-lg ~ .floating-label {
	top: 5.9999994px;
	font-size: 18px;
}
.form-material .form-control.input-lg:focus ~ .floating-label, .form-material .form-control.input-lg.focus ~ .floating-label, .form-material .form-control.input-lg:not(.empty) ~ .floating-label {
	top: -14.4px;
	font-size: 14.4px;
}
.form-material .form-control.input-lg:-webkit-autofill ~ .floating-label {
	top: -14.4px;
	font-size: 14.4px;
}
.form-material textarea.form-control {
	padding-bottom: 4px;
	resize: none;
}
.form-material.floating textarea.form-control {
	padding-top: 4px;
}
.form-material select.form-control {
	border: 0;
	border-radius: 0;
}
.form-material:not(.floating) .control-label + select[multiple] {
	margin-top: 5px;
}
.form-material .hint {
	position: absolute;
	display: none;
	font-size: 80%;
}
.form-material .form-control:focus ~ .hint, .form-material .form-control.focus ~ .hint {
	display: block;
}
.form-material .form-control:not(.empty):invalid ~ .floating-label, .form-material .form-control.focus:invalid ~ .floating-label {
	color: #f96868;
}
.form-material .form-control:invalid {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f96868), to(#f96868)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f96868, #f96868), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#f96868, #f96868), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f96868, #f96868), linear-gradient(#e4eaec, #e4eaec);
}
.form-material.form-group.has-warning .form-control:focus, .form-material.form-group.has-warning .form-control.focus, .form-material.form-group.has-warning .form-control:not(.empty) {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f2a654), to(#f2a654)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f2a654, #f2a654), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#f2a654, #f2a654), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f2a654, #f2a654), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-warning .form-control:focus, .no-cssgradients .form-material.form-group.has-warning .form-control.focus, .no-cssgradients .form-material.form-group.has-warning .form-control:not(.empty) {
	background: transparent;
	border-bottom: 2px solid #f2a654;
}
.form-material.form-group.has-warning .form-control:-webkit-autofill {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f2a654), to(#f2a654)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f2a654, #f2a654), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f2a654, #f2a654), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-warning .form-control:-webkit-autofill {
	background: transparent;
	border-bottom: 2px solid #f2a654;
}
.form-material.form-group.has-warning .form-control:not(.empty) {
	-webkit-background-size: 100% 2px, 100% 1px;
	        background-size: 100% 2px, 100% 1px;
}
.form-material.form-group.has-warning .control-label {
	color: #f2a654;
}
.form-material.form-group.has-warning .form-control:focus ~ .floating-label, .form-material.form-group.has-warning .form-control.focus ~ .floating-label, .form-material.form-group.has-warning .form-control:not(.empty) ~ .floating-label {
	color: #f2a654;
}
.form-material.form-group.has-warning .form-control:-webkit-autofill ~ .floating-label {
	color: #f2a654;
}
.form-material.form-group.has-error .form-control:focus, .form-material.form-group.has-error .form-control.focus, .form-material.form-group.has-error .form-control:not(.empty) {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f96868), to(#f96868)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f96868, #f96868), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#f96868, #f96868), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f96868, #f96868), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-error .form-control:focus, .no-cssgradients .form-material.form-group.has-error .form-control.focus, .no-cssgradients .form-material.form-group.has-error .form-control:not(.empty) {
	background: transparent;
	border-bottom: 2px solid #f96868;
}
.form-material.form-group.has-error .form-control:-webkit-autofill {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f96868), to(#f96868)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#f96868, #f96868), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#f96868, #f96868), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-error .form-control:-webkit-autofill {
	background: transparent;
	border-bottom: 2px solid #f96868;
}
.form-material.form-group.has-error .form-control:not(.empty) {
	-webkit-background-size: 100% 2px, 100% 1px;
	        background-size: 100% 2px, 100% 1px;
}
.form-material.form-group.has-error .control-label {
	color: #f96868;
}
.form-material.form-group.has-error .form-control:focus ~ .floating-label, .form-material.form-group.has-error .form-control.focus ~ .floating-label, .form-material.form-group.has-error .form-control:not(.empty) ~ .floating-label {
	color: #f96868;
}
.form-material.form-group.has-error .form-control:-webkit-autofill ~ .floating-label {
	color: #f96868;
}
.form-material.form-group.has-success .form-control:focus, .form-material.form-group.has-success .form-control.focus, .form-material.form-group.has-success .form-control:not(.empty) {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#46be8a), to(#46be8a)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#46be8a, #46be8a), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#46be8a, #46be8a), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#46be8a, #46be8a), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-success .form-control:focus, .no-cssgradients .form-material.form-group.has-success .form-control.focus, .no-cssgradients .form-material.form-group.has-success .form-control:not(.empty) {
	background: transparent;
	border-bottom: 2px solid #46be8a;
}
.form-material.form-group.has-success .form-control:-webkit-autofill {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#46be8a), to(#46be8a)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#46be8a, #46be8a), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#46be8a, #46be8a), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-success .form-control:-webkit-autofill {
	background: transparent;
	border-bottom: 2px solid #46be8a;
}
.form-material.form-group.has-success .form-control:not(.empty) {
	-webkit-background-size: 100% 2px, 100% 1px;
	        background-size: 100% 2px, 100% 1px;
}
.form-material.form-group.has-success .control-label {
	color: #46be8a;
}
.form-material.form-group.has-success .form-control:focus ~ .floating-label, .form-material.form-group.has-success .form-control.focus ~ .floating-label, .form-material.form-group.has-success .form-control:not(.empty) ~ .floating-label {
	color: #46be8a;
}
.form-material.form-group.has-success .form-control:-webkit-autofill ~ .floating-label {
	color: #46be8a;
}
.form-material.form-group.has-info .form-control:focus, .form-material.form-group.has-info .form-control.focus, .form-material.form-group.has-info .form-control:not(.empty) {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#57c7d4), to(#57c7d4)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#57c7d4, #57c7d4), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:      -o-linear-gradient(#57c7d4, #57c7d4), -o-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#57c7d4, #57c7d4), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-info .form-control:focus, .no-cssgradients .form-material.form-group.has-info .form-control.focus, .no-cssgradients .form-material.form-group.has-info .form-control:not(.empty) {
	background: transparent;
	border-bottom: 2px solid #57c7d4;
}
.form-material.form-group.has-info .form-control:-webkit-autofill {
	background-image: -webkit-gradient(linear, left top, left bottom, from(#57c7d4), to(#57c7d4)), -webkit-gradient(linear, left top, left bottom, from(#e4eaec), to(#e4eaec));
	background-image: -webkit-linear-gradient(#57c7d4, #57c7d4), -webkit-linear-gradient(#e4eaec, #e4eaec);
	background-image:         linear-gradient(#57c7d4, #57c7d4), linear-gradient(#e4eaec, #e4eaec);
}
.no-cssgradients .form-material.form-group.has-info .form-control:-webkit-autofill {
	background: transparent;
	border-bottom: 2px solid #57c7d4;
}
.form-material.form-group.has-info .form-control:not(.empty) {
	-webkit-background-size: 100% 2px, 100% 1px;
	        background-size: 100% 2px, 100% 1px;
}
.form-material.form-group.has-info .control-label {
	color: #57c7d4;
}
.form-material.form-group.has-info .form-control:focus ~ .floating-label, .form-material.form-group.has-info .form-control.focus ~ .floating-label, .form-material.form-group.has-info .form-control:not(.empty) ~ .floating-label {
	color: #57c7d4;
}
.form-material.form-group.has-info .form-control:-webkit-autofill ~ .floating-label {
	color: #57c7d4;
}
.form-material .input-group .form-control-wrap {
	margin-right: 5px;
	margin-left: 5px;
}
.form-material .input-group .form-control-wrap .form-control {
	float: none;
}
.form-material .input-group .input-group-addon {
	background: transparent;
	border: 0;
}
.form-material .input-group .input-group-btn .btn {
	margin: 0;
	border-radius: 4px;
}
.form-material input[type=file] {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 100;
	width: 100%;
	height: 100%;
	opacity: 0;
}
.form-control-wrap {
	position: relative;
}
.loader {
	position: relative;
	display: inline-block;
	margin: 0 auto;
	font-size: 30px;
	text-indent: -9999em;
	-webkit-transform: translateZ(0);
	    -ms-transform: translateZ(0);
	        transform: translateZ(0);
}
.loader.loader-default {
	width: 1em;
	height: 1em;
	background-color: #a3afb7;
	border-radius: 100%;
	-webkit-animation: loader-default 1s infinite ease-in-out;
	     -o-animation: loader-default 1s infinite ease-in-out;
	        animation: loader-default 1s infinite ease-in-out;
}
.loader.loader-grill {
	width: .25em;
	height: .5em;
	background: #a3afb7;
	-webkit-animation: default-grill 1s infinite ease-in-out;
	     -o-animation: default-grill 1s infinite ease-in-out;
	        animation: default-grill 1s infinite ease-in-out;
	-webkit-animation-delay: -.16s;
	     -o-animation-delay: -.16s;
	        animation-delay: -.16s;
}
.loader.loader-grill:before, .loader.loader-grill:after {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	content: "";
	background: #a3afb7;
	-webkit-animation: default-grill 1s infinite ease-in-out;
	     -o-animation: default-grill 1s infinite ease-in-out;
	        animation: default-grill 1s infinite ease-in-out;
}
.loader.loader-grill:before {
	left: -.375em;
	-webkit-animation-delay: -.32s;
	     -o-animation-delay: -.32s;
	        animation-delay: -.32s;
}
.loader.loader-grill:after {
	left: .375em;
}
.loader.loader-circle {
	width: 1em;
	height: 1em;
	border-top: .125em solid rgba(163, 175, 183, .5);
	border-right: .125em solid rgba(163, 175, 183, .5);
	border-bottom: .125em solid rgba(163, 175, 183, .5);
	border-left: .125em solid #a3afb7;
	border-radius: 50%;
	-webkit-animation: loader-circle 1.1s infinite linear;
	     -o-animation: loader-circle 1.1s infinite linear;
	        animation: loader-circle 1.1s infinite linear;
}
.loader.loader-tadpole {
	width: 1em;
	height: 1em;
	border-radius: 50%;
	-webkit-animation: loader-tadpole 1.7s infinite ease;
	     -o-animation: loader-tadpole 1.7s infinite ease;
	        animation: loader-tadpole 1.7s infinite ease;
}
.loader.loader-ellipsis {
	top: -.625em;
	width: .625em;
	height: .625em;
	border-radius: 50%;
	-webkit-animation: loader-ellipsis 1.8s infinite ease-in-out;
	     -o-animation: loader-ellipsis 1.8s infinite ease-in-out;
	        animation: loader-ellipsis 1.8s infinite ease-in-out;
	-webkit-animation-delay: -.16s;
	     -o-animation-delay: -.16s;
	        animation-delay: -.16s;

	-webkit-animation-fill-mode: both;
	     -o-animation-fill-mode: both;
	        animation-fill-mode: both;
}
.loader.loader-ellipsis:before, .loader.loader-ellipsis:after {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	content: "";
	border-radius: 50%;
	-webkit-animation: loader-ellipsis 1.8s infinite ease-in-out;
	     -o-animation: loader-ellipsis 1.8s infinite ease-in-out;
	        animation: loader-ellipsis 1.8s infinite ease-in-out;

	-webkit-animation-fill-mode: both;
	     -o-animation-fill-mode: both;
	        animation-fill-mode: both;
}
.loader.loader-ellipsis:before {
	left: -.875em;
	-webkit-animation-delay: -.32s;
	     -o-animation-delay: -.32s;
	        animation-delay: -.32s;
}
.loader.loader-ellipsis:after {
	left: .875em;
}
.loader.loader-dot {
	width: 2em;
	height: 2em;
	-webkit-animation: loader-dot-rotate 2s infinite linear;
	     -o-animation: loader-dot-rotate 2s infinite linear;
	        animation: loader-dot-rotate 2s infinite linear;
}
.loader.loader-dot:before, .loader.loader-dot:after {
	position: absolute;
	top: 0;
	left: 0;
	width: 60%;
	height: 60%;
	content: "";
	background: #a3afb7;
	border-radius: 100%;
	-webkit-animation: loader-dot-bounce 2s infinite ease-in-out;
	     -o-animation: loader-dot-bounce 2s infinite ease-in-out;
	        animation: loader-dot-bounce 2s infinite ease-in-out;
}
.loader.loader-dot:after {
	top: auto;
	bottom: 0;
	-webkit-animation-delay: -1s;
	     -o-animation-delay: -1s;
	        animation-delay: -1s;
}
.loader.loader-bounce {
	width: 1.5em;
	height: 1.5em;
}
.loader.loader-bounce:before, .loader.loader-bounce:after {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	content: "";
	background: #a3afb7;
	border-radius: 50%;
	opacity: .6;
	-webkit-animation: loader-bounce 2s infinite ease-in-out;
	     -o-animation: loader-bounce 2s infinite ease-in-out;
	        animation: loader-bounce 2s infinite ease-in-out;
}
.loader.loader-bounce:after {
	-webkit-animation-delay: -1s;
	     -o-animation-delay: -1s;
	        animation-delay: -1s;
}
.side-panel-loading, body > .loader {
	position: fixed;
	top: 50%;
	left: 50%;
	margin-top: -20px;
}
.site-menubar-unfold > .loader {
	margin-left: 130px;
}
.site-menubar-fold > .loader {
	margin-left: 45px;
}
.site-menubar-hide.site-menubar-unfold > .loader {
	margin-left: 0;
}
@-webkit-keyframes loader-default {
	0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
	}
	100% {
		opacity: 0;
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes loader-default {
	0% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	100% {
		opacity: 0;
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@keyframes loader-default {
	0% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	100% {
		opacity: 0;
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@-webkit-keyframes default-grill {
	0%, 80%, 100% {
		height: 1em;
		-webkit-box-shadow: 0 0 #a3afb7;
		        box-shadow: 0 0 #a3afb7;
	}
	40% {
		height: 1.2em;
		-webkit-box-shadow: 0 -.25em #a3afb7;
		        box-shadow: 0 -.25em #a3afb7;
	}
}
@-o-keyframes default-grill {
	0%, 80%, 100% {
		height: 1em;
		box-shadow: 0 0 #a3afb7;
	}
	40% {
		height: 1.2em;
		box-shadow: 0 -.25em #a3afb7;
	}
}
@keyframes default-grill {
	0%, 80%, 100% {
		height: 1em;
		-webkit-box-shadow: 0 0 #a3afb7;
		        box-shadow: 0 0 #a3afb7;
	}
	40% {
		height: 1.2em;
		-webkit-box-shadow: 0 -.25em #a3afb7;
		        box-shadow: 0 -.25em #a3afb7;
	}
}
@-webkit-keyframes loader-circle {
	0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-o-keyframes loader-circle {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@keyframes loader-circle {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-webkit-keyframes loader-tadpole {
	0% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	5%, 95% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
	}
	10%, 59% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.087em -.825em 0 -.42em #a3afb7, -.173em -.812em 0 -.44em #a3afb7, -.256em -.789em 0 -.46em #a3afb7, -.297em -.775em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.087em -.825em 0 -.42em #a3afb7, -.173em -.812em 0 -.44em #a3afb7, -.256em -.789em 0 -.46em #a3afb7, -.297em -.775em 0 -.477em #a3afb7;
	}
	20% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.338em -.758em 0 -.42em #a3afb7, -.555em -.617em 0 -.44em #a3afb7, -.671em -.488em 0 -.46em #a3afb7, -.749em -.34em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.338em -.758em 0 -.42em #a3afb7, -.555em -.617em 0 -.44em #a3afb7, -.671em -.488em 0 -.46em #a3afb7, -.749em -.34em 0 -.477em #a3afb7;
	}
	38% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.377em -.74em 0 -.42em #a3afb7, -.645em -.522em 0 -.44em #a3afb7, -.775em -.297em 0 -.46em #a3afb7, -.82em -.09em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.377em -.74em 0 -.42em #a3afb7, -.645em -.522em 0 -.44em #a3afb7, -.775em -.297em 0 -.46em #a3afb7, -.82em -.09em 0 -.477em #a3afb7;
	}
	100% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-o-keyframes loader-tadpole {
	0% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	5%, 95% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
	}
	10%, 59% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, -.087em -.825em 0 -.42em #a3afb7, -.173em -.812em 0 -.44em #a3afb7, -.256em -.789em 0 -.46em #a3afb7, -.297em -.775em 0 -.477em #a3afb7;
	}
	20% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, -.338em -.758em 0 -.42em #a3afb7, -.555em -.617em 0 -.44em #a3afb7, -.671em -.488em 0 -.46em #a3afb7, -.749em -.34em 0 -.477em #a3afb7;
	}
	38% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, -.377em -.74em 0 -.42em #a3afb7, -.645em -.522em 0 -.44em #a3afb7, -.775em -.297em 0 -.46em #a3afb7, -.82em -.09em 0 -.477em #a3afb7;
	}
	100% {
		box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@keyframes loader-tadpole {
	0% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	5%, 95% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
	}
	10%, 59% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.087em -.825em 0 -.42em #a3afb7, -.173em -.812em 0 -.44em #a3afb7, -.256em -.789em 0 -.46em #a3afb7, -.297em -.775em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.087em -.825em 0 -.42em #a3afb7, -.173em -.812em 0 -.44em #a3afb7, -.256em -.789em 0 -.46em #a3afb7, -.297em -.775em 0 -.477em #a3afb7;
	}
	20% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.338em -.758em 0 -.42em #a3afb7, -.555em -.617em 0 -.44em #a3afb7, -.671em -.488em 0 -.46em #a3afb7, -.749em -.34em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.338em -.758em 0 -.42em #a3afb7, -.555em -.617em 0 -.44em #a3afb7, -.671em -.488em 0 -.46em #a3afb7, -.749em -.34em 0 -.477em #a3afb7;
	}
	38% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, -.377em -.74em 0 -.42em #a3afb7, -.645em -.522em 0 -.44em #a3afb7, -.775em -.297em 0 -.46em #a3afb7, -.82em -.09em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, -.377em -.74em 0 -.42em #a3afb7, -.645em -.522em 0 -.44em #a3afb7, -.775em -.297em 0 -.46em #a3afb7, -.82em -.09em 0 -.477em #a3afb7;
	}
	100% {
		-webkit-box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		        box-shadow: 0 -.83em 0 -.4em #a3afb7, 0 -.83em 0 -.42em #a3afb7, 0 -.83em 0 -.44em #a3afb7, 0 -.83em 0 -.46em #a3afb7, 0 -.83em 0 -.477em #a3afb7;
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-webkit-keyframes loader-ellipsis {
	0%, 80%, 100% {
		-webkit-box-shadow: 0 .625em 0 -.325em #a3afb7;
		        box-shadow: 0 .625em 0 -.325em #a3afb7;
	}
	40% {
		-webkit-box-shadow: 0 .625em 0 0 #a3afb7;
		        box-shadow: 0 .625em 0 0 #a3afb7;
	}
}
@-o-keyframes loader-ellipsis {
	0%, 80%, 100% {
		box-shadow: 0 .625em 0 -.325em #a3afb7;
	}
	40% {
		box-shadow: 0 .625em 0 0 #a3afb7;
	}
}
@keyframes loader-ellipsis {
	0%, 80%, 100% {
		-webkit-box-shadow: 0 .625em 0 -.325em #a3afb7;
		        box-shadow: 0 .625em 0 -.325em #a3afb7;
	}
	40% {
		-webkit-box-shadow: 0 .625em 0 0 #a3afb7;
		        box-shadow: 0 .625em 0 0 #a3afb7;
	}
}
@-webkit-keyframes loader-dot-rotate {
	0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-o-keyframes loader-dot-rotate {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@keyframes loader-dot-rotate {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		     -o-transform: rotate(360deg);
		        transform: rotate(360deg);
	}
}
@-webkit-keyframes loader-dot-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes loader-dot-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@keyframes loader-dot-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@-webkit-keyframes loader-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes loader-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@keyframes loader-bounce {
	0%, 100% {
		-webkit-transform: scale(0);
		     -o-transform: scale(0);
		        transform: scale(0);
	}
	50% {
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
[class*=animation-] {
	-webkit-animation-duration: .5s;
	     -o-animation-duration: .5s;
	        animation-duration: .5s;
	-webkit-animation-timing-function: ease-out;
	     -o-animation-timing-function: ease-out;
	        animation-timing-function: ease-out;
}
.animation-hover:not(:hover), .animation-hover:not(:hover) [class*=animation-], .touch .animation-hover:not(.hover), .touch .animation-hover:not(.hover) [class*=animation-] {
	-webkit-animation-name: none;
	     -o-animation-name: none;
	        animation-name: none;
}
.animation-reverse {
	-webkit-animation-timing-function: ease-in;
	     -o-animation-timing-function: ease-in;
	        animation-timing-function: ease-in;
	animation-direction: reverse;
}
.animation-repeat {
	-webkit-animation-iteration-count: infinite;
	     -o-animation-iteration-count: infinite;
	        animation-iteration-count: infinite;
}
.animation-fade {
	-webkit-animation-name: fade;
	     -o-animation-name: fade;
	        animation-name: fade;
	-webkit-animation-duration: .8s;
	     -o-animation-duration: .8s;
	        animation-duration: .8s;
	-webkit-animation-timing-function: linear;
	     -o-animation-timing-function: linear;
	        animation-timing-function: linear;
}
.animation-scale {
	-webkit-animation-name: scale-12;
	     -o-animation-name: scale-12;
	        animation-name: scale-12;
}
.animation-scale-up {
	-webkit-animation-name: fade-scale-02;
	     -o-animation-name: fade-scale-02;
	        animation-name: fade-scale-02;
}
.animation-scale-down {
	-webkit-animation-name: fade-scale-18;
	     -o-animation-name: fade-scale-18;
	        animation-name: fade-scale-18;
}
.animation-slide-top {
	-webkit-animation-name: slide-top;
	     -o-animation-name: slide-top;
	        animation-name: slide-top;
}
.animation-slide-bottom {
	-webkit-animation-name: slide-bottom;
	     -o-animation-name: slide-bottom;
	        animation-name: slide-bottom;
}
.animation-slide-left {
	-webkit-animation-name: slide-left;
	     -o-animation-name: slide-left;
	        animation-name: slide-left;
}
.animation-slide-right {
	-webkit-animation-name: slide-right;
	     -o-animation-name: slide-right;
	        animation-name: slide-right;
}
.animation-shake {
	-webkit-animation-name: shake;
	     -o-animation-name: shake;
	        animation-name: shake;
}
.animation-duration-10 {
	-webkit-animation-duration: 15s;
	     -o-animation-duration: 15s;
	        animation-duration: 15s;
}
.animation-duration-9 {
	-webkit-animation-duration: 9s;
	     -o-animation-duration: 9s;
	        animation-duration: 9s;
}
.animation-duration-8 {
	-webkit-animation-duration: 8s;
	     -o-animation-duration: 8s;
	        animation-duration: 8s;
}
.animation-duration-7 {
	-webkit-animation-duration: 7s;
	     -o-animation-duration: 7s;
	        animation-duration: 7s;
}
.animation-duration-6 {
	-webkit-animation-duration: 6s;
	     -o-animation-duration: 6s;
	        animation-duration: 6s;
}
.animation-duration-5 {
	-webkit-animation-duration: 5s;
	     -o-animation-duration: 5s;
	        animation-duration: 5s;
}
.animation-duration-4 {
	-webkit-animation-duration: 4s;
	     -o-animation-duration: 4s;
	        animation-duration: 4s;
}
.animation-duration-3 {
	-webkit-animation-duration: 3s;
	     -o-animation-duration: 3s;
	        animation-duration: 3s;
}
.animation-duration-2 {
	-webkit-animation-duration: 2s;
	     -o-animation-duration: 2s;
	        animation-duration: 2s;
}
.animation-duration-1 {
	-webkit-animation-duration: 1s;
	     -o-animation-duration: 1s;
	        animation-duration: 1s;
}
.animation-delay-100 {
	-webkit-animation-duration: 100ms;
	     -o-animation-duration: 100ms;
	        animation-duration: 100ms;
}
.animation-duration-250 {
	-webkit-animation-duration: 250ms;
	     -o-animation-duration: 250ms;
	        animation-duration: 250ms;
}
.animation-duration-300 {
	-webkit-animation-duration: 300ms;
	     -o-animation-duration: 300ms;
	        animation-duration: 300ms;
}
.animation-duration-500 {
	-webkit-animation-duration: 500ms;
	     -o-animation-duration: 500ms;
	        animation-duration: 500ms;
}
.animation-duration-750 {
	-webkit-animation-duration: 750ms;
	     -o-animation-duration: 750ms;
	        animation-duration: 750ms;
}
.animation-delay-1000 {
	-webkit-animation-delay: 1s;
	     -o-animation-delay: 1s;
	        animation-delay: 1s;
}
.animation-delay-900 {
	-webkit-animation-delay: 900ms;
	     -o-animation-delay: 900ms;
	        animation-delay: 900ms;
}
.animation-delay-800 {
	-webkit-animation-delay: 800ms;
	     -o-animation-delay: 800ms;
	        animation-delay: 800ms;
}
.animation-delay-700 {
	-webkit-animation-delay: 700ms;
	     -o-animation-delay: 700ms;
	        animation-delay: 700ms;
}
.animation-delay-600 {
	-webkit-animation-delay: 600ms;
	     -o-animation-delay: 600ms;
	        animation-delay: 600ms;
}
.animation-delay-500 {
	-webkit-animation-delay: 500ms;
	     -o-animation-delay: 500ms;
	        animation-delay: 500ms;
}
.animation-delay-400 {
	-webkit-animation-delay: 400ms;
	     -o-animation-delay: 400ms;
	        animation-delay: 400ms;
}
.animation-delay-300 {
	-webkit-animation-delay: 300ms;
	     -o-animation-delay: 300ms;
	        animation-delay: 300ms;
}
.animation-delay-200 {
	-webkit-animation-delay: 200ms;
	     -o-animation-delay: 200ms;
	        animation-delay: 200ms;
}
.animation-delay-100 {
	-webkit-animation-delay: 100ms;
	     -o-animation-delay: 100ms;
	        animation-delay: 100ms;
}
.animation-top-left {
	-webkit-transform-origin: 0 0;
	    -ms-transform-origin: 0 0;
	     -o-transform-origin: 0 0;
	        transform-origin: 0 0;
}
.animation-top-center {
	-webkit-transform-origin: 50% 0;
	    -ms-transform-origin: 50% 0;
	     -o-transform-origin: 50% 0;
	        transform-origin: 50% 0;
}
.animation-top-right {
	-webkit-transform-origin: 100% 0;
	    -ms-transform-origin: 100% 0;
	     -o-transform-origin: 100% 0;
	        transform-origin: 100% 0;
}
.animation-middle-left {
	-webkit-transform-origin: 0 50%;
	    -ms-transform-origin: 0 50%;
	     -o-transform-origin: 0 50%;
	        transform-origin: 0 50%;
}
.animation-middle-right {
	-webkit-transform-origin: 100% 50%;
	    -ms-transform-origin: 100% 50%;
	     -o-transform-origin: 100% 50%;
	        transform-origin: 100% 50%;
}
.animation-bottom-left {
	-webkit-transform-origin: 0 100%;
	    -ms-transform-origin: 0 100%;
	     -o-transform-origin: 0 100%;
	        transform-origin: 0 100%;
}
.animation-bottom-center {
	-webkit-transform-origin: 50% 100%;
	    -ms-transform-origin: 50% 100%;
	     -o-transform-origin: 50% 100%;
	        transform-origin: 50% 100%;
}
.animation-bottom-right {
	-webkit-transform-origin: 100% 100%;
	    -ms-transform-origin: 100% 100%;
	     -o-transform-origin: 100% 100%;
	        transform-origin: 100% 100%;
}
.animation-easing-easeInOut {
	-webkit-animation-timing-function: cubic-bezier(.42, 0, .58, 1);
	     -o-animation-timing-function: cubic-bezier(.42, 0, .58, 1);
	        animation-timing-function: cubic-bezier(.42, 0, .58, 1);
}
.animation-easing-easeInQuad {
	-webkit-animation-timing-function: cubic-bezier(.55, .085, .68, .53);
	     -o-animation-timing-function: cubic-bezier(.55, .085, .68, .53);
	        animation-timing-function: cubic-bezier(.55, .085, .68, .53);
}
.animation-easing-easeInCubic {
	-webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
	     -o-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
	        animation-timing-function: cubic-bezier(.55, .055, .675, .19);
}
.animation-easing-easeInQuart {
	-webkit-animation-timing-function: cubic-bezier(.895, .03, .685, .22);
	     -o-animation-timing-function: cubic-bezier(.895, .03, .685, .22);
	        animation-timing-function: cubic-bezier(.895, .03, .685, .22);
}
.animation-easing-easeInQuint {
	-webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
	     -o-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
	        animation-timing-function: cubic-bezier(.755, .05, .855, .06);
}
.animation-easing-easeInSine {
	-webkit-animation-timing-function: cubic-bezier(.47, 0, .745, .715);
	     -o-animation-timing-function: cubic-bezier(.47, 0, .745, .715);
	        animation-timing-function: cubic-bezier(.47, 0, .745, .715);
}
.animation-easing-easeInExpo {
	-webkit-animation-timing-function: cubic-bezier(.95, .05, .795, .035);
	     -o-animation-timing-function: cubic-bezier(.95, .05, .795, .035);
	        animation-timing-function: cubic-bezier(.95, .05, .795, .035);
}
.animation-easing-easeInCirc {
	-webkit-animation-timing-function: cubic-bezier(.6, .04, .98, .335);
	     -o-animation-timing-function: cubic-bezier(.6, .04, .98, .335);
	        animation-timing-function: cubic-bezier(.6, .04, .98, .335);
}
.animation-easing-easeInBack {
	-webkit-animation-timing-function: cubic-bezier(.6, -.28, .735, .045);
	     -o-animation-timing-function: cubic-bezier(.6, -.28, .735, .045);
	        animation-timing-function: cubic-bezier(.6, -.28, .735, .045);
}
.animation-easing-eastOutQuad {
	-webkit-animation-timing-function: cubic-bezier(.25, .46, .45, .94);
	     -o-animation-timing-function: cubic-bezier(.25, .46, .45, .94);
	        animation-timing-function: cubic-bezier(.25, .46, .45, .94);
}
.animation-easing-easeOutCubic {
	-webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
	     -o-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
	        animation-timing-function: cubic-bezier(.215, .61, .355, 1);
}
.animation-easing-easeOutQuart {
	-webkit-animation-timing-function: cubic-bezier(.165, .84, .44, 1);
	     -o-animation-timing-function: cubic-bezier(.165, .84, .44, 1);
	        animation-timing-function: cubic-bezier(.165, .84, .44, 1);
}
.animation-easing-easeOutQuint {
	-webkit-animation-timing-function: cubic-bezier(.23, 1, .32, 1);
	     -o-animation-timing-function: cubic-bezier(.23, 1, .32, 1);
	        animation-timing-function: cubic-bezier(.23, 1, .32, 1);
}
.animation-easing-easeOutSine {
	-webkit-animation-timing-function: cubic-bezier(.39, .575, .565, 1);
	     -o-animation-timing-function: cubic-bezier(.39, .575, .565, 1);
	        animation-timing-function: cubic-bezier(.39, .575, .565, 1);
}
.animation-easing-easeOutExpo {
	-webkit-animation-timing-function: cubic-bezier(.19, 1, .22, 1);
	     -o-animation-timing-function: cubic-bezier(.19, 1, .22, 1);
	        animation-timing-function: cubic-bezier(.19, 1, .22, 1);
}
.animation-easing-easeOutCirc {
	-webkit-animation-timing-function: cubic-bezier(.075, .82, .165, 1);
	     -o-animation-timing-function: cubic-bezier(.075, .82, .165, 1);
	        animation-timing-function: cubic-bezier(.075, .82, .165, 1);
}
.animation-easing-easeOutBack {
	-webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1.275);
	     -o-animation-timing-function: cubic-bezier(.175, .885, .32, 1.275);
	        animation-timing-function: cubic-bezier(.175, .885, .32, 1.275);
}
.animation-easing-easeInOutQuad {
	-webkit-animation-timing-function: cubic-bezier(.455, .03, .515, .955);
	     -o-animation-timing-function: cubic-bezier(.455, .03, .515, .955);
	        animation-timing-function: cubic-bezier(.455, .03, .515, .955);
}
.animation-easing-easeInOutCubic {
	-webkit-animation-timing-function: cubic-bezier(.645, .045, .355, 1);
	     -o-animation-timing-function: cubic-bezier(.645, .045, .355, 1);
	        animation-timing-function: cubic-bezier(.645, .045, .355, 1);
}
.animation-easing-easeInOutQuart {
	-webkit-animation-timing-function: cubic-bezier(.77, 0, .175, 1);
	     -o-animation-timing-function: cubic-bezier(.77, 0, .175, 1);
	        animation-timing-function: cubic-bezier(.77, 0, .175, 1);
}
.animation-easing-easeInOutQuint {
	-webkit-animation-timing-function: cubic-bezier(.86, 0, .07, 1);
	     -o-animation-timing-function: cubic-bezier(.86, 0, .07, 1);
	        animation-timing-function: cubic-bezier(.86, 0, .07, 1);
}
.animation-easing-easeInOutSine {
	-webkit-animation-timing-function: cubic-bezier(.445, .05, .55, .95);
	     -o-animation-timing-function: cubic-bezier(.445, .05, .55, .95);
	        animation-timing-function: cubic-bezier(.445, .05, .55, .95);
}
.animation-easing-easeInOutExpo {
	-webkit-animation-timing-function: cubic-bezier(1, 0, 0, 1);
	     -o-animation-timing-function: cubic-bezier(1, 0, 0, 1);
	        animation-timing-function: cubic-bezier(1, 0, 0, 1);
}
.animation-easing-easeInOutCirc {
	-webkit-animation-timing-function: cubic-bezier(.785, .135, .15, .86);
	     -o-animation-timing-function: cubic-bezier(.785, .135, .15, .86);
	        animation-timing-function: cubic-bezier(.785, .135, .15, .86);
}
.animation-easing-easeInOutBack {
	-webkit-animation-timing-function: cubic-bezier(.68, -.55, .265, 1.55);
	     -o-animation-timing-function: cubic-bezier(.68, -.55, .265, 1.55);
	        animation-timing-function: cubic-bezier(.68, -.55, .265, 1.55);
}
.animation-easing-easeInOutElastic {
	-webkit-animation-timing-function: cubic-bezier(1, -.56, 0, 1.455);
	     -o-animation-timing-function: cubic-bezier(1, -.56, 0, 1.455);
	        animation-timing-function: cubic-bezier(1, -.56, 0, 1.455);
}
@-webkit-keyframes fade {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@-o-keyframes fade {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@keyframes fade {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@-webkit-keyframes scale-12 {
	0% {
		-webkit-transform: scale(1.2);
		        transform: scale(1.2);
	}
	100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes scale-12 {
	0% {
		-o-transform: scale(1.2);
		   transform: scale(1.2);
	}
	100% {
		-o-transform: scale(1);
		   transform: scale(1);
	}
}
@keyframes scale-12 {
	0% {
		-webkit-transform: scale(1.2);
		     -o-transform: scale(1.2);
		        transform: scale(1.2);
	}
	100% {
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@-webkit-keyframes fade-scale-02 {
	0% {
		opacity: 0;
		-webkit-transform: scale(.2);
		        transform: scale(.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes fade-scale-02 {
	0% {
		opacity: 0;
		-o-transform: scale(.2);
		   transform: scale(.2);
	}
	100% {
		opacity: 1;
		-o-transform: scale(1);
		   transform: scale(1);
	}
}
@keyframes fade-scale-02 {
	0% {
		opacity: 0;
		-webkit-transform: scale(.2);
		     -o-transform: scale(.2);
		        transform: scale(.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@-webkit-keyframes fade-scale-18 {
	0% {
		opacity: 0;
		-webkit-transform: scale(1.8);
		        transform: scale(1.8);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		        transform: scale(1);
	}
}
@-o-keyframes fade-scale-18 {
	0% {
		opacity: 0;
		-o-transform: scale(1.8);
		   transform: scale(1.8);
	}
	100% {
		opacity: 1;
		-o-transform: scale(1);
		   transform: scale(1);
	}
}
@keyframes fade-scale-18 {
	0% {
		opacity: 0;
		-webkit-transform: scale(1.8);
		     -o-transform: scale(1.8);
		        transform: scale(1.8);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		     -o-transform: scale(1);
		        transform: scale(1);
	}
}
@-webkit-keyframes slide-top {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		        transform: translate3d(0, -100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-o-keyframes slide-top {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		        transform: translate3d(0, -100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@keyframes slide-top {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		        transform: translate3d(0, -100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-webkit-keyframes slide-bottom {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0);
		        transform: translate3d(0, 100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-o-keyframes slide-bottom {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0);
		        transform: translate3d(0, 100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@keyframes slide-bottom {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0);
		        transform: translate3d(0, 100%, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-webkit-keyframes slide-left {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0);
		        transform: translate3d(-100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-o-keyframes slide-left {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0);
		        transform: translate3d(-100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@keyframes slide-left {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0);
		        transform: translate3d(-100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-webkit-keyframes slide-right {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0);
		        transform: translate3d(100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-o-keyframes slide-right {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0);
		        transform: translate3d(100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@keyframes slide-right {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0);
		        transform: translate3d(100%, 0, 0);
	}
	100% {
		opacity: 1;
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
	}
}
@-webkit-keyframes shake {
	0%, 100% {
		-webkit-transform: translateX(0);
		        transform: translateX(0);
	}
	10% {
		-webkit-transform: translateX(-9px);
		        transform: translateX(-9px);
	}
	20% {
		-webkit-transform: translateX(8px);
		        transform: translateX(8px);
	}
	30% {
		-webkit-transform: translateX(-7px);
		        transform: translateX(-7px);
	}
	40% {
		-webkit-transform: translateX(6px);
		        transform: translateX(6px);
	}
	50% {
		-webkit-transform: translateX(-5px);
		        transform: translateX(-5px);
	}
	60% {
		-webkit-transform: translateX(4px);
		        transform: translateX(4px);
	}
	70% {
		-webkit-transform: translateX(-3px);
		        transform: translateX(-3px);
	}
	80% {
		-webkit-transform: translateX(2px);
		        transform: translateX(2px);
	}
	90% {
		-webkit-transform: translateX(-1px);
		        transform: translateX(-1px);
	}
}
@-o-keyframes shake {
	0%, 100% {
		-o-transform: translateX(0);
		   transform: translateX(0);
	}
	10% {
		-o-transform: translateX(-9px);
		   transform: translateX(-9px);
	}
	20% {
		-o-transform: translateX(8px);
		   transform: translateX(8px);
	}
	30% {
		-o-transform: translateX(-7px);
		   transform: translateX(-7px);
	}
	40% {
		-o-transform: translateX(6px);
		   transform: translateX(6px);
	}
	50% {
		-o-transform: translateX(-5px);
		   transform: translateX(-5px);
	}
	60% {
		-o-transform: translateX(4px);
		   transform: translateX(4px);
	}
	70% {
		-o-transform: translateX(-3px);
		   transform: translateX(-3px);
	}
	80% {
		-o-transform: translateX(2px);
		   transform: translateX(2px);
	}
	90% {
		-o-transform: translateX(-1px);
		   transform: translateX(-1px);
	}
}
@keyframes shake {
	0%, 100% {
		-webkit-transform: translateX(0);
		     -o-transform: translateX(0);
		        transform: translateX(0);
	}
	10% {
		-webkit-transform: translateX(-9px);
		     -o-transform: translateX(-9px);
		        transform: translateX(-9px);
	}
	20% {
		-webkit-transform: translateX(8px);
		     -o-transform: translateX(8px);
		        transform: translateX(8px);
	}
	30% {
		-webkit-transform: translateX(-7px);
		     -o-transform: translateX(-7px);
		        transform: translateX(-7px);
	}
	40% {
		-webkit-transform: translateX(6px);
		     -o-transform: translateX(6px);
		        transform: translateX(6px);
	}
	50% {
		-webkit-transform: translateX(-5px);
		     -o-transform: translateX(-5px);
		        transform: translateX(-5px);
	}
	60% {
		-webkit-transform: translateX(4px);
		     -o-transform: translateX(4px);
		        transform: translateX(4px);
	}
	70% {
		-webkit-transform: translateX(-3px);
		     -o-transform: translateX(-3px);
		        transform: translateX(-3px);
	}
	80% {
		-webkit-transform: translateX(2px);
		     -o-transform: translateX(2px);
		        transform: translateX(2px);
	}
	90% {
		-webkit-transform: translateX(-1px);
		     -o-transform: translateX(-1px);
		        transform: translateX(-1px);
	}
}
.primary-100 {
	color: #ffeaea !important;
}
.primary-200 {
	color: #fad3d3 !important;
}
.primary-300 {
	color: #fab4b4 !important;
}
.primary-400 {
	color: #fa9898 !important;
}
.primary-500 {
	color: #fa7a7a !important;
}
.primary-600 {
	color: #f96868 !important;
}
.primary-700 {
	color: #e9595b !important;
}
.primary-800 {
	color: #d6494b !important;
}
.blue-100 {
	color: #e8f1f8 !important;
}
.blue-200 {
	color: #d5e4f1 !important;
}
.blue-300 {
	color: #bcd8f1 !important;
}
.blue-400 {
	color: #a2caee !important;
}
.blue-500 {
	color: #89bceb !important;
}
.blue-600 {
	color: #62a8ea !important;
}
.blue-700 {
	color: #4e97d9 !important;
}
.blue-800 {
	color: #3583ca !important;
}
.red-100 {
	color: #ffeaea !important;
}
.red-200 {
	color: #fad3d3 !important;
}
.red-300 {
	color: #fab4b4 !important;
}
.red-400 {
	color: #fa9898 !important;
}
.red-500 {
	color: #fa7a7a !important;
}
.red-600 {
	color: #f96868 !important;
}
.red-700 {
	color: #e9595b !important;
}
.red-800 {
	color: #d6494b !important;
}
.pink-100 {
	color: #fce4ec !important;
}
.pink-200 {
	color: #ffccde !important;
}
.pink-300 {
	color: #fba9c6 !important;
}
.pink-400 {
	color: #fb8db4 !important;
}
.pink-500 {
	color: #f978a6 !important;
}
.pink-600 {
	color: #f96197 !important;
}
.pink-700 {
	color: #f44c87 !important;
}
.pink-800 {
	color: #e53b75 !important;
}
.purple-100 {
	color: #f6f2ff !important;
}
.purple-200 {
	color: #e3dbf4 !important;
}
.purple-300 {
	color: #d2c5ec !important;
}
.purple-400 {
	color: #bba7e4 !important;
}
.purple-500 {
	color: #a58add !important;
}
.purple-600 {
	color: #926dde !important;
}
.purple-700 {
	color: #7c51d1 !important;
}
.purple-800 {
	color: #6d45bc !important;
}
.indigo-100 {
	color: #edeff9 !important;
}
.indigo-200 {
	color: #dadef5 !important;
}
.indigo-300 {
	color: #bcc5f4 !important;
}
.indigo-400 {
	color: #9daaf3 !important;
}
.indigo-500 {
	color: #8897ec !important;
}
.indigo-600 {
	color: #677ae4 !important;
}
.indigo-700 {
	color: #5166d6 !important;
}
.indigo-800 {
	color: #465bd4 !important;
}
.cyan-100 {
	color: #ecf9fa !important;
}
.cyan-200 {
	color: #d3eff2 !important;
}
.cyan-300 {
	color: #baeaef !important;
}
.cyan-400 {
	color: #9ae1e9 !important;
}
.cyan-500 {
	color: #77d6e1 !important;
}
.cyan-600 {
	color: #57c7d4 !important;
}
.cyan-700 {
	color: #47b8c6 !important;
}
.cyan-800 {
	color: #37a9b7 !important;
}
.teal-100 {
	color: #ecfdfc !important;
}
.teal-200 {
	color: #cdf4f1 !important;
}
.teal-300 {
	color: #99e1da !important;
}
.teal-400 {
	color: #79d1c9 !important;
}
.teal-500 {
	color: #56bfb5 !important;
}
.teal-600 {
	color: #3aa99e !important;
}
.teal-700 {
	color: #269b8f !important;
}
.teal-800 {
	color: #178d81 !important;
}
.green-100 {
	color: #e7faf2 !important;
}
.green-200 {
	color: #bfedd8 !important;
}
.green-300 {
	color: #9fe5c5 !important;
}
.green-400 {
	color: #7dd3ae !important;
}
.green-500 {
	color: #5cd29d !important;
}
.green-600 {
	color: #46be8a !important;
}
.green-700 {
	color: #36ab7a !important;
}
.green-800 {
	color: #279566 !important;
}
.light-green-100 {
	color: #f1f7ea !important;
}
.light-green-200 {
	color: #e0ecd1 !important;
}
.light-green-300 {
	color: #cadfb1 !important;
}
.light-green-400 {
	color: #bad896 !important;
}
.light-green-500 {
	color: #acd57c !important;
}
.light-green-600 {
	color: #9ece67 !important;
}
.light-green-700 {
	color: #83b944 !important;
}
.light-green-800 {
	color: #70a532 !important;
}
.yellow-100 {
	color: #fffae7 !important;
}
.yellow-200 {
	color: #f9eec1 !important;
}
.yellow-300 {
	color: #f6e7a9 !important;
}
.yellow-400 {
	color: #f8e59b !important;
}
.yellow-500 {
	color: #f7e083 !important;
}
.yellow-600 {
	color: #f7da64 !important;
}
.yellow-700 {
	color: #f9cd48 !important;
}
.yellow-800 {
	color: #fbc02d !important;
}
.orange-100 {
	color: #fff3e6 !important;
}
.orange-200 {
	color: #ffddb9 !important;
}
.orange-300 {
	color: #fbce9d !important;
}
.orange-400 {
	color: #f6be80 !important;
}
.orange-500 {
	color: #f4b066 !important;
}
.orange-600 {
	color: #f2a654 !important;
}
.orange-700 {
	color: #ec9940 !important;
}
.orange-800 {
	color: #e98f2e !important;
}
.brown-100 {
	color: #fae6df !important;
}
.brown-200 {
	color: #e2bdaf !important;
}
.brown-300 {
	color: #d3aa9c !important;
}
.brown-400 {
	color: #b98e7e !important;
}
.brown-500 {
	color: #a17768 !important;
}
.brown-600 {
	color: #8d6658 !important;
}
.brown-700 {
	color: #7d5b4f !important;
}
.brown-800 {
	color: #715146 !important;
}
.grey-100 {
	color: #fafafa !important;
}
.grey-200 {
	color: #eee !important;
}
.grey-300 {
	color: #e0e0e0 !important;
}
.grey-400 {
	color: #bdbdbd !important;
}
.grey-500 {
	color: #9e9e9e !important;
}
.grey-600 {
	color: #757575 !important;
}
.grey-700 {
	color: #616161 !important;
}
.grey-800 {
	color: #424242 !important;
}
.blue-grey-100 {
	color: #f3f7f9 !important;
}
.blue-grey-200 {
	color: #e4eaec !important;
}
.blue-grey-300 {
	color: #ccd5db !important;
}
.blue-grey-400 {
	color: #a3afb7 !important;
}
.blue-grey-500 {
	color: #76838f !important;
}
.blue-grey-600 {
	color: #526069 !important;
}
.blue-grey-700 {
	color: #37474f !important;
}
.blue-grey-800 {
	color: #263238 !important;
}
.bg-primary-100 {
	background-color: #ffeaea !important;
}
.bg-primary-200 {
	background-color: #fad3d3 !important;
}
.bg-primary-300 {
	background-color: #fab4b4 !important;
}
.bg-primary-400 {
	background-color: #fa9898 !important;
}
.bg-primary-500 {
	background-color: #fa7a7a !important;
}
.bg-primary-600 {
	background-color: #f96868 !important;
}
.bg-primary-700 {
	background-color: #e9595b !important;
}
.bg-primary-800 {
	background-color: #d6494b !important;
}
.bg-blue-100 {
	background-color: #e8f1f8 !important;
}
.bg-blue-200 {
	background-color: #d5e4f1 !important;
}
.bg-blue-300 {
	background-color: #bcd8f1 !important;
}
.bg-blue-400 {
	background-color: #a2caee !important;
}
.bg-blue-500 {
	background-color: #89bceb !important;
}
.bg-blue-600 {
	background-color: #62a8ea !important;
}
.bg-blue-700 {
	background-color: #4e97d9 !important;
}
.bg-blue-800 {
	background-color: #3583ca !important;
}
.bg-red-100 {
	background-color: #ffeaea !important;
}
.bg-red-200 {
	background-color: #fad3d3 !important;
}
.bg-red-300 {
	background-color: #fab4b4 !important;
}
.bg-red-400 {
	background-color: #fa9898 !important;
}
.bg-red-500 {
	background-color: #fa7a7a !important;
}
.bg-red-600 {
	background-color: #f96868 !important;
}
.bg-red-700 {
	background-color: #e9595b !important;
}
.bg-red-800 {
	background-color: #d6494b !important;
}
.bg-pink-100 {
	background-color: #fce4ec !important;
}
.bg-pink-200 {
	background-color: #ffccde !important;
}
.bg-pink-300 {
	background-color: #fba9c6 !important;
}
.bg-pink-400 {
	background-color: #fb8db4 !important;
}
.bg-pink-500 {
	background-color: #f978a6 !important;
}
.bg-pink-600 {
	background-color: #f96197 !important;
}
.bg-pink-700 {
	background-color: #f44c87 !important;
}
.bg-pink-800 {
	background-color: #e53b75 !important;
}
.bg-purple-100 {
	background-color: #f6f2ff !important;
}
.bg-purple-200 {
	background-color: #e3dbf4 !important;
}
.bg-purple-300 {
	background-color: #d2c5ec !important;
}
.bg-purple-400 {
	background-color: #bba7e4 !important;
}
.bg-purple-500 {
	background-color: #a58add !important;
}
.bg-purple-600 {
	background-color: #926dde !important;
}
.bg-purple-700 {
	background-color: #7c51d1 !important;
}
.bg-purple-800 {
	background-color: #6d45bc !important;
}
.bg-indigo-100 {
	background-color: #edeff9 !important;
}
.bg-indigo-200 {
	background-color: #dadef5 !important;
}
.bg-indigo-300 {
	background-color: #bcc5f4 !important;
}
.bg-indigo-400 {
	background-color: #9daaf3 !important;
}
.bg-indigo-500 {
	background-color: #8897ec !important;
}
.bg-indigo-600 {
	background-color: #677ae4 !important;
}
.bg-indigo-700 {
	background-color: #5166d6 !important;
}
.bg-indigo-800 {
	background-color: #465bd4 !important;
}
.bg-cyan-100 {
	background-color: #ecf9fa !important;
}
.bg-cyan-200 {
	background-color: #d3eff2 !important;
}
.bg-cyan-300 {
	background-color: #baeaef !important;
}
.bg-cyan-400 {
	background-color: #9ae1e9 !important;
}
.bg-cyan-500 {
	background-color: #77d6e1 !important;
}
.bg-cyan-600 {
	background-color: #57c7d4 !important;
}
.bg-cyan-700 {
	background-color: #47b8c6 !important;
}
.bg-cyan-800 {
	background-color: #37a9b7 !important;
}
.bg-teal-100 {
	background-color: #ecfdfc !important;
}
.bg-teal-200 {
	background-color: #cdf4f1 !important;
}
.bg-teal-300 {
	background-color: #99e1da !important;
}
.bg-teal-400 {
	background-color: #79d1c9 !important;
}
.bg-teal-500 {
	background-color: #56bfb5 !important;
}
.bg-teal-600 {
	background-color: #3aa99e !important;
}
.bg-teal-700 {
	background-color: #269b8f !important;
}
.bg-teal-800 {
	background-color: #178d81 !important;
}
.bg-green-100 {
	background-color: #e7faf2 !important;
}
.bg-green-200 {
	background-color: #bfedd8 !important;
}
.bg-green-300 {
	background-color: #9fe5c5 !important;
}
.bg-green-400 {
	background-color: #7dd3ae !important;
}
.bg-green-500 {
	background-color: #5cd29d !important;
}
.bg-green-600 {
	background-color: #46be8a !important;
}
.bg-green-700 {
	background-color: #36ab7a !important;
}
.bg-green-800 {
	background-color: #279566 !important;
}
.bg-light-green-100 {
	background-color: #f1f7ea !important;
}
.bg-light-green-200 {
	background-color: #e0ecd1 !important;
}
.bg-light-green-300 {
	background-color: #cadfb1 !important;
}
.bg-light-green-400 {
	background-color: #bad896 !important;
}
.bg-light-green-500 {
	background-color: #acd57c !important;
}
.bg-light-green-600 {
	background-color: #9ece67 !important;
}
.bg-light-green-700 {
	background-color: #83b944 !important;
}
.bg-light-green-800 {
	background-color: #70a532 !important;
}
.bg-yellow-100 {
	background-color: #fffae7 !important;
}
.bg-yellow-200 {
	background-color: #f9eec1 !important;
}
.bg-yellow-300 {
	background-color: #f6e7a9 !important;
}
.bg-yellow-400 {
	background-color: #f8e59b !important;
}
.bg-yellow-500 {
	background-color: #f7e083 !important;
}
.bg-yellow-600 {
	background-color: #f7da64 !important;
}
.bg-yellow-700 {
	background-color: #f9cd48 !important;
}
.bg-yellow-800 {
	background-color: #fbc02d !important;
}
.bg-orange-100 {
	background-color: #fff3e6 !important;
}
.bg-orange-200 {
	background-color: #ffddb9 !important;
}
.bg-orange-300 {
	background-color: #fbce9d !important;
}
.bg-orange-400 {
	background-color: #f6be80 !important;
}
.bg-orange-500 {
	background-color: #f4b066 !important;
}
.bg-orange-600 {
	background-color: #f2a654 !important;
}
.bg-orange-700 {
	background-color: #ec9940 !important;
}
.bg-orange-800 {
	background-color: #e98f2e !important;
}
.bg-brown-100 {
	background-color: #fae6df !important;
}
.bg-brown-200 {
	background-color: #e2bdaf !important;
}
.bg-brown-300 {
	background-color: #d3aa9c !important;
}
.bg-brown-400 {
	background-color: #b98e7e !important;
}
.bg-brown-500 {
	background-color: #a17768 !important;
}
.bg-brown-600 {
	background-color: #8d6658 !important;
}
.bg-brown-700 {
	background-color: #7d5b4f !important;
}
.bg-brown-800 {
	background-color: #715146 !important;
}
.bg-grey-100 {
	background-color: #fafafa !important;
}
.bg-grey-200 {
	background-color: #eee !important;
}
.bg-grey-300 {
	background-color: #e0e0e0 !important;
}
.bg-grey-400 {
	background-color: #bdbdbd !important;
}
.bg-grey-500 {
	background-color: #9e9e9e !important;
}
.bg-grey-600 {
	background-color: #757575 !important;
}
.bg-grey-700 {
	background-color: #616161 !important;
}
.bg-grey-800 {
	background-color: #424242 !important;
}
.bg-blue-grey-100 {
	background-color: #f3f7f9 !important;
}
.bg-blue-grey-200 {
	background-color: #e4eaec !important;
}
.bg-blue-grey-300 {
	background-color: #ccd5db !important;
}
.bg-blue-grey-400 {
	background-color: #a3afb7 !important;
}
.bg-blue-grey-500 {
	background-color: #76838f !important;
}
.bg-blue-grey-600 {
	background-color: #526069 !important;
}
.bg-blue-grey-700 {
	background-color: #37474f !important;
}
.bg-blue-grey-800 {
	background-color: #263238 !important;
}
.black {
	color: #000 !important;
}
.white {
	color: #fff !important;
}
.bg-white {
	color: #76838f;
	background-color: #fff;
}
.bg-primary {
	color: #fff;
	background-color: #f96868;
}
.bg-primary:hover {
	background-color: #fb9999;
}
.bg-primary a, a.bg-primary {
	color: #fff;
}
.bg-primary a:hover, a.bg-primary:hover {
	color: #fff;
}
.bg-success {
	color: #fff;
	background-color: #46be8a;
}
.bg-success:hover {
	background-color: #6ccba2;
}
.bg-success a, a.bg-success {
	color: #fff;
}
.bg-success a:hover, a.bg-success:hover {
	color: #fff;
}
.bg-info {
	color: #fff;
	background-color: #57c7d4;
}
.bg-info:hover {
	background-color: #80d5de;
}
.bg-info a, a.bg-info {
	color: #fff;
}
.bg-info a:hover, a.bg-info:hover {
	color: #fff;
}
.bg-warning {
	color: #fff;
	background-color: #f2a654;
}
.bg-warning:hover {
	background-color: #f6bf83;
}
.bg-warning a, a.bg-warning {
	color: #fff;
}
.bg-warning a:hover, a.bg-warning:hover {
	color: #fff;
}
.bg-danger {
	color: #fff;
	background-color: #f96868;
}
.bg-danger:hover {
	background-color: #fb9999;
}
.bg-danger a, a.bg-danger {
	color: #fff;
}
.bg-danger a:hover, a.bg-danger:hover {
	color: #fff;
}
.bg-dark {
	color: #fff;
	background-color: #526069;
}
.bg-dark:hover {
	background-color: #687a86;
}
.bg-dark a, a.bg-dark {
	color: #fff;
}
.bg-dark a:hover, a.bg-dark:hover {
	color: #fff;
}
.social-wechat {
	color: #fff;
	background-color: #51c332 !important;
}
.social-wechat:hover, .social-wechat:focus {
	color: #fff;
	background-color: #70d355 !important;
}
.social-wechat:active, .social-wechat.active {
	color: #fff;
	background-color: #409a28 !important;
}
.bg-wechat {
	background-color: #51c332;
}
.social-qq {
	color: #fff;
	background-color: #12b7f5 !important;
}
.social-qq:hover, .social-qq:focus {
	color: #fff;
	background-color: #43c6f7 !important;
}
.social-qq:active, .social-qq.active {
	color: #fff;
	background-color: #0996cb !important;
}
.bg-qq {
	background-color: #12b7f5;
}
.social-weibo {
	color: #fff;
	background-color: #e6624b !important;
}
.social-weibo:hover, .social-weibo:focus {
	color: #fff;
	background-color: #ec8978 !important;
}
.social-weibo:active, .social-weibo.active {
	color: #fff;
	background-color: #df3b1f !important;
}
.bg-weibo {
	background-color: #e6624b;
}
.social-facebook {
	color: #fff;
	background-color: #3b5998 !important;
}
.social-facebook:hover, .social-facebook:focus {
	color: #fff;
	background-color: #4c70ba !important;
}
.social-facebook:active, .social-facebook.active {
	color: #fff;
	background-color: #2d4373 !important;
}
.bg-facebook {
	background-color: #3b5998;
}
.social-twitter {
	color: #fff;
	background-color: #55acee !important;
}
.social-twitter:hover, .social-twitter:focus {
	color: #fff;
	background-color: #83c3f3 !important;
}
.social-twitter:active, .social-twitter.active {
	color: #fff;
	background-color: #2795e9 !important;
}
.bg-twitter {
	background-color: #55acee;
}
.social-google-plus {
	color: #fff;
	background-color: #dd4b39 !important;
}
.social-google-plus:hover, .social-google-plus:focus {
	color: #fff;
	background-color: #e47365 !important;
}
.social-google-plus:active, .social-google-plus.active {
	color: #fff;
	background-color: #c23321 !important;
}
.bg-google-plus {
	background-color: #dd4b39;
}
.social-linkedin {
	color: #fff;
	background-color: #0976b4 !important;
}
.social-linkedin:hover, .social-linkedin:focus {
	color: #fff;
	background-color: #0b96e5 !important;
}
.social-linkedin:active, .social-linkedin.active {
	color: #fff;
	background-color: #075683 !important;
}
.bg-linkedin {
	background-color: #0976b4;
}
.social-flickr {
	color: #fff;
	background-color: #ff0084 !important;
}
.social-flickr:hover, .social-flickr:focus {
	color: #fff;
	background-color: #ff339d !important;
}
.social-flickr:active, .social-flickr.active {
	color: #fff;
	background-color: #cc006a !important;
}
.bg-flickr {
	background-color: #ff0084;
}
.social-tumblr {
	color: #fff;
	background-color: #35465c !important;
}
.social-tumblr:hover, .social-tumblr:focus {
	color: #fff;
	background-color: #485f7c !important;
}
.social-tumblr:active, .social-tumblr.active {
	color: #fff;
	background-color: #222d3c !important;
}
.bg-tumblr {
	background-color: #35465c;
}
.social-xing {
	color: #fff;
	background-color: #024b4d !important;
}
.social-xing:hover, .social-xing:focus {
	color: #fff;
	background-color: #037b7f !important;
}
.social-xing:active, .social-xing.active {
	color: #fff;
	background-color: #011b1b !important;
}
.bg-xing {
	background-color: #024b4d;
}
.social-github {
	color: #fff;
	background-color: #4183c4 !important;
}
.social-github:hover, .social-github:focus {
	color: #fff;
	background-color: #689cd0 !important;
}
.social-github:active, .social-github.active {
	color: #fff;
	background-color: #3269a0 !important;
}
.bg-github {
	background-color: #4183c4;
}
.social-html5 {
	color: #fff;
	background-color: #e44f26 !important;
}
.social-html5:hover, .social-html5:focus {
	color: #fff;
	background-color: #ea7453 !important;
}
.social-html5:active, .social-html5.active {
	color: #fff;
	background-color: #bf3c18 !important;
}
.bg-html5 {
	background-color: #e44f26;
}
.social-openid {
	color: #fff;
	background-color: #f67d28 !important;
}
.social-openid:hover, .social-openid:focus {
	color: #fff;
	background-color: #f89b59 !important;
}
.social-openid:active, .social-openid.active {
	color: #fff;
	background-color: #e26309 !important;
}
.bg-openid {
	background-color: #f67d28;
}
.social-stack-overflow {
	color: #fff;
	background-color: #f86c01 !important;
}
.social-stack-overflow:hover, .social-stack-overflow:focus {
	color: #fff;
	background-color: #fe882e !important;
}
.social-stack-overflow:active, .social-stack-overflow.active {
	color: #fff;
	background-color: #c55601 !important;
}
.bg-stack-overflow {
	background-color: #f86c01;
}
.social-css3 {
	color: #fff;
	background-color: #1572b6 !important;
}
.social-css3:hover, .social-css3:focus {
	color: #fff;
	background-color: #1a8fe4 !important;
}
.social-css3:active, .social-css3.active {
	color: #fff;
	background-color: #105588 !important;
}
.bg-css3 {
	background-color: #1572b6;
}
.social-youtube {
	color: #fff;
	background-color: #b31217 !important;
}
.social-youtube:hover, .social-youtube:focus {
	color: #fff;
	background-color: #e1171d !important;
}
.social-youtube:active, .social-youtube.active {
	color: #fff;
	background-color: #850d11 !important;
}
.bg-youtube {
	background-color: #b31217;
}
.social-dribbble {
	color: #fff;
	background-color: #c32361 !important;
}
.social-dribbble:hover, .social-dribbble:focus {
	color: #fff;
	background-color: #dc3d7b !important;
}
.social-dribbble:active, .social-dribbble.active {
	color: #fff;
	background-color: #981b4b !important;
}
.bg-dribbble {
	background-color: #c32361;
}
.social-instagram {
	color: #fff;
	background-color: #3f729b !important;
}
.social-instagram:hover, .social-instagram:focus {
	color: #fff;
	background-color: #548cb9 !important;
}
.social-instagram:active, .social-instagram.active {
	color: #fff;
	background-color: #305777 !important;
}
.bg-instagram {
	background-color: #3f729b;
}
.social-pinterest {
	color: #fff;
	background-color: #cc2127 !important;
}
.social-pinterest:hover, .social-pinterest:focus {
	color: #fff;
	background-color: #e04046 !important;
}
.social-pinterest:active, .social-pinterest.active {
	color: #fff;
	background-color: #a01a1f !important;
}
.bg-pinterest {
	background-color: #cc2127;
}
.social-vk {
	color: #fff;
	background-color: #3d5a7d !important;
}
.social-vk:hover, .social-vk:focus {
	color: #fff;
	background-color: #4e739f !important;
}
.social-vk:active, .social-vk.active {
	color: #fff;
	background-color: #2c415b !important;
}
.bg-vk {
	background-color: #3d5a7d;
}
.social-yahoo {
	color: #fff;
	background-color: #350178 !important;
}
.social-yahoo:hover, .social-yahoo:focus {
	color: #fff;
	background-color: #4b01ab !important;
}
.social-yahoo:active, .social-yahoo.active {
	color: #fff;
	background-color: #1f0145 !important;
}
.bg-yahoo {
	background-color: #350178;
}
.social-behance {
	color: #fff;
	background-color: #1769ff !important;
}
.social-behance:hover, .social-behance:focus {
	color: #fff;
	background-color: #4a8aff !important;
}
.social-behance:active, .social-behance.active {
	color: #fff;
	background-color: #0050e3 !important;
}
.bg-behance {
	background-color: #024b4d;
}
.social-dropbox {
	color: #fff;
	background-color: #007ee5 !important;
}
.social-dropbox:hover, .social-dropbox:focus {
	color: #fff;
	background-color: #1998ff !important;
}
.social-dropbox:active, .social-dropbox.active {
	color: #fff;
	background-color: #0062b2 !important;
}
.bg-dropbox {
	background-color: #007ee5;
}
.social-reddit {
	color: #fff;
	background-color: #ff4500 !important;
}
.social-reddit:hover, .social-reddit:focus {
	color: #fff;
	background-color: #ff6a33 !important;
}
.social-reddit:active, .social-reddit.active {
	color: #fff;
	background-color: #cc3700 !important;
}
.bg-reddit {
	background-color: #ff4500;
}
.social-spotify {
	color: #fff;
	background-color: #7ab800 !important;
}
.social-spotify:hover, .social-spotify:focus {
	color: #fff;
	background-color: #9ceb00 !important;
}
.social-spotify:active, .social-spotify.active {
	color: #fff;
	background-color: #588500 !important;
}
.bg-spotify {
	background-color: #7ab800;
}
.social-vine {
	color: #fff;
	background-color: #00b488 !important;
}
.social-vine:hover, .social-vine:focus {
	color: #fff;
	background-color: #00e7af !important;
}
.social-vine:active, .social-vine.active {
	color: #fff;
	background-color: #008161 !important;
}
.bg-vine {
	background-color: #00b488;
}
.social-foursquare {
	color: #fff;
	background-color: #0cbadf !important;
}
.social-foursquare:hover, .social-foursquare:focus {
	color: #fff;
	background-color: #2ad0f4 !important;
}
.social-foursquare:active, .social-foursquare.active {
	color: #fff;
	background-color: #0992af !important;
}
.bg-foursquare {
	background-color: #0cbadf;
}
.social-vimeo {
	color: #fff;
	background-color: #1ab7ea !important;
}
.social-vimeo:hover, .social-vimeo:focus {
	color: #fff;
	background-color: #49c6ee !important;
}
.social-vimeo:active, .social-vimeo.active {
	color: #fff;
	background-color: #1295bf !important;
}
.bg-vimeo {
	background-color: #1ab7ea;
}
.social-skype {
	color: #fff;
	background-color: #77bcfd !important;
}
.social-skype:hover, .social-skype:focus {
	color: #fff;
	background-color: #a9d5fe !important;
}
.social-skype:active, .social-skype.active {
	color: #fff;
	background-color: #45a3fc !important;
}
.bg-skype {
	background-color: #77bcfd;
}
a:focus {
	outline: none;
}
.blocks, [class*="blocks-"] {
	padding: 0;
	margin: 0;
	margin-right: -12px;
	margin-left: -12px;
	list-style: none;
}
.blocks:before, [class*="blocks-"]:before, .blocks:after, [class*="blocks-"]:after {
	display: table;
	content: " ";
}
.blocks:after, [class*="blocks-"]:after {
	clear: both;
}
.blocks > li, [class*="blocks-"] > li {
	position: relative;
	float: left;
	min-height: 1px;
	padding-right: 12px;
	padding-left: 12px;
	margin-bottom: 0;
}
.blocks.no-space, [class*="blocks-"].no-space {
	margin: 0;
}
.blocks.no-space > li, [class*="blocks-"].no-space > li {
	padding-right: 0;
	padding-left: 0;
	margin-bottom: 0;
}
.blocks-2 > li:nth-child(2n+3), .blocks-3 > li:nth-child(3n+4), .blocks-4 > li:nth-child(4n+5), .blocks-5 > li:nth-child(5n+6), .blocks-6 > li:nth-child(6n+7), .block-first {
	clear: both;
}
.blocks-100 > li {
	width: 100%;
}
.blocks-2 > li {
	width: 50%;
}
.blocks-3 > li {
	width: 33.33333333%;
}
.blocks-4 > li {
	width: 25%;
}
.blocks-5 > li {
	width: 20%;
}
.blocks-6 > li {
	width: 16.66666667%;
}
@media (min-width: 480px) {
	.blocks-xs-100 > li {
		width: 100%;
	}
}
@media (min-width: 480px) {
	.blocks-xs-2 > li {
		width: 50%;
	}
}
@media (min-width: 480px) {
	.blocks-xs-3 > li {
		width: 33.33333333%;
	}
}
@media (min-width: 480px) {
	.blocks-xs-4 > li {
		width: 25%;
	}
}
@media (min-width: 480px) {
	.blocks-xs-5 > li {
		width: 20%;
	}
}
@media (min-width: 480px) {
	.blocks-xs-6 > li {
		width: 16.66666667%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-100 > li {
		width: 100%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-2 > li {
		width: 50%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-3 > li {
		width: 33.33333333%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-4 > li {
		width: 25%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-5 > li {
		width: 20%;
	}
}
@media (min-width: 768px) {
	.blocks-sm-6 > li {
		width: 16.66666667%;
	}
}
@media (min-width: 992px) {
	.blocks-md-100 > li {
		width: 100%;
	}
}
@media (min-width: 992px) {
	.blocks-md-2 > li {
		width: 50%;
	}
}
@media (min-width: 992px) {
	.blocks-md-3 > li {
		width: 33.33333333%;
	}
}
@media (min-width: 992px) {
	.blocks-md-4 > li {
		width: 25%;
	}
}
@media (min-width: 992px) {
	.blocks-md-5 > li {
		width: 20%;
	}
}
@media (min-width: 992px) {
	.blocks-md-6 > li {
		width: 16.66666667%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-100 > li {
		width: 100%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-2 > li {
		width: 50%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-3 > li {
		width: 33.33333333%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-4 > li {
		width: 25%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-5 > li {
		width: 20%;
	}
}
@media (min-width: 1200px) {
	.blocks-lg-6 > li {
		width: 16.66666667%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-100 > li {
		width: 100%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-2 > li {
		width: 50%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-3 > li {
		width: 33.33333333%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-4 > li {
		width: 25%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-5 > li {
		width: 20%;
	}
}
@media (min-width: 1600px) {
	.blocks-xlg-6 > li {
		width: 16.66666667%;
	}
}
.avatar {
	position: relative;
	display: inline-block;
	width: 40px;
	white-space: nowrap;
	vertical-align: bottom;
	border-radius: 1000px;
}
.avatar i {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 10px;
	height: 10px;
	border: 2px solid #fff;
	border-radius: 100%;
}
.avatar img {
	width: 100%;
	max-width: 100%;
	height: auto;
	border: 0 none;
	border-radius: 1000px;
}
.avatar-online i {
	background-color: #46be8a;
}
.avatar-off i {
	background-color: #526069;
}
.avatar-busy i {
	background-color: #f2a654;
}
.avatar-away i {
	background-color: #f96868;
}
.avatar-100 {
	width: 100px;
}
.avatar-100 i {
	width: 20px;
	height: 20px;
}
.avatar-lg {
	width: 50px;
}
.avatar-lg i {
	width: 12px;
	height: 12px;
}
.avatar-sm {
	width: 30px;
}
.avatar-sm i {
	width: 8px;
	height: 8px;
}
.avatar-xs {
	width: 20px;
}
.avatar-xs i {
	width: 7px;
	height: 7px;
}
.box {
	font-size: 14px;
	text-align: center;
}
.status {
	display: block;
	width: 10px;
	height: 10px;
	border: 2px solid #fff;
	border-radius: 100%;
}
.status-online {
	background-color: #46be8a;
}
.status-off {
	background-color: #526069;
}
.status-busy {
	background-color: #f2a654;
}
.status-away {
	background-color: #f96868;
}
.status-lg {
	width: 14px;
	height: 14px;
}
.status-md {
	width: 10px;
	height: 10px;
}
.status-sm {
	width: 8px;
	height: 8px;
}
.status-xs {
	width: 7px;
	height: 7px;
}
.icon {
	position: relative;
	display: inline-block;
	font-style: normal;
	font-weight: normal;
	line-height: 1;
	-webkit-transform: translate(0, 0);
	    -ms-transform: translate(0, 0);
	     -o-transform: translate(0, 0);
	        transform: translate(0, 0);

	text-rendering: auto;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.icon.pull-left {
	margin-right: .3em;
}
.icon.pull-right {
	margin-left: .3em;
}
.icon.icon-circle {
	position: relative;
	margin: .5em;
}
.icon.icon-circle:before {
	position: relative;
	z-index: 1;
}
.icon.icon-circle:after {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 0;
	width: 2em;
	height: 2em;
	content: "";
	background-color: inherit;
	border-radius: 100%;
	-webkit-transform: translate(-50%, -50%);
	    -ms-transform: translate(-50%, -50%);
	     -o-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
}
.icon.icon-lg {
	font-size: 1.33333333em;
	vertical-align: -15%;
}
.icon.icon-2x {
	font-size: 2em;
}
.icon.icon-3x {
	font-size: 3em;
}
.icon.icon-4x {
	font-size: 4em;
}
.icon.icon-5x {
	font-size: 5em;
}
.icon.icon-fw {
	width: 1.28571429em;
	text-align: center;
}
.icon.icon-ul {
	padding-left: 0;
	margin-left: 2.14285714em;
	list-style-type: none;
}
.icon.icon-ul > li {
	position: relative;
}
.icon.icon-li {
	position: absolute;
	top: .14285714em;
	left: -2.14285714em;
	width: 2.14285714em;
	text-align: center;
}
.icon.icon-li.icon-lg {
	left: -1.85714286em;
}
.icon.icon-border {
	padding: .2em .25em .15em;
	border: solid .08em #e4eaec;
	border-radius: .1em;
}
.icon.icon-spin {
	-webkit-animation: icon-spin 2s infinite linear;
	     -o-animation: icon-spin 2s infinite linear;
	        animation: icon-spin 2s infinite linear;
}
.icon.icon-spin-reverse {
	-webkit-animation: icon-spin-reverse 2s infinite linear;
	     -o-animation: icon-spin-reverse 2s infinite linear;
	        animation: icon-spin-reverse 2s infinite linear;
}
.icon.icon-rotate-90 {
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
	-webkit-transform: rotate(90deg);
	    -ms-transform: rotate(90deg);
	     -o-transform: rotate(90deg);
	        transform: rotate(90deg);
}
.icon.icon-rotate-180 {
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
	-webkit-transform: rotate(180deg);
	    -ms-transform: rotate(180deg);
	     -o-transform: rotate(180deg);
	        transform: rotate(180deg);
}
.icon.icon-rotate-270 {
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
	-webkit-transform: rotate(270deg);
	    -ms-transform: rotate(270deg);
	     -o-transform: rotate(270deg);
	        transform: rotate(270deg);
}
.icon.icon-flip-horizontal {
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
	-webkit-transform: scale(-1, 1);
	    -ms-transform: scale(-1, 1);
	     -o-transform: scale(-1, 1);
	        transform: scale(-1, 1);
}
.icon.icon-flip-vertical {
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
	-webkit-transform: scale(1, -1);
	    -ms-transform: scale(1, -1);
	     -o-transform: scale(1, -1);
	        transform: scale(1, -1);
}
.icon.icon-stack {
	position: relative;
	display: inline-block;
	width: 2em;
	height: 2em;
	line-height: 2em;
	vertical-align: middle;
}
.icon.icon-stack-1x, .icon.icon-stack-2x {
	position: absolute;
	left: 0;
	width: 100%;
	text-align: center;
}
.icon.icon-stack-1x {
	line-height: inherit;
}
.icon.icon-stack-2x {
	font-size: 2em;
}
.icon.icon-stack-inverse {
	color: #fff;
}
.icon-color {
	color: rgba(55, 71, 79, .4);
}
.icon-color:hover, .icon-color:focus {
	color: rgba(55, 71, 79, .6);
}
.icon-color.active, .icon-color:active {
	color: #37474f;
}
.icon-color-alt {
	color: rgba(55, 71, 79, .6);
}
.icon-color-alt:hover, .icon-color-alt:focus {
	color: rgba(55, 71, 79, .8);
}
.icon-color-alt.active, .icon-color-alt:active {
	color: #37474f;
}
@-webkit-keyframes icon-spin {
	0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(359deg);
		        transform: rotate(359deg);
	}
}
@-o-keyframes icon-spin {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(359deg);
		     -o-transform: rotate(359deg);
		        transform: rotate(359deg);
	}
}
@keyframes icon-spin {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(359deg);
		     -o-transform: rotate(359deg);
		        transform: rotate(359deg);
	}
}
@-webkit-keyframes icon-spin-reverse {
	0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(-359deg);
		        transform: rotate(-359deg);
	}
}
@-o-keyframes icon-spin-reverse {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(-359deg);
		     -o-transform: rotate(-359deg);
		        transform: rotate(-359deg);
	}
}
@keyframes icon-spin-reverse {
	0% {
		-webkit-transform: rotate(0deg);
		     -o-transform: rotate(0deg);
		        transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(-359deg);
		     -o-transform: rotate(-359deg);
		        transform: rotate(-359deg);
	}
}
.hamburger {
	font-size: 20px;
	vertical-align: middle;
}
.hamburger, .hamburger:before, .hamburger .hamburger-bar, .hamburger:after {
	-webkit-transition: -webkit-transform .2s ease-in-out;
	     -o-transition:      -o-transform .2s ease-in-out;
	        transition: -webkit-transform .2s ease-in-out;
	        transition:         transform .2s ease-in-out;
	        transition:         transform .2s ease-in-out, -webkit-transform .2s ease-in-out, -o-transform .2s ease-in-out;
}
.hamburger:before, .hamburger:after {
	content: "";
}
.hamburger:before, .hamburger .hamburger-bar, .hamburger:after {
	display: block;
	width: 1em;
	height: .1em;
	margin: 0;
	background: #76838f;
	border-radius: 1px;
}
.navbar-default .hamburger:before, .navbar-default .hamburger .hamburger-bar, .navbar-default .hamburger:after {
	background: #76838f;
}
.navbar-inverse .hamburger:before, .navbar-inverse .hamburger .hamburger-bar, .navbar-inverse .hamburger:after {
	background: #fff;
}
.hamburger .hamburger-bar {
	margin: .2em 0;
}
.hamburger-close:before {
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	     -o-transform: rotate(45deg);
	        transform: rotate(45deg);
	-webkit-transform-origin: 8%;
	    -ms-transform-origin: 8%;
	     -o-transform-origin: 8%;
	        transform-origin: 8%;
}
.hamburger-close .hamburger-bar {
	opacity: 0;
}
.hamburger-close:after {
	-webkit-transform: rotate(-45deg);
	    -ms-transform: rotate(-45deg);
	     -o-transform: rotate(-45deg);
	        transform: rotate(-45deg);
	-webkit-transform-origin: 8%;
	    -ms-transform-origin: 8%;
	     -o-transform-origin: 8%;
	        transform-origin: 8%;
}
.hamburger-close.hided:before, .hamburger-close.collapsed:before {
	-webkit-transform: rotate(0);
	    -ms-transform: rotate(0);
	     -o-transform: rotate(0);
	        transform: rotate(0);
}
.hamburger-close.hided .hamburger-bar, .hamburger-close.collapsed .hamburger-bar {
	opacity: 1;
}
.hamburger-close.hided:after, .hamburger-close.collapsed:after {
	-webkit-transform: rotate(0);
	    -ms-transform: rotate(0);
	     -o-transform: rotate(0);
	        transform: rotate(0);
}
.hamburger-arrow-left.collapsed {
	-webkit-transform: rotate(180deg);
	    -ms-transform: rotate(180deg);
	     -o-transform: rotate(180deg);
	        transform: rotate(180deg);
}
.hamburger-arrow-left.collapsed:before {
	width: .6em;
	-webkit-transform: translate3d(.45em, .1em, 0) rotate(45deg);
	        transform: translate3d(.45em, .1em, 0) rotate(45deg);
}
.hamburger-arrow-left.collapsed .hamburger-bar {
	border-radius: .2em;
}
.hamburger-arrow-left.collapsed:after {
	width: .6em;
	-webkit-transform: translate3d(.45em, -.1em, 0) rotate(-45deg);
	        transform: translate3d(.45em, -.1em, 0) rotate(-45deg);
}
.counter {
	text-align: center;
}
.counter > .counter-number, .counter .counter-number-group {
	font-size: 20px;
	color: #37474f;
}
.counter-label {
	display: block;
}
.counter-icon {
	font-size: 20px;
}
.counter-lg > .counter-number, .counter-lg .counter-number-group {
	font-size: 40px;
}
.counter-lg .counter-icon {
	font-size: 40px;
}
.counter-md > .counter-number, .counter-md .counter-number-group {
	font-size: 30px;
}
.counter-md .counter-icon {
	font-size: 30px;
}
.counter-sm > .counter-number, .counter-sm .counter-number-group {
	font-size: 14px;
}
.counter-sm .counter-icon {
	font-size: 14px;
}
.counter-sm .counter-number-related + .counter-number, .counter-sm .counter-number + .counter-number-related {
	margin-left: 0;
}
.counter-inverse {
	color: #fff;
}
.counter-inverse > .counter-number, .counter-inverse .counter-number-group {
	color: #fff;
}
.counter-inverse .counter-icon {
	color: #fff;
}
.widget {
	position: relative;
	margin-bottom: 24px;
	background-color: #fff;
}
.widget .cover {
	width: 100%;
}
[class*="blocks-"] > li > .widget {
	margin-bottom: 0;
}
.widget-shadow {
	-webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
	        box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}
.widget {
	border-radius: 3px;
}
.widget .widget-header {
	border-radius: 3px 3px 0 0;
}
.widget .widget-header:last-child {
	border-radius: inherit;
}
.widget .widget-body:last-child {
	border-radius: 0 0 3px 3px;
}
.widget .widget-body:last-child .widget-body-footer {
	border-radius: 0 0 3px 3px;
}
.widget .widget-footer {
	border-radius: 0 0 3px 3px;
}
.widget .widget-footer:first-child {
	border-radiu: inherit;
}
.widget-body {
	position: relative;
	padding: 30px 25px;
}
.widget-body-footer {
	margin-top: 30px;
}
.widget-body-footer:before, .widget-body-footer:after {
	display: table;
	content: " ";
}
.widget-body-footer:after {
	clear: both;
}
.widget-content ul {
	padding: 0;
	margin: 0;
}
.widget-content li {
	list-style: none;
}
.widget-title {
	margin-top: 0;
	color: #37474f;
	text-transform: capitalize;
}
div.widget-title {
	font-size: 22px;
}
.overlay-panel .widget-title {
	color: #fff;
}
.widget > .widget-title {
	padding: 12px 20px;
}
.widget-metas {
	font-size: 12px;
	color: #a3afb7;
}
.widget-metas.type-link > a {
	position: relative;
	display: inline-block;
	padding: 3px 5px;
	color: #a3afb7;
}
.widget-metas.type-link > a:first-child {
	padding-left: 0;
}
.widget-metas.type-link > a:hover {
	color: #ccd5db;
}
.widget-metas.type-link > a + a:before {
	position: absolute;
	top: 10px;
	left: -2px;
	width: 3px;
	height: 3px;
	content: "";
	background-color: #a3afb7;
	border-radius: 50%;
}
.overlay-background .widget-time {
	color: #fff;
	opacity: .8;
}
.widget-category {
	font-size: 16px;
}
.widget-actions {
	margin-top: 10px;
	text-align: right;
}
.widget-actions a {
	display: inline-block;
	margin-right: 10px;
	color: #a3afb7;
	vertical-align: middle;
}
.widget-actions a.icon, .widget-actions a .icon {
	text-decoration: none;
}
.widget-actions a.icon + span, .widget-actions a .icon + span {
	margin-left: 2px;
}
.widget-actions a.active, .widget-actions a:hover, .widget-actions a:focus {
	color: #ccd5db;
	text-decoration: none;
}
.widget-actions a:last-child {
	margin-right: 0;
}
.widget-actions-sidebar {
	position: absolute;
	top: 20px;
	left: 20px;
	width: 60px;
}
.widget-actions-sidebar a {
	display: inline-block;
	width: 100%;
	height: 60px;
	margin-right: 0;
	text-align: center;
	border-right: 1px solid #e4eaec;
}
.widget-actions-sidebar a:before {
	display: inline-block;
	height: 100%;
	vertical-align: middle;
	content: "";
}
.widget-actions-sidebar a + a {
	border-top: 1px solid #e4eaec;
}
.widget-actions-sidebar + .widget-content {
	margin-left: 80px;
}
.widget-watermark {
	position: absolute;
	right: 0;
	bottom: 0;
	line-height: 1;
	opacity: .1;
}
.widget-watermark.darker {
	color: black;
}
.widget-watermark.lighter {
	color: white;
}
.widget-divider:after {
	display: block;
	width: 20px;
	height: 2px;
	margin: 15px auto;
	content: "";
	background-color: #fff;
}
.widget-left {
	position: absolute;
	top: 0;
	left: 0;
	width: 40%;
	height: 100%;
}
.widget-left + .widget-body {
	width: 60%;
	margin-left: 40%;
}
@media (max-width: 767px) {
	.widget-left {
		position: relative;
		width: 100%;
		height: 320px;
	}
	.widget-left + .widget-body {
		width: 100%;
		margin-left: 0;
	}
}
.panel-group .panel {
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.panel-group .panel-title {
	position: relative;
	padding: 15px 30px;
	font-size: 14px;
}
.panel-group .panel-title:before, .panel-group .panel-title:after {
	position: absolute;
	top: 15px;
	right: 30px;
	font-family: "Web Icons";
	-webkit-transition: all 300ms linear 0s;
	     -o-transition: all 300ms linear 0s;
	        transition: all 300ms linear 0s;
}
.panel-group .panel-title:before {
	content: "\f197";
}
.panel-group .panel-title:after {
	content: "\f199";
}
.panel-group .panel-title[aria-expanded="false"]:before {
	opacity: .4;
}
.panel-group .panel-title[aria-expanded="false"]:after {
	opacity: 0;
	-webkit-transform: rotate(-180deg);
	    -ms-transform: rotate(-180deg);
	     -o-transform: rotate(-180deg);
	        transform: rotate(-180deg);
}
.panel-group .panel-title[aria-expanded="true"]:before {
	opacity: 0;
	-webkit-transform: rotate(180deg);
	    -ms-transform: rotate(180deg);
	     -o-transform: rotate(180deg);
	        transform: rotate(180deg);
}
.panel-group .panel-title[aria-expanded="true"]:after {
	opacity: 1;
}
.panel-group .panel-title:hover, .panel-group .panel-title:focus {
	color: #76838f;
	text-decoration: none;
}
.panel-group .panel-title:focus {
	outline: none;
}
.panel-group .panel-heading + .panel-collapse {
	margin: 0;
}
.panel-group .panel-collapse .panel-body {
	padding: 15px 30px;
}
.panel-group .panel-heading + .panel-collapse .panel-body {
	border-top-color: transparent;
}
.panel-group .panel + .panel {
	margin-top: 10px;
}
.panel-group-continuous .panel {
	border-radius: 0;
}
.panel-group-continuous .panel:first-child {
	border-radius: 1px 1px 0 0;
}
.panel-group-continuous .panel:last-child {
	border-radius: 0 0 1px 1px;
}
.panel-group-continuous .panel + .panel {
	margin-top: 0;
	border-top: 1px solid #e4eaec;
}
.panel-group-simple .panel {
	background: transparent;
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.panel-group-simple .panel-title {
	padding-bottom: 10px;
	padding-left: 0;
}
.panel-group-simple .panel-title:before, .panel-group-simple .panel-title:after {
	right: 5px;
}
.panel-group-simple .panel-collapse .panel-body {
	padding-top: 10px;
	padding-right: 0;
	padding-left: 0;
}
.panel-group-simple .panel + .panel {
	margin-top: 0;
}
.cover {
	overflow: hidden;
}
.cover-background {
	height: 100%;
	background-repeat: no-repeat;
	background-position: center;
	-webkit-background-size: cover;
	        background-size: cover;
}
.cover-image {
	width: 100%;
}
.cover-quote {
	position: relative;
	padding-left: 35px;
	margin-bottom: 0;
	border-left: none;
}
.cover-quote:before, .cover-quote:after {
	position: absolute;
	top: -20px;
	font-size: 4em;
}
.cover-quote:before {
	left: 0;
	content: open-quote;
}
.cover-quote:after {
	right: 0;
	visibility: hidden;
	content: close-quote;
}
.cover-quote.blockquote-reverse {
	padding-right: 35px;
	padding-left: 20px;
	border-right: none;
}
.cover-quote.blockquote-reverse:before {
	right: 0;
	left: auto;
	content: close-quote;
}
.cover-gallery .carousel-inner img {
	width: 100%;
}
.cover-iframe {
	width: 100%;
	border: 0 none;
}
.overlay {
	position: relative;
	display: inline-block;
	width: 100%;
	max-width: 100%;
	margin: 0;
	overflow: hidden;
	vertical-align: middle;
	-webkit-transform: translateZ(0);
	        transform: translateZ(0);
}
.overlay > :first-child, .overlay .overlay-figure {
	width: 100%;
	max-width: 100%;
	margin-bottom: 0;
}
.overlay-panel {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	padding: 20px;
	color: #fff;
}
.overlay-panel a:not([class]) {
	color: inherit;
	text-decoration: underline;
}
.overlay-panel > :last-child {
	margin-bottom: 0;
}
.overlay-panel h1, .overlay-panel h2, .overlay-panel h3, .overlay-panel h4, .overlay-panel h5, .overlay-panel h6 {
	color: inherit;
}
.overlay-hover:not(:hover) .overlay-panel:not(.overlay-background-fixed) {
	opacity: 0;
}
.overlay-background {
	background: rgba(0, 0, 0, .5);
}
.overlay-image {
	width: 100%;
	max-width: 100%;
	padding: 0;
}
.overlay-shade {
	background: rgba(0, 0, 0, 0) -webkit-gradient(linear, left top, left bottom, color-stop(50%, rgba(255, 255, 255, 0)), color-stop(90%, rgba(255, 255, 255, .87)), to(#fff)) repeat scroll 0 0;
	background: rgba(0, 0, 0, 0) -webkit-linear-gradient(top, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, .87) 90%, #fff 100%) repeat scroll 0 0;
	background: rgba(0, 0, 0, 0) -o-linear-gradient(top, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, .87) 90%, #fff 100%) repeat scroll 0 0;
	background: rgba(0, 0, 0, 0) linear-gradient(to bottom, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, .87) 90%, #fff 100%) repeat scroll 0 0;
}
/* Position modifiers
 ========================================================================== */
.overlay-top {
	bottom: auto;
}
.overlay-bottom {
	top: auto;
}
.overlay-left {
	right: auto;
}
.overlay-right {
	left: auto;
}
/* Sub-object `overlay-icon`
 ========================================================================== */
.overlay-icon {
	font-size: 0;
	text-align: center;
}
.overlay-icon:before {
	display: inline-block;
	height: 100%;
	vertical-align: middle;
	content: "";
}
.overlay-icon .icon {
	display: inline-block;
	width: 32px;
	height: 32px;
	margin-right: 10px;
	margin-left: 10px;
	font-size: 32px;
	line-height: 1;
	color: #fff;
	text-decoration: none;
}
.overlay-anchor {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}
.overlay-fade, .overlay-scale, .overlay-spin, .overlay-grayscale, .overlay-blur, [class*="overlay-slide"] {
	-webkit-transition-timing-function: ease-out;
	     -o-transition-timing-function: ease-out;
	        transition-timing-function: ease-out;
	-webkit-transition-duration: .3s;
	     -o-transition-duration: .3s;
	        transition-duration: .3s;
	-webkit-transition-property: opacity;
	     -o-transition-property: opacity;
	        transition-property: opacity;
}
.overlay-fade {
	opacity: .7;
}
.overlay-hover:hover .overlay-fade {
	opacity: 1;
}
.overlay-scale {
	-webkit-transform: scale(1);
	    -ms-transform: scale(1);
	     -o-transform: scale(1);
	        transform: scale(1);
}
.overlay-hover:hover .overlay-scale {
	-webkit-transform: scale(1.1);
	    -ms-transform: scale(1.1);
	     -o-transform: scale(1.1);
	        transform: scale(1.1);
}
.overlay-spin {
	-webkit-transform: scale(1) rotate(0deg);
	    -ms-transform: scale(1) rotate(0deg);
	     -o-transform: scale(1) rotate(0deg);
	        transform: scale(1) rotate(0deg);
}
.overlay-hover:hover .overlay-spin {
	-webkit-transform: scale(1.1) rotate(3deg);
	    -ms-transform: scale(1.1) rotate(3deg);
	     -o-transform: scale(1.1) rotate(3deg);
	        transform: scale(1.1) rotate(3deg);
}
.overlay-grayscale {
	        filter: grayscale(100%);

	-webkit-filter: grayscale(100%);
}
.overlay-hover:hover .overlay-grayscale {
	        filter: grayscale(0%);

	-webkit-filter: grayscale(0%);
}
[class*="overlay-slide"] {
	opacity: 0;
}
.overlay-slide-top {
	-webkit-transform: translateY(-100%);
	    -ms-transform: translateY(-100%);
	     -o-transform: translateY(-100%);
	        transform: translateY(-100%);
}
.overlay-slide-bottom {
	-webkit-transform: translateY(100%);
	    -ms-transform: translateY(100%);
	     -o-transform: translateY(100%);
	        transform: translateY(100%);
}
.overlay-slide-left {
	-webkit-transform: translateX(-100%);
	    -ms-transform: translateX(-100%);
	     -o-transform: translateX(-100%);
	        transform: translateX(-100%);
}
.overlay-slide-right {
	-webkit-transform: translateX(100%);
	    -ms-transform: translateX(100%);
	     -o-transform: translateX(100%);
	        transform: translateX(100%);
}
.overlay-hover:hover [class*="overlay-slide"] {
	opacity: 1;
	-webkit-transform: translateX(0) translateY(0);
	    -ms-transform: translateX(0) translateY(0);
	     -o-transform: translateX(0) translateY(0);
	        transform: translateX(0) translateY(0);
}
.comments {
	padding: 0;
	margin: 0;
}
.comments .comment {
	border: none;
	border-bottom: 1px solid #e4eaec;
}
.comments .comment .comment:first-child {
	border-top: 1px solid #e4eaec;
}
.comments .comment .comment:last-child {
	border-bottom: none;
}
.comment {
	padding: 15px 0;
	margin: 0;
}
.comment .comment {
	padding-bottom: 15px;
	margin-top: 15px;
}
.comment .comment:last-child {
	padding-bottom: 0;
}
.comment-author, .comment-author:hover, .comment-author:focus {
	color: #37474f;
}
.comment-meta {
	display: inline-block;
	margin-left: 5px;
	font-size: 12px;
	color: #a3afb7;
}
.comment-content {
	margin-top: 5px;
}
.comment-content p {
	margin-bottom: 10px;
}
.comment-actions {
	margin-top: 10px;
	text-align: right;
}
.comment-actions a {
	display: inline-block;
	margin-right: 10px;
	vertical-align: middle;
}
.comment-actions a.icon {
	text-decoration: none;
}
.comment-actions a:last-child {
	margin-right: 0;
}
.comment-reply {
	margin: 10px 0 10px;
}
.comment-reply .form-group:last-child {
	margin-bottom: 0;
}
.chat-box {
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: #fff;
}
.chats {
	padding: 30px 15px;
}
.chat-avatar {
	float: right;
}
.chat-avatar .avatar {
	width: 30px;
}
.chat-body {
	display: block;
	margin: 10px 30px 0 0;
	overflow: hidden;
}
.chat-body:first-child {
	margin-top: 0;
}
.chat-content {
	position: relative;
	display: block;
	float: right;
	padding: 5px 10px;
	margin: 0 20px 10px 0;
	clear: both;
	color: #fff;
	background-color: #f96868;
	border-radius: 4px;
}
.chat-content:before {
	position: absolute;
	top: 10px;
	right: -10px;
	width: 0;
	height: 0;
	content: "";
	border: 5px solid transparent;
	border-left-color: #f96868;
}
.chat-content > p {
	margin: 0;
}
.chat-content + .chat-content:before {
	border-color: transparent;
}
.chat-time {
	display: block;
	font-size: 8px;
	color: rgba(255, 255, 255, .6);
}
.chat-left .chat-avatar {
	float: left;
}
.chat-left .chat-body {
	margin-right: 0;
	margin-left: 30px;
}
.chat-left .chat-content {
	float: left;
	margin: 0 0 10px 20px;
	color: #76838f;
	background-color: #dfe9ef;
}
.chat-left .chat-content:before {
	right: auto;
	left: -10px;
	border-right-color: #dfe9ef;
	border-left-color: transparent;
}
.chat-left .chat-content + .chat-content:before {
	border-color: transparent;
}
.chat-left .chat-time {
	color: #a3afb7;
}
.steps {
	margin-bottom: 22px;
}
.steps.row {
	display: block;
	margin-right: 0;
	margin-left: 0;
}
.step {
	position: relative;
	padding: 10px 15px;
	margin: 0;
	font-size: inherit;
	color: #a3afb7;
	vertical-align: top;
	background-color: #f3f7f9;
	border-radius: 0;
}
.step-icon {
	float: left;
	margin-top: 4px;
	margin-right: .5em;
	font-size: 16px;
}
.step-number {
	position: absolute;
	top: 50%;
	left: 15px;
	width: 32px;
	height: 32px;
	font-size: 20px;
	line-height: 32px;
	color: #fff;
	text-align: center;
	background: #e4eaec;
	border-radius: 50%;
	-webkit-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	     -o-transform: translateY(-50%);
	        transform: translateY(-50%);
}
.step-number ~ .step-desc {
	min-height: 32px;
	margin-left: 42px;
}
.step-title {
	margin-bottom: 0;
	font-size: 16px;
	color: #526069;
}
.step-desc {
	text-align: left;
}
.step-desc p {
	margin-bottom: 0;
}
.steps-vertical .step {
	display: block;
	padding: 12px 15px;
}
.steps-vertical .step[class*="col-"] {
	float: none;
	width: 100%;
}
.step.current {
	color: #fff;
	background-color: #f96868;
}
.step.current .step-title {
	color: #fff;
}
.step.current .step-number {
	color: #f96868;
	background-color: #fff;
}
.step.disabled {
	color: #ccd5db;
	pointer-events: none;
	cursor: auto;
}
.step.disabled .step-title {
	color: #ccd5db;
}
.step.disabled .step-number {
	background-color: #ccd5db;
}
.step.error {
	color: #fff;
	background-color: #f96868;
}
.step.error .step-title {
	color: #fff;
}
.step.error .step-number {
	color: #f96868;
	background-color: #fff;
}
.step.done {
	color: #fff;
	background-color: #46be8a;
}
.step.done .step-title {
	color: #fff;
}
.step.done .step-number {
	color: #46be8a;
	background-color: #fff;
}
.steps-lg .step {
	padding: 15px 15px;
	font-size: 14px;
}
.steps-lg .step-icon {
	font-size: 18px;
}
.steps-lg .step-title {
	font-size: 18px;
}
.steps-lg .step-number {
	width: 46px;
	height: 46px;
	font-size: 28px;
	line-height: 46px;
}
.steps-lg .step-number ~ .step-desc {
	min-height: 46px;
	margin-left: 56px;
}
.steps-sm .step {
	font-size: 12px;
}
.steps-sm .step-icon {
	font-size: 18px;
}
.steps-sm .step-title {
	font-size: 18px;
}
.steps-sm .step-number {
	width: 30px;
	height: 30px;
	font-size: 24px;
	line-height: 30px;
}
.steps-sm .step-number ~ .step-desc {
	min-height: 30px;
	margin-left: 40px;
}
.steps-sm .step-icon {
	margin-top: 3px;
}
.steps-xs .step {
	font-size: 12px;
}
.steps-xs .step-icon {
	font-size: 16px;
}
.steps-xs .step-title {
	font-size: 16px;
}
.steps-xs .step-number {
	width: 24px;
	height: 24px;
	font-size: 20px;
	line-height: 24px;
}
.steps-xs .step-number ~ .step-desc {
	min-height: 24px;
	margin-left: 34px;
}
.steps-xs .step-icon {
	margin-top: 3px;
}
.pearls {
	margin-bottom: 22px;
}
.pearls.row {
	display: block;
}
.pearl {
	position: relative;
	padding: 0;
	margin: 0;
	text-align: center;
}
.pearl:before, .pearl:after {
	position: absolute;
	top: 18px;
	z-index: 0;
	width: 50%;
	height: 4px;
	content: "";
	background-color: #f3f7f9;
}
.pearl:before {
	left: 0;
}
.pearl:after {
	right: 0;
}
.pearl:first-child:before, .pearl:last-child:after {
	display: none !important;
}
.pearl-number, .pearl-icon {
	position: relative;
	z-index: 1;
	display: inline-block;
	width: 36px;
	height: 36px;
	line-height: 32px;
	color: #fff;
	text-align: center;
	background: #ccd5db;
	border: 2px solid #ccd5db;
	border-radius: 50%;
}
.pearl-number {
	font-size: 18px;
}
.pearl-icon {
	font-size: 18px;
}
.pearl-title {
	display: block;
	margin-top: .5em;
	margin-bottom: 0;
	/* for IE */
	overflow: hidden;
	font-size: 16px;
	color: #526069;
	text-overflow: ellipsis;
	word-wrap: normal;
	/* for IE */
	white-space: nowrap;
}
.pearl.current:before, .pearl.current:after {
	background-color: #f96868;
}
.pearl.current .pearl-number, .pearl.current .pearl-icon {
	color: #f96868;
	background-color: #fff;
	border-color: #f96868;
	-webkit-transform: scale(1.3);
	    -ms-transform: scale(1.3);
	     -o-transform: scale(1.3);
	        transform: scale(1.3);
}
.pearl.disabled {
	pointer-events: none;
	cursor: auto;
}
.pearl.disabled:before, .pearl.disabled:after {
	background-color: #f3f7f9;
}
.pearl.disabled .pearl-number, .pearl.disabled .pearl-icon {
	color: #fff;
	background-color: #ccd5db;
	border-color: #ccd5db;
}
.pearl.error:before {
	background-color: #f96868;
}
.pearl.error:after {
	background-color: #f3f7f9;
}
.pearl.error .pearl-number, .pearl.error .pearl-icon {
	color: #f96868;
	background-color: #fff;
	border-color: #f96868;
}
.pearl.done:before, .pearl.done:after {
	background-color: #f96868;
}
.pearl.done .pearl-number, .pearl.done .pearl-icon {
	color: #fff;
	background-color: #f96868;
	border-color: #f96868;
}
.pearls-lg .pearl:before, .pearls-lg .pearl:after {
	top: 20px;
}
.pearls-lg .pearl-title {
	font-size: 18px;
}
.pearls-lg .pearl-number, .pearls-lg .pearl-icon {
	width: 40px;
	height: 40px;
	line-height: 36px;
}
.pearls-lg .pearl-icon {
	font-size: 20px;
}
.pearls-lg .pearl-number {
	font-size: 20px;
}
.pearls-sm .pearl:before, .pearls-sm .pearl:after {
	top: 16px;
}
.pearls-sm .pearl-title {
	font-size: 14px;
}
.pearls-sm .pearl-number, .pearls-sm .pearl-icon {
	width: 32px;
	height: 32px;
	line-height: 28px;
}
.pearls-sm .pearl-number {
	font-size: 16px;
}
.pearls-sm .pearl-icon {
	font-size: 14px;
}
.pearls-xs .pearl:before, .pearls-xs .pearl:after {
	top: 12px;
	height: 2px;
}
.pearls-xs .pearl-title {
	font-size: 12px;
}
.pearls-xs .pearl-number, .pearls-xs .pearl-icon {
	width: 24px;
	height: 24px;
	line-height: 20px;
}
.pearls-xs .pearl-number {
	font-size: 12px;
}
.pearls-xs .pearl-icon {
	font-size: 12px;
}
.timeline {
	position: relative;
	padding: 0;
	margin-bottom: 22px;
	list-style: none;
	background: transparent;
}
.timeline:before {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 50%;
	width: 2px;
	margin-left: -1px;
	content: "";
	background-color: #e4eaec;
}
.timeline:after {
	display: table;
	content: "";
}
.timeline:after {
	clear: both;
}
.timeline > li {
	position: relative;
	display: block;
	float: left;
	width: 50%;
	padding-right: 40px;
	margin-bottom: 60px;
}
.timeline > li:not(.timeline-period):before, .timeline > li:not(.timeline-period):after {
	display: table;
	content: " ";
}
.timeline > li:not(.timeline-period):after {
	clear: both;
}
.timeline > li.timeline-period {
	position: relative;
	z-index: 6;
	display: block;
	float: none;
	width: 200px;
	padding: 25px 10px;
	margin: 20px auto 30px;
	clear: both;
	font-size: 26px;
	text-align: center;
	text-transform: uppercase;
	background: #f1f4f5;
}
.timeline > li.timeline-reverse {
	float: right;
	padding-right: 0;
	padding-left: 40px;
}
.timeline > li:last-child {
	margin-bottom: 0;
}
.timeline:not(.timeline-single) > li:not(.timeline-period):first-child + .timeline-item, .timeline:not(.timeline-single) > li.timeline-period + .timeline-item + .timeline-item {
	margin-top: 90px;
}
.timeline-content {
	float: left;
	width: 100%;
	overflow: hidden;
	clear: left;
}
.timeline-reverse .timeline-content {
	float: right;
	clear: right;
}
.timeline-content > .widget {
	margin: 0;
}
.timeline-dot {
	position: absolute;
	top: 7.5px;
	right: 0;
	z-index: 11;
	color: #fff;
	text-align: center;
	cursor: pointer;
	background-color: #f96868;
	border-radius: 50%;
}
.timeline-reverse .timeline-dot {
	right: auto;
	left: 0;
	margin-right: 0;
}
.timeline .timeline-dot {
	width: 14px;
	height: 14px;
	margin-right: -7px;
	line-height: 14px;
}
.timeline .timeline-reverse .timeline-dot {
	margin-left: -7px;
}
.timeline.timeline-single {
	margin-left: 7px;
}
.timeline.timeline-single .timeline-dot {
	margin-left: -7px;
}
@media screen and (max-width: 767px) {
	.timeline {
		margin-left: 7px;
	}
	.timeline .timeline-dot {
		margin-left: -7px;
	}
}
.timeline-info {
	float: right;
	padding: 0 20px;
	margin-bottom: 22px;
	line-height: 28px;
	text-align: center;
	background: #e4eaec;
	border: 1px solid #e4eaec;
	border-radius: 20px;
}
.timeline-reverse .timeline-info {
	float: left;
}
.timeline-footer {
	position: absolute;
	right: 0;
	bottom: -30px;
	margin-right: 55px;
}
.timeline-footer .icon {
	margin-right: .3em;
}
.timeline-reverse .timeline-footer {
	right: auto;
	left: 0;
	margin-right: 0;
	margin-left: 55px;
}
.timeline-reverse + .timeline-reverse {
	margin-top: 0;
}
@media screen and (max-width: 767px) {
	.timeline:before {
		left: 0;
	}
	.timeline > li, .timeline li.timeline-reverse {
		float: none;
		width: 100%;
		padding-right: 0;
		padding-left: 40px;
		margin-top: 0;
		margin-bottom: 60px;
	}
	.timeline-content {
		float: none;
	}
	.timeline-dot {
		right: auto;
		left: 0;
		margin-right: 0;
		margin-left: -7px;
	}
	.timeline-info {
		display: inline-block;
		float: none;
	}
	.timeline-footer {
		right: auto;
		bottom: -26px;
		left: 0;
		margin-right: 0;
		margin-left: 40px;
	}
}
.timeline-single:before {
	left: 0;
}
.timeline-single > li {
	float: none;
	width: 100%;
	padding-right: 0;
	padding-left: 40px;
	margin-bottom: 60px;
}
.timeline-single .timeline-content {
	float: none;
}
.timeline-single .timeline-dot {
	right: auto;
	left: 0;
	margin-right: 0;
	margin-left: -7px;
}
.timeline-single .timeline-info {
	float: left;
}
.timeline-single .timeline-footer {
	right: auto;
	bottom: -26px;
	left: 0;
	margin-right: 0;
	margin-left: 40px;
}
.timeline-icon .timeline-dot {
	width: 40px;
	height: 40px;
	margin-right: -20px;
	line-height: 40px;
}
.timeline-icon .timeline-reverse .timeline-dot {
	margin-left: -20px;
}
.timeline-icon.timeline-single {
	margin-left: 20px;
}
.timeline-icon.timeline-single .timeline-dot {
	margin-left: -20px;
}
@media screen and (max-width: 767px) {
	.timeline-icon {
		margin-left: 20px;
	}
	.timeline-icon .timeline-dot {
		margin-left: -20px;
	}
}
.timeline-icon .timeline-dot {
	top: -5.5px;
}
.timeline-avatar .timeline-dot {
	width: 40px;
	height: 40px;
	margin-right: -20px;
	line-height: 40px;
}
.timeline-avatar .timeline-reverse .timeline-dot {
	margin-left: -20px;
}
.timeline-avatar.timeline-single {
	margin-left: 20px;
}
.timeline-avatar.timeline-single .timeline-dot {
	margin-left: -20px;
}
@media screen and (max-width: 767px) {
	.timeline-avatar {
		margin-left: 20px;
	}
	.timeline-avatar .timeline-dot {
		margin-left: -20px;
	}
}
.timeline-avatar-sm .timeline-dot {
	width: 30px;
	height: 30px;
	margin-right: -15px;
	line-height: 30px;
}
.timeline-avatar-sm .timeline-reverse .timeline-dot {
	margin-left: -15px;
}
.timeline-avatar-sm.timeline-single {
	margin-left: 15px;
}
.timeline-avatar-sm.timeline-single .timeline-dot {
	margin-left: -15px;
}
@media screen and (max-width: 767px) {
	.timeline-avatar-sm {
		margin-left: 15px;
	}
	.timeline-avatar-sm .timeline-dot {
		margin-left: -15px;
	}
}
.timeline-avatar-lg .timeline-dot {
	width: 50px;
	height: 50px;
	margin-right: -25px;
	line-height: 50px;
}
.timeline-avatar-lg .timeline-reverse .timeline-dot {
	margin-left: -25px;
}
.timeline-avatar-lg.timeline-single {
	margin-left: 25px;
}
.timeline-avatar-lg.timeline-single .timeline-dot {
	margin-left: -25px;
}
@media screen and (max-width: 767px) {
	.timeline-avatar-lg {
		margin-left: 25px;
	}
	.timeline-avatar-lg .timeline-dot {
		margin-left: -25px;
	}
}
.timeline-simple .timeline-dot {
	top: 0;
	margin-top: 10px;
}
.timeline-feed .timeline-dot {
	width: 30px;
	height: 30px;
	margin-right: -15px;
	line-height: 30px;
}
.timeline-feed .timeline-reverse .timeline-dot {
	margin-left: -15px;
}
.timeline-feed.timeline-single {
	margin-left: 15px;
}
.timeline-feed.timeline-single .timeline-dot {
	margin-left: -15px;
}
@media screen and (max-width: 767px) {
	.timeline-feed {
		margin-left: 15px;
	}
	.timeline-feed .timeline-dot {
		margin-left: -15px;
	}
}
.timeline-feed.timeline-simple .timeline-dot {
	margin-top: 5px;
}
.timeline-feed > li {
	padding-right: 30px;
	margin-bottom: 22px;
}
.timeline-feed > li.timeline-reverse {
	padding-left: 30px;
}
.timeline-feed.timeline-single > li {
	padding-left: 30px;
}
@media screen and (max-width: 767px) {
	.timeline-feed > li {
		padding-right: 30px;
		margin-bottom: 22px;
	}
}
@media screen and (max-width: 767px) {
}
.testimonial {
	margin: 3px 3px 22px;
}
.testimonial-ul {
	padding: 0;
	margin: 0;
	list-style: none;
}
.testimonial-item {
	float: left;
	padding: 0 15px 30px;
	margin: 0;
}
.testimonial-content {
	position: relative;
	padding: 15px 20px;
	margin-top: 10px;
	margin-bottom: 25px;
	background-color: #f3f7f9;
	border-radius: 3px;
}
.testimonial-content:before {
	position: absolute;
	bottom: -7px;
	left: 33px;
	display: block;
	width: 14px;
	height: 14px;
	content: "";
	background-color: #f3f7f9;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	     -o-transform: rotate(45deg);
	        transform: rotate(45deg);
}
.testimonial-content > p:last-child {
	margin-bottom: 0;
}
.testimonial-image {
	position: relative;
	float: left;
	margin-top: 5px;
	margin-left: 20px;
}
.testimonial-author {
	display: block;
	margin-left: 75px;
	font-size: 18px;
}
.testimonial-company {
	display: block;
	margin-left: 75px;
	font-size: 12px;
	opacity: .8;
}
.testimonial-control a {
	color: #ccd5db;
}
.testimonial-control a:hover {
	color: #fa7a7a;
	text-decoration: none;
}
.testimonial-reverse .testimonial-content:before {
	right: 33px;
	left: auto;
}
.testimonial-reverse .testimonial-image {
	float: right;
	margin-right: 20px;
	margin-left: 0;
}
.testimonial-reverse .testimonial-author, .testimonial-reverse .testimonial-company {
	margin-right: 75px;
	margin-left: 0;
	text-align: right;
}
.testimonial-top .testimonial-item {
	padding: 30px 15px 0;
}
.testimonial-top .testimonial-content {
	margin-top: 30px;
	margin-bottom: 10px;
}
.testimonial-top .testimonial-content:before {
	top: -7px;
	bottom: auto;
}
.testimonial.carousel {
	position: relative;
	width: 100%;
	overflow: hidden;
}
.testimonial.carousel .testimonial-item {
	position: relative;
	display: none;
	width: 100%;
	padding: 10px;
	-webkit-transition: left .6s ease-in-out 0s;
	     -o-transition: left .6s ease-in-out 0s;
	        transition: left .6s ease-in-out 0s;
}
.testimonial.carousel .testimonial-item.active, .testimonial.carousel .testimonial-item.next, .testimonial.carousel .testimonial-item.prev {
	display: block;
}
.testimonial.carousel .testimonial-item.next, .testimonial.carousel .testimonial-item.prev {
	position: absolute;
	top: 0;
	width: 100%;
}
.testimonial.carousel .testimonial-item.next {
	left: 100%;
}
.testimonial.carousel .testimonial-item.prev {
	left: -100%;
}
.testimonial.carousel .testimonial-item.next.left, .testimonial.carousel .testimonial-item.prev.right {
	left: 0;
}
.testimonial.carousel .testimonial-item.active {
	left: 0;
}
.testimonial.carousel .testimonial-item.active.left {
	left: -100%;
}
.testimonial.carousel .testimonial-item.active.right {
	left: 100%;
}
.testimonial.carousel .testimonial-content {
	padding: 10px;
}
.testimonial.carousel .testimonial-control {
	position: absolute;
	right: 10px;
	bottom: 20px;
}
.testimonial.carousel .testimonial-control > * {
	margin-left: 10px;
}
.testimonial.carousel.testimonial-reverse .testimonial-control {
	right: auto;
	left: 10px;
}
.testimonial.carousel.testimonial-reverse .testimonial-control > * {
	margin-right: 10px;
	margin-left: 0;
}
.testimonial.carousel.testimonial-top .testimonial-control {
	top: 20px;
	bottom: auto;
}
.pricing-list {
	margin-bottom: 22px;
	text-align: center;
	border: 1px solid #e4eaec;
	border-radius: 3px;
}
.pricing-list [class^="bg-"], .pricing-list [class*="bg-"], .pricing-list [class^="bg-"] *, .pricing-list [class*="bg-"] * {
	color: #fff;
}
.pricing-list .pricing-header {
	border-bottom: 1px solid #e4eaec;
	border-radius: 3px 3px 0 0;
}
.pricing-list .pricing-title {
	padding: 15px 30px;
	font-size: 14px;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 2px;
	border-radius: 3px 3px 0 0;
}
.pricing-list .pricing-price {
	padding: 20px 30px;
	margin: 0;
	font-size: 33px;
	font-weight: 700;
	color: #37474f;
}
.pricing-list .pricing-period {
	font-size: 14px;
	font-weight: 400;
}
.pricing-list .pricing-features {
	padding: 0 18px;
	margin: 0;
}
.pricing-list .pricing-features li {
	display: block;
	padding: 15px;
	list-style: none;
	border-top: 1px dashed #e4eaec;
}
.pricing-list .pricing-features li:first-child {
	border-top: none;
}
.pricing-list .pricing-footer {
	padding: 30px;
	border-radius: 0 0 3px 3px;
}
.pricing-table {
	padding-top: 30px;
	text-align: center;
}
.pricing-table:before, .pricing-table:after {
	display: table;
	content: " ";
}
.pricing-table:after {
	clear: both;
}
.pricing-table [class*="pricing-column"] {
	float: left;
	width: 100%;
	margin-bottom: 30px;
	background-color: #f3f7f9;
	border: 1px solid #e4eaec;
	border-right: none;
}
.pricing-table [class*="pricing-column"]:last-child {
	border-right: 1px solid #e4eaec;
}
.pricing-table [class*="pricing-column"].featured {
	position: relative;
	margin-right: -1px;
	background-color: #fff;
	border-right: 1px solid #e4eaec;
}
.pricing-table .pricing-header {
	padding-bottom: 24px;
	margin: 30px 30px 25px;
	border-bottom: 1px solid #e4eaec;
}
.pricing-table .pricing-price {
	font-size: 48px;
}
.pricing-table .pricing-currency {
	display: inline-block;
	margin-top: 10px;
	margin-right: -10px;
	font-size: 20px;
	vertical-align: top;
}
.pricing-table .pricing-period {
	font-size: 16px;
}
.pricing-table .pricing-title {
	font-size: 20px;
	text-transform: uppercase;
	letter-spacing: 2px;
}
.pricing-table .pricing-features {
	padding: 0;
	margin: 0;
}
.pricing-table .pricing-features li {
	display: block;
	margin-bottom: 20px;
	font-size: 14px;
	list-style: none;
}
.pricing-table .pricing-footer {
	padding: 20px 0;
	margin: 25px 30px 30px;
}
@media screen and (min-width: 768px) {
	.pricing-table .pricing-column-three {
		width: 33.33%;
	}
	.pricing-table .pricing-column-three.featured {
		top: -30px;
		padding-top: 30px;
		padding-bottom: 30px;
		margin-bottom: -30px;
	}
	.pricing-table .pricing-column-four {
		width: 50%;
	}
	.pricing-table .pricing-column-five {
		width: 50%;
	}
}
@media screen and (min-width: 1200px) {
	.pricing-table .pricing-column-four {
		width: 25%;
	}
	.pricing-table .pricing-column-five {
		width: 20%;
	}
	.pricing-table .pricing-column-four.featured, .pricing-table .pricing-column-five.featured {
		top: -30px;
		padding-top: 30px;
		padding-bottom: 30px;
		margin-bottom: -30px;
	}
}
.rating {
	display: inline-block;
	margin: 0 .5rem 0 0;
	font-size: 0;
	vertical-align: middle;
}
.rating:before {
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
	content: "";
}
.rating.hover .icon.active {
	opacity: .5;
}
.rating .icon {
	width: 1em;
	height: auto;
	padding: 0;
	margin: 0 10px 0 0;
	font-size: 14px;
	color: #ccd5db;
	vertical-align: middle;
	cursor: pointer;
}
.rating .icon:before {
	-webkit-transition: color .3s ease,
	opacity .3s ease;
	     -o-transition: color .3s ease,
	opacity .3s ease;
	        transition: color .3s ease,
	opacity .3s ease;
}
.rating .icon.active {
	color: #f2a654 !important;
}
.rating .icon.active.hover {
	color: #f2a654 !important;
	opacity: 1;
}
.rating .icon.hover {
	color: #f2a654 !important;
	opacity: 1;
}
.rating .icon:last-child {
	margin-right: 0;
}
.rating-disabled .icon {
	cursor: default;
}
.rating-sm .icon {
	font-size: 12px;
}
.rating-lg .icon {
	font-size: 18px;
}
.ribbon {
	position: absolute;
	top: -3px;
	left: -3px;
	width: 150px;
	height: 150px;
	text-align: center;
	background-color: transparent;
}
.ribbon-inner {
	position: absolute;
	top: 16px;
	left: 0;
	display: inline-block;
	height: 30px;
	padding-right: 20px;
	padding-left: 20px;
	line-height: 30px;
	color: #fff;
	white-space: nowrap;
	background-color: #526069;
}
.ribbon-inner .icon {
	font-size: 16px;
}
.ribbon-lg .ribbon-inner {
	height: 38px;
	font-size: 18px;
	line-height: 38px;
}
.ribbon-sm .ribbon-inner {
	height: 26px;
	font-size: 12px;
	line-height: 26px;
}
.ribbon-xs .ribbon-inner {
	height: 22px;
	font-size: 12px;
	line-height: 22px;
}
.ribbon-vertical .ribbon-inner {
	top: 0;
	left: 16px;
	width: 30px;
	height: 60px;
	padding: 15px 0;
}
.ribbon-vertical.ribbon-xs .ribbon-inner {
	width: 22px;
	height: 50px;
}
.ribbon-vertical.ribbon-sm .ribbon-inner {
	width: 26px;
	height: 55px;
}
.ribbon-vertical.ribbon-lg .ribbon-inner {
	width: 38px;
	height: 70px;
}
.ribbon-reverse {
	right: -3px;
	left: auto;
}
.ribbon-reverse .ribbon-inner {
	right: 0;
	left: auto;
}
.ribbon-reverse.ribbon-vertical .ribbon-inner {
	right: 16px;
}
.ribbon-bookmark .ribbon-inner {
	-webkit-box-shadow: none;
	        box-shadow: none;
}
.ribbon-bookmark .ribbon-inner:before {
	position: absolute;
	top: 0;
	left: 100%;
	display: block;
	width: 0;
	height: 0;
	content: "";
	border: 15px solid #526069;
	border-right: 10px solid transparent;
}
.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	top: 100%;
	left: 0;
	margin-top: -15px;
	border-right: 15px solid #526069;
	border-bottom: 10px solid transparent;
}
.ribbon-bookmark.ribbon-vertical.ribbon-xs .ribbon-inner:before {
	margin-top: -11px;
}
.ribbon-bookmark.ribbon-vertical.ribbon-sm .ribbon-inner:before {
	margin-top: -13px;
}
.ribbon-bookmark.ribbon-vertical.ribbon-lg .ribbon-inner:before {
	margin-top: -19px;
}
.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	right: 100%;
	left: auto;
	border-right: 15px solid #526069;
	border-left: 10px solid transparent;
}
.ribbon-bookmark.ribbon-reverse.ribbon-vertical .ribbon-inner:before {
	right: auto;
	left: 0;
	border-right-color: #526069;
	border-bottom-color: transparent;
	border-left: 15px solid #526069;
}
.ribbon-bookmark.ribbon-xs .ribbon-inner:before {
	border-width: 11px;
}
.ribbon-bookmark.ribbon-sm .ribbon-inner:before {
	border-width: 13px;
}
.ribbon-bookmark.ribbon-lg .ribbon-inner:before {
	border-width: 19px;
}
.ribbon-badge {
	top: -2px;
	left: -2px;
	overflow: hidden;
}
.ribbon-badge .ribbon-inner {
	left: -40px;
	width: 100%;
	-webkit-transform: rotate(-45deg);
	    -ms-transform: rotate(-45deg);
	     -o-transform: rotate(-45deg);
	        transform: rotate(-45deg);
}
.ribbon-badge.ribbon-reverse {
	right: -2px;
	left: auto;
}
.ribbon-badge.ribbon-reverse .ribbon-inner {
	right: -40px;
	left: auto;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	     -o-transform: rotate(45deg);
	        transform: rotate(45deg);
}
.ribbon-badge.ribbon-bottom {
	top: auto;
	bottom: -2px;
}
.ribbon-badge.ribbon-bottom .ribbon-inner {
	top: auto;
	bottom: 16px;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	     -o-transform: rotate(45deg);
	        transform: rotate(45deg);
}
.ribbon-badge.ribbon-bottom.ribbon-reverse .ribbon-inner {
	-webkit-transform: rotate(-45deg);
	    -ms-transform: rotate(-45deg);
	     -o-transform: rotate(-45deg);
	        transform: rotate(-45deg);
}
.ribbon-corner {
	top: 0;
	left: 0;
	overflow: hidden;
}
.ribbon-corner .ribbon-inner {
	top: 0;
	left: 0;
	width: 40px;
	height: 35px;
	padding: 0;
	line-height: 35px;
	background-color: transparent;
}
.ribbon-corner .ribbon-inner:before {
	position: absolute;
	top: 0;
	left: 0;
	width: 0;
	height: 0;
	content: "";
	border: 30px solid transparent;
	border-top-color: #526069;
	border-left-color: #526069;
}
.ribbon-corner.ribbon-reverse {
	right: 0;
	left: auto;
}
.ribbon-corner.ribbon-reverse .ribbon-inner {
	right: 0;
	left: auto;
}
.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	right: 0;
	left: auto;
	border-right-color: #526069;
	border-left-color: transparent;
}
.ribbon-corner.ribbon-bottom {
	top: auto;
	bottom: 0;
}
.ribbon-corner.ribbon-bottom .ribbon-inner {
	top: auto;
	bottom: 0;
}
.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	top: auto;
	bottom: 0;
	border-top-color: transparent;
	border-bottom-color: #526069;
}
.ribbon-corner.ribbon-xs .ribbon-inner {
	width: 28px;
	height: 26px;
	line-height: 26px;
}
.ribbon-corner.ribbon-xs .ribbon-inner:before {
	border-width: 22px;
}
.ribbon-corner.ribbon-xs .ribbon-inner > .icon {
	font-size: 12px;
}
.ribbon-corner.ribbon-sm .ribbon-inner {
	width: 34px;
	height: 32px;
	line-height: 32px;
}
.ribbon-corner.ribbon-sm .ribbon-inner:before {
	border-width: 26px;
}
.ribbon-corner.ribbon-sm .ribbon-inner > .icon {
	font-size: 12px;
}
.ribbon-corner.ribbon-lg .ribbon-inner {
	width: 46px;
	height: 44px;
	line-height: 44px;
}
.ribbon-corner.ribbon-lg .ribbon-inner:before {
	border-width: 36px;
}
.ribbon-corner.ribbon-lg .ribbon-inner > .icon {
	font-size: 18px;
}
.ribbon-clip {
	left: -14px;
}
.ribbon-clip .ribbon-inner {
	padding-left: 23px;
	border-radius: 0 5px 5px 0;
}
.ribbon-clip .ribbon-inner:after {
	position: absolute;
	bottom: -14px;
	left: 0;
	width: 0;
	height: 0;
	content: "";
	border: 7px solid transparent;
	border-top-color: #37474f;
	border-right-color: #37474f;
}
.ribbon-clip.ribbon-reverse {
	right: -14px;
	left: auto;
}
.ribbon-clip.ribbon-reverse .ribbon-inner {
	padding-right: 23px;
	padding-left: 15px;
	border-radius: 5px 0 0 5px;
}
.ribbon-clip.ribbon-reverse .ribbon-inner:after {
	right: 0;
	left: auto;
	border-right-color: transparent;
	border-left-color: #37474f;
}
.ribbon-clip.ribbon-bottom {
	top: auto;
	bottom: -3px;
}
.ribbon-clip.ribbon-bottom .ribbon-inner {
	top: auto;
	bottom: 16px;
}
.ribbon-clip.ribbon-bottom .ribbon-inner:after {
	top: -14px;
	bottom: auto;
	border-top-color: transparent;
	border-bottom-color: #37474f;
}
.ribbon-primary .ribbon-inner {
	background-color: #f96868;
}
.ribbon-primary.ribbon-bookmark .ribbon-inner:before {
	border-color: #f96868;
	border-right-color: transparent;
}
.ribbon-primary.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-left-color: transparent;
}
.ribbon-primary.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	border-right-color: #f96868;
	border-bottom-color: transparent;
}
.ribbon-primary.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-bottom-color: transparent;
	border-left-color: #f96868;
}
.ribbon-primary.ribbon-corner .ribbon-inner {
	background-color: transparent;
}
.ribbon-primary.ribbon-corner .ribbon-inner:before {
	border-top-color: #f96868;
	border-left-color: #f96868;
}
.ribbon-primary.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-left-color: transparent;
}
.ribbon-primary.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	border-top-color: transparent;
	border-bottom-color: #f96868;
}
.ribbon-primary .ribbon-inner:after {
	border-top-color: #e9595b;
	border-right-color: #e9595b;
}
.ribbon-primary.ribbon-reverse .ribbon-inner:after {
	border-right-color: transparent;
	border-left-color: #e9595b;
}
.ribbon-primary.ribbon-bottom .ribbon-inner:after {
	border-top-color: transparent;
	border-bottom-color: #e9595b;
}
.ribbon-success .ribbon-inner {
	background-color: #46be8a;
}
.ribbon-success.ribbon-bookmark .ribbon-inner:before {
	border-color: #46be8a;
	border-right-color: transparent;
}
.ribbon-success.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	border-right-color: #46be8a;
	border-left-color: transparent;
}
.ribbon-success.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	border-right-color: #46be8a;
	border-bottom-color: transparent;
}
.ribbon-success.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
	border-right-color: #46be8a;
	border-bottom-color: transparent;
	border-left-color: #46be8a;
}
.ribbon-success.ribbon-corner .ribbon-inner {
	background-color: transparent;
}
.ribbon-success.ribbon-corner .ribbon-inner:before {
	border-top-color: #46be8a;
	border-left-color: #46be8a;
}
.ribbon-success.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	border-right-color: #46be8a;
	border-left-color: transparent;
}
.ribbon-success.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	border-top-color: transparent;
	border-bottom-color: #46be8a;
}
.ribbon-success .ribbon-inner:after {
	border-top-color: #36ab7a;
	border-right-color: #36ab7a;
}
.ribbon-success.ribbon-reverse .ribbon-inner:after {
	border-right-color: transparent;
	border-left-color: #36ab7a;
}
.ribbon-success.ribbon-bottom .ribbon-inner:after {
	border-top-color: transparent;
	border-bottom-color: #36ab7a;
}
.ribbon-info .ribbon-inner {
	background-color: #57c7d4;
}
.ribbon-info.ribbon-bookmark .ribbon-inner:before {
	border-color: #57c7d4;
	border-right-color: transparent;
}
.ribbon-info.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	border-right-color: #57c7d4;
	border-left-color: transparent;
}
.ribbon-info.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	border-right-color: #57c7d4;
	border-bottom-color: transparent;
}
.ribbon-info.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
	border-right-color: #57c7d4;
	border-bottom-color: transparent;
	border-left-color: #57c7d4;
}
.ribbon-info.ribbon-corner .ribbon-inner {
	background-color: transparent;
}
.ribbon-info.ribbon-corner .ribbon-inner:before {
	border-top-color: #57c7d4;
	border-left-color: #57c7d4;
}
.ribbon-info.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	border-right-color: #57c7d4;
	border-left-color: transparent;
}
.ribbon-info.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	border-top-color: transparent;
	border-bottom-color: #57c7d4;
}
.ribbon-info .ribbon-inner:after {
	border-top-color: #47b8c6;
	border-right-color: #47b8c6;
}
.ribbon-info.ribbon-reverse .ribbon-inner:after {
	border-right-color: transparent;
	border-left-color: #47b8c6;
}
.ribbon-info.ribbon-bottom .ribbon-inner:after {
	border-top-color: transparent;
	border-bottom-color: #47b8c6;
}
.ribbon-warning .ribbon-inner {
	background-color: #f2a654;
}
.ribbon-warning.ribbon-bookmark .ribbon-inner:before {
	border-color: #f2a654;
	border-right-color: transparent;
}
.ribbon-warning.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f2a654;
	border-left-color: transparent;
}
.ribbon-warning.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	border-right-color: #f2a654;
	border-bottom-color: transparent;
}
.ribbon-warning.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f2a654;
	border-bottom-color: transparent;
	border-left-color: #f2a654;
}
.ribbon-warning.ribbon-corner .ribbon-inner {
	background-color: transparent;
}
.ribbon-warning.ribbon-corner .ribbon-inner:before {
	border-top-color: #f2a654;
	border-left-color: #f2a654;
}
.ribbon-warning.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f2a654;
	border-left-color: transparent;
}
.ribbon-warning.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	border-top-color: transparent;
	border-bottom-color: #f2a654;
}
.ribbon-warning .ribbon-inner:after {
	border-top-color: #ec9940;
	border-right-color: #ec9940;
}
.ribbon-warning.ribbon-reverse .ribbon-inner:after {
	border-right-color: transparent;
	border-left-color: #ec9940;
}
.ribbon-warning.ribbon-bottom .ribbon-inner:after {
	border-top-color: transparent;
	border-bottom-color: #ec9940;
}
.ribbon-danger .ribbon-inner {
	background-color: #f96868;
}
.ribbon-danger.ribbon-bookmark .ribbon-inner:before {
	border-color: #f96868;
	border-right-color: transparent;
}
.ribbon-danger.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-left-color: transparent;
}
.ribbon-danger.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
	border-right-color: #f96868;
	border-bottom-color: transparent;
}
.ribbon-danger.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-bottom-color: transparent;
	border-left-color: #f96868;
}
.ribbon-danger.ribbon-corner .ribbon-inner {
	background-color: transparent;
}
.ribbon-danger.ribbon-corner .ribbon-inner:before {
	border-top-color: #f96868;
	border-left-color: #f96868;
}
.ribbon-danger.ribbon-corner.ribbon-reverse .ribbon-inner:before {
	border-right-color: #f96868;
	border-left-color: transparent;
}
.ribbon-danger.ribbon-corner.ribbon-bottom .ribbon-inner:before {
	border-top-color: transparent;
	border-bottom-color: #f96868;
}
.ribbon-danger .ribbon-inner:after {
	border-top-color: #e9595b;
	border-right-color: #e9595b;
}
.ribbon-danger.ribbon-reverse .ribbon-inner:after {
	border-right-color: transparent;
	border-left-color: #e9595b;
}
.ribbon-danger.ribbon-bottom .ribbon-inner:after {
	border-top-color: transparent;
	border-bottom-color: #e9595b;
}
.dotnav {
	padding: 0;
	margin: 0;
	text-align: center;
	list-style: none;
}
.dotnav > li {
	position: relative;
	display: inline-block;
	width: 16px;
	height: 16px;
	margin: 0 16px;
	vertical-align: top;
	list-style: none;
}
.dotnav > li > a {
	display: inline-block;
	width: 100%;
	height: 100%;
	overflow: hidden;
	text-indent: 100%;
	white-space: nowrap;
	cursor: pointer;
	background-color: rgba(255, 255, 255, .3);
	border-radius: 50%;
}
.dotnav > li > a:focus {
	outline: none;
}
.dotnav > li.active > a, .dotnav > li:hover > a, .dotnav > li:focus > a {
	background-color: #fff;
}
.dotnav-scaleup > li > a {
	-webkit-transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	     -o-transition: background-color .3s ease 0s, -o-transform .3s ease 0s;
	        transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s;
	        transition: transform .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
}
.dotnav-scaleup > li.active > a {
	-webkit-transform: scale(1.5);
	    -ms-transform: scale(1.5);
	     -o-transform: scale(1.5);
	        transform: scale(1.5);
}
.dotnav-stroke > li > a {
	border: 2px solid transparent;
	-webkit-transition: border .3s ease 0s, background-color .3s ease 0s;
	     -o-transition: border .3s ease 0s, background-color .3s ease 0s;
	        transition: border .3s ease 0s, background-color .3s ease 0s;
}
.dotnav-stroke > li.active > a {
	background-color: transparent;
	border-color: #fff;
	-webkit-transform: scale(1.3);
	    -ms-transform: scale(1.3);
	     -o-transform: scale(1.3);
	        transform: scale(1.3);
}
.dotnav-fillin > li > a {
	background-color: transparent;
	-webkit-box-shadow: 0 0 0 2px #fff inset;
	        box-shadow: 0 0 0 2px #fff inset;
	-webkit-transition: -webkit-box-shadow .3s ease 0s;
	     -o-transition:         box-shadow .3s ease 0s;
	        transition: -webkit-box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s, -webkit-box-shadow .3s ease 0s;
}
.dotnav-fillin > li:hover > a, .dotnav-fillin > li:focus > a {
	background-color: transparent;
	-webkit-box-shadow: 0 0 0 2px rgba(255, 255, 255, .6) inset;
	        box-shadow: 0 0 0 2px rgba(255, 255, 255, .6) inset;
}
.dotnav-fillin > li.active > a {
	-webkit-box-shadow: 0 0 0 8px #fff inset;
	        box-shadow: 0 0 0 8px #fff inset;
}
.dotnav-dotstroke > li > a {
	-webkit-box-shadow: 0 0 0 8px rgba(255, 255, 255, .5) inset;
	        box-shadow: 0 0 0 8px rgba(255, 255, 255, .5) inset;
	-webkit-transition: -webkit-box-shadow .3s ease 0s;
	     -o-transition:         box-shadow .3s ease 0s;
	        transition: -webkit-box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s;
	        transition:         box-shadow .3s ease 0s, -webkit-box-shadow .3s ease 0s;
}
.dotnav-dotstroke > li.active > a {
	background-color: rgba(255, 255, 255, .3);
	-webkit-box-shadow: 0 0 0 2px #fff inset;
	        box-shadow: 0 0 0 2px #fff inset;
}
.dotnav-fall > li:after {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	visibility: hidden;
	content: "";
	background-color: rgba(0, 0, 0, .3);
	border-radius: 50%;
	opacity: 0;
	-webkit-transition: opacity .3s ease 0s, visibility 0s ease .3s, -webkit-transform .3s ease 0s;
	     -o-transition: opacity .3s ease 0s, visibility 0s ease .3s, -o-transform .3s ease 0s;
	        transition: opacity .3s ease 0s, visibility 0s ease .3s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s, visibility 0s ease .3s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s, visibility 0s ease .3s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
	-webkit-transform: translate(0%, -200%);
	    -ms-transform: translate(0%, -200%);
	     -o-transform: translate(0%, -200%);
	        transform: translate(0%, -200%);
}
.dotnav-fall > li > a {
	-webkit-transition: opacity .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	     -o-transition: opacity .3s ease 0s, background-color .3s ease 0s, -o-transform .3s ease 0s;
	        transition: opacity .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s, background-color .3s ease 0s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
}
.dotnav-fall > li.active:after {
	visibility: visible;
	opacity: 1;
	-webkit-transition: opacity .3s ease 0s, -webkit-transform .3s ease 0s;
	     -o-transition: opacity .3s ease 0s, -o-transform .3s ease 0s;
	        transition: opacity .3s ease 0s, -webkit-transform .3s ease 0s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s;
	        transition: transform .3s ease 0s, opacity .3s ease 0s, -webkit-transform .3s ease 0s, -o-transform .3s ease 0s;
	-webkit-transform: translate(0%, 0%);
	    -ms-transform: translate(0%, 0%);
	     -o-transform: translate(0%, 0%);
	        transform: translate(0%, 0%);
}
.dotnav-fall > li.active > a {
	opacity: 0;
	-webkit-transform: translate(0, 200%);
	    -ms-transform: translate(0, 200%);
	     -o-transform: translate(0, 200%);
	        transform: translate(0, 200%);
}
.color-selector {
	padding: 0;
	margin: 0;
	list-style: none;
}
.color-selector > li {
	position: relative;
	display: inline-block;
	width: 24px;
	height: 24px;
	margin: 0 5px 0 0;
	background-color: #f96868;
	border-radius: 100%;
}
.color-selector > li:hover {
	opacity: .8;
}
.color-selector > li:before {
	position: absolute;
	top: 0;
	left: 0;
	display: inline-block;
	width: inherit;
	height: inherit;
	content: "";
	background: inherit;
	border: 1px solid rgba(0, 0, 0, .1);
	border-radius: inherit;
}
.color-selector > li input[type="radio"] {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	width: inherit;
	height: inherit;
	cursor: pointer;
	border-radius: inherit;
	opacity: 0;
}
.color-selector > li input[type="radio"]:disabled {
	cursor: not-allowed;
}
.color-selector > li label {
	position: relative;
	font-family: "Web Icons";
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
}
.color-selector > li input[type="radio"]:checked + label:after {
	position: absolute;
	top: 0;
	left: 5px;
	display: inline-block;
	margin-top: -5px;
	font-size: 14px;
	line-height: 1;
	color: #fff;
	content: "\f192";
}
.color-selector > li.color-selector-disabled {
	background-color: #ccd5db !important;
}
.color-selector > li.color-selector-disabled input[type="radio"]:disabled {
	cursor: not-allowed;
}
.width-50 {
	width: 50px;
}
.width-100 {
	width: 100px;
}
.width-150 {
	width: 150px;
}
.width-200 {
	width: 200px;
}
.width-250 {
	width: 250px;
}
.width-300 {
	width: 300px;
}
.width-350 {
	width: 350px;
}
.width-400 {
	width: 400px;
}
.width-450 {
	width: 450px;
}
.width-500 {
	width: 500px;
}
.width-full {
	width: 100% !important;
}
@media (max-width: 767px) {
	.width-xs-50 {
		width: 50px;
	}
	.width-xs-100 {
		width: 100px;
	}
	.width-xs-150 {
		width: 150px;
	}
	.width-xs-200 {
		width: 200px;
	}
	.width-xs-250 {
		width: 250px;
	}
	.width-xs-300 {
		width: 300px;
	}
	.width-xs-350 {
		width: 350px;
	}
	.width-xs-400 {
		width: 400px;
	}
	.width-xs-450 {
		width: 450px;
	}
	.width-xs-500 {
		width: 500px;
	}
	.width-xs-100pc {
		width: 100%;
	}
}
@media (min-width: 768px) and (max-width: 991px) {
	.width-sm-50 {
		width: 50px;
	}
	.width-sm-100 {
		width: 100px;
	}
	.width-sm-150 {
		width: 150px;
	}
	.width-sm-200 {
		width: 200px;
	}
	.width-sm-250 {
		width: 250px;
	}
	.width-sm-300 {
		width: 300px;
	}
	.width-sm-350 {
		width: 350px;
	}
	.width-sm-400 {
		width: 400px;
	}
	.width-sm-450 {
		width: 450px;
	}
	.width-sm-500 {
		width: 500px;
	}
	.width-sm-100pc {
		width: 100%;
	}
}
@media (min-width: 992px) and (max-width: 1199px) {
	.width-md-50 {
		width: 50px;
	}
	.width-md-100 {
		width: 100px;
	}
	.width-md-150 {
		width: 150px;
	}
	.width-md-200 {
		width: 200px;
	}
	.width-md-250 {
		width: 250px;
	}
	.width-md-300 {
		width: 300px;
	}
	.width-md-350 {
		width: 350px;
	}
	.width-md-400 {
		width: 400px;
	}
	.width-md-450 {
		width: 450px;
	}
	.width-md-500 {
		width: 500px;
	}
}
@media (min-width: 1200px) {
	.width-lg-50 {
		width: 50px;
	}
	.width-lg-100 {
		width: 100px;
	}
	.width-lg-150 {
		width: 150px;
	}
	.width-lg-200 {
		width: 200px;
	}
	.width-lg-250 {
		width: 250px;
	}
	.width-lg-300 {
		width: 300px;
	}
	.width-lg-350 {
		width: 350px;
	}
	.width-lg-400 {
		width: 400px;
	}
	.width-lg-450 {
		width: 450px;
	}
	.width-lg-500 {
		width: 500px;
	}
}
.height-50 {
	height: 50px;
}
.height-100 {
	height: 100px;
}
.height-120 {
	height: 120px;
}
.height-150 {
	height: 150px;
}
.height-200 {
	height: 200px;
}
.height-250 {
	height: 250px;
}
.height-300 {
	height: 300px;
}
.height-350 {
	height: 350px;
}
.height-400 {
	height: 400px;
}
.height-450 {
	height: 450px;
}
.height-500 {
	height: 500px;
}
.height-full {
	height: 100% !important;
}
@media (max-width: 767px) {
	.height-xs-50 {
		height: 50px;
	}
	.height-xs-100 {
		height: 100px;
	}
	.height-xs-120 {
		height: 120px;
	}
	.height-xs-150 {
		height: 150px;
	}
	.height-xs-200 {
		height: 200px;
	}
	.height-xs-250 {
		height: 250px;
	}
	.height-xs-300 {
		height: 300px;
	}
	.height-xs-350 {
		height: 350px;
	}
	.height-xs-400 {
		height: 400px;
	}
	.height-xs-450 {
		height: 450px;
	}
	.height-xs-500 {
		height: 500px;
	}
}
@media (min-width: 768px) and (max-width: 991px) {
	.height-sm-50 {
		height: 50px;
	}
	.height-sm-100 {
		height: 100px;
	}
	.height-sm-120 {
		height: 120px;
	}
	.height-sm-150 {
		height: 150px;
	}
	.height-sm-200 {
		height: 200px;
	}
	.height-sm-250 {
		height: 250px;
	}
	.height-sm-300 {
		height: 300px;
	}
	.height-sm-350 {
		height: 350px;
	}
	.height-sm-400 {
		height: 400px;
	}
	.height-sm-450 {
		height: 450px;
	}
	.height-sm-500 {
		height: 500px;
	}
}
@media (min-width: 992px) and (max-width: 1199px) {
	.height-md-50 {
		height: 50px;
	}
	.height-md-100 {
		height: 100px;
	}
	.height-md-120 {
		height: 120px;
	}
	.height-md-150 {
		height: 150px;
	}
	.height-md-200 {
		height: 200px;
	}
	.height-md-250 {
		height: 250px;
	}
	.height-md-300 {
		height: 300px;
	}
	.height-md-350 {
		height: 350px;
	}
	.height-md-400 {
		height: 400px;
	}
	.height-md-450 {
		height: 450px;
	}
	.height-md-500 {
		height: 500px;
	}
}
@media (min-width: 1200px) {
	.height-lg-50 {
		height: 50px;
	}
	.height-lg-100 {
		height: 100px;
	}
	.height-lg-120 {
		height: 120px;
	}
	.height-lg-150 {
		height: 150px;
	}
	.height-lg-200 {
		height: 200px;
	}
	.height-lg-250 {
		height: 250px;
	}
	.height-lg-300 {
		height: 300px;
	}
	.height-lg-350 {
		height: 350px;
	}
	.height-lg-400 {
		height: 400px;
	}
	.height-lg-450 {
		height: 450px;
	}
	.height-lg-500 {
		height: 500px;
	}
}
.margin-0 {
	margin: 0 !important;
}
.margin-3 {
	margin: 3px !important;
}
.margin-5 {
	margin: 5px !important;
}
.margin-10 {
	margin: 10px !important;
}
.margin-15 {
	margin: 15px !important;
}
.margin-20 {
	margin: 20px !important;
}
.margin-25 {
	margin: 25px !important;
}
.margin-30 {
	margin: 30px !important;
}
.margin-35 {
	margin: 35px !important;
}
.margin-40 {
	margin: 40px !important;
}
.margin-45 {
	margin: 45px !important;
}
.margin-50 {
	margin: 50px !important;
}
.margin-60 {
	margin: 60px !important;
}
.margin-70 {
	margin: 70px !important;
}
.margin-80 {
	margin: 80px !important;
}
.margin-vertical-0 {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}
.margin-vertical-3 {
	margin-top: 3px !important;
	margin-bottom: 3px !important;
}
.margin-vertical-5 {
	margin-top: 5px !important;
	margin-bottom: 5px !important;
}
.margin-vertical-10 {
	margin-top: 10px !important;
	margin-bottom: 10px !important;
}
.margin-vertical-15 {
	margin-top: 15px !important;
	margin-bottom: 15px !important;
}
.margin-vertical-20 {
	margin-top: 20px !important;
	margin-bottom: 20px !important;
}
.margin-vertical-25 {
	margin-top: 25px !important;
	margin-bottom: 25px !important;
}
.margin-vertical-30 {
	margin-top: 30px !important;
	margin-bottom: 30px !important;
}
.margin-vertical-35 {
	margin-top: 35px !important;
	margin-bottom: 35px !important;
}
.margin-vertical-40 {
	margin-top: 40px !important;
	margin-bottom: 40px !important;
}
.margin-vertical-45 {
	margin-top: 45px !important;
	margin-bottom: 45px !important;
}
.margin-vertical-50 {
	margin-top: 50px !important;
	margin-bottom: 50px !important;
}
.margin-vertical-60 {
	margin-top: 60px !important;
	margin-bottom: 60px !important;
}
.margin-vertical-70 {
	margin-top: 70px !important;
	margin-bottom: 70px !important;
}
.margin-vertical-80 {
	margin-top: 80px !important;
	margin-bottom: 80px !important;
}
.margin-horizontal-0 {
	margin-right: 0 !important;
	margin-left: 0 !important;
}
.margin-horizontal-3 {
	margin-right: 3px !important;
	margin-left: 3px !important;
}
.margin-horizontal-5 {
	margin-right: 5px !important;
	margin-left: 5px !important;
}
.margin-horizontal-10 {
	margin-right: 10px !important;
	margin-left: 10px !important;
}
.margin-horizontal-15 {
	margin-right: 15px !important;
	margin-left: 15px !important;
}
.margin-horizontal-20 {
	margin-right: 20px !important;
	margin-left: 20px !important;
}
.margin-horizontal-25 {
	margin-right: 25px !important;
	margin-left: 25px !important;
}
.margin-horizontal-30 {
	margin-right: 30px !important;
	margin-left: 30px !important;
}
.margin-horizontal-35 {
	margin-right: 35px !important;
	margin-left: 35px !important;
}
.margin-horizontal-40 {
	margin-right: 40px !important;
	margin-left: 40px !important;
}
.margin-horizontal-45 {
	margin-right: 45px !important;
	margin-left: 45px !important;
}
.margin-horizontal-50 {
	margin-right: 50px !important;
	margin-left: 50px !important;
}
.margin-horizontal-60 {
	margin-right: 60px !important;
	margin-left: 60px !important;
}
.margin-horizontal-70 {
	margin-right: 70px !important;
	margin-left: 70px !important;
}
.margin-horizontal-80 {
	margin-right: 80px !important;
	margin-left: 80px !important;
}
.margin-top-0 {
	margin-top: 0 !important;
}
.margin-top-3 {
	margin-top: 3px !important;
}
.margin-top-5 {
	margin-top: 5px !important;
}
.margin-top-10 {
	margin-top: 10px !important;
}
.margin-top-15 {
	margin-top: 15px !important;
}
.margin-top-20 {
	margin-top: 20px !important;
}
.margin-top-25 {
	margin-top: 25px !important;
}
.margin-top-30 {
	margin-top: 30px !important;
}
.margin-top-35 {
	margin-top: 35px !important;
}
.margin-top-40 {
	margin-top: 40px !important;
}
.margin-top-45 {
	margin-top: 45px !important;
}
.margin-top-50 {
	margin-top: 50px !important;
}
.margin-top-60 {
	margin-top: 60px !important;
}
.margin-top-70 {
	margin-top: 70px !important;
}
.margin-top-80 {
	margin-top: 80px !important;
}
.margin-bottom-0 {
	margin-bottom: 0 !important;
}
.margin-bottom-3 {
	margin-bottom: 3px !important;
}
.margin-bottom-5 {
	margin-bottom: 5px !important;
}
.margin-bottom-10 {
	margin-bottom: 10px !important;
}
.margin-bottom-15 {
	margin-bottom: 15px !important;
}
.margin-bottom-20 {
	margin-bottom: 20px !important;
}
.margin-bottom-25 {
	margin-bottom: 25px !important;
}
.margin-bottom-30 {
	margin-bottom: 30px !important;
}
.margin-bottom-35 {
	margin-bottom: 35px !important;
}
.margin-bottom-40 {
	margin-bottom: 40px !important;
}
.margin-bottom-45 {
	margin-bottom: 45px !important;
}
.margin-bottom-50 {
	margin-bottom: 50px !important;
}
.margin-bottom-60 {
	margin-bottom: 60px !important;
}
.margin-bottom-70 {
	margin-bottom: 70px !important;
}
.margin-bottom-80 {
	margin-bottom: 80px !important;
}
.margin-left-0 {
	margin-left: 0 !important;
}
.margin-left-3 {
	margin-left: 3px !important;
}
.margin-left-5 {
	margin-left: 5px !important;
}
.margin-left-10 {
	margin-left: 10px !important;
}
.margin-left-15 {
	margin-left: 15px !important;
}
.margin-left-20 {
	margin-left: 20px !important;
}
.margin-left-25 {
	margin-left: 25px !important;
}
.margin-left-30 {
	margin-left: 30px !important;
}
.margin-left-35 {
	margin-left: 35px !important;
}
.margin-left-40 {
	margin-left: 40px !important;
}
.margin-left-45 {
	margin-left: 45px !important;
}
.margin-left-50 {
	margin-left: 50px !important;
}
.margin-left-60 {
	margin-left: 60px !important;
}
.margin-left-70 {
	margin-left: 70px !important;
}
.margin-left-80 {
	margin-left: 80px !important;
}
.margin-right-0 {
	margin-right: 0 !important;
}
.margin-right-3 {
	margin-right: 3px !important;
}
.margin-right-5 {
	margin-right: 5px !important;
}
.margin-right-10 {
	margin-right: 10px !important;
}
.margin-right-15 {
	margin-right: 15px !important;
}
.margin-right-20 {
	margin-right: 20px !important;
}
.margin-right-25 {
	margin-right: 25px !important;
}
.margin-right-30 {
	margin-right: 30px !important;
}
.margin-right-35 {
	margin-right: 35px !important;
}
.margin-right-40 {
	margin-right: 40px !important;
}
.margin-right-45 {
	margin-right: 45px !important;
}
.margin-right-50 {
	margin-right: 50px !important;
}
.margin-right-60 {
	margin-right: 60px !important;
}
.margin-right-70 {
	margin-right: 70px !important;
}
.margin-right-80 {
	margin-right: 80px !important;
}
@media (max-width: 767px) {
	.margin-xs-0 {
		margin: 0 !important;
	}
}
@media (min-width: 768px) {
	.margin-sm-0 {
		margin: 0 !important;
	}
}
@media (min-width: 992px) {
	.margin-md-0 {
		margin: 0 !important;
	}
}
@media (min-width: 1200px) {
	.margin-lg-0 {
		margin: 0 !important;
	}
}
.padding-0 {
	padding: 0 !important;
}
.padding-3 {
	padding: 3px !important;
}
.padding-5 {
	padding: 5px !important;
}
.padding-10 {
	padding: 10px !important;
}
.padding-15 {
	padding: 15px !important;
}
.padding-20 {
	padding: 20px !important;
}
.padding-25 {
	padding: 25px !important;
}
.padding-30 {
	padding: 30px !important;
}
.padding-35 {
	padding: 35px !important;
}
.padding-40 {
	padding: 40px !important;
}
.padding-45 {
	padding: 45px !important;
}
.padding-50 {
	padding: 50px !important;
}
.padding-60 {
	padding: 60px !important;
}
.padding-70 {
	padding: 70px !important;
}
.padding-80 {
	padding: 80px !important;
}
.padding-vertical-0 {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}
.padding-vertical-3 {
	padding-top: 3px !important;
	padding-bottom: 3px !important;
}
.padding-vertical-5 {
	padding-top: 5px !important;
	padding-bottom: 5px !important;
}
.padding-vertical-10 {
	padding-top: 10px !important;
	padding-bottom: 10px !important;
}
.padding-vertical-15 {
	padding-top: 15px !important;
	padding-bottom: 15px !important;
}
.padding-vertical-20 {
	padding-top: 20px !important;
	padding-bottom: 20px !important;
}
.padding-vertical-25 {
	padding-top: 25px !important;
	padding-bottom: 25px !important;
}
.padding-vertical-30 {
	padding-top: 30px !important;
	padding-bottom: 30px !important;
}
.padding-vertical-35 {
	padding-top: 35px !important;
	padding-bottom: 35px !important;
}
.padding-vertical-40 {
	padding-top: 40px !important;
	padding-bottom: 40px !important;
}
.padding-vertical-45 {
	padding-top: 45px !important;
	padding-bottom: 45px !important;
}
.padding-vertical-50 {
	padding-top: 50px !important;
	padding-bottom: 50px !important;
}
.padding-vertical-60 {
	padding-top: 60px !important;
	padding-bottom: 60px !important;
}
.padding-vertical-70 {
	padding-top: 70px !important;
	padding-bottom: 70px !important;
}
.padding-vertical-80 {
	padding-top: 80px !important;
	padding-bottom: 80px !important;
}
.padding-horizontal-0 {
	padding-right: 0 !important;
	padding-left: 0 !important;
}
.padding-horizontal-3 {
	padding-right: 3px !important;
	padding-left: 3px !important;
}
.padding-horizontal-5 {
	padding-right: 5px !important;
	padding-left: 5px !important;
}
.padding-horizontal-10 {
	padding-right: 10px !important;
	padding-left: 10px !important;
}
.padding-horizontal-15 {
	padding-right: 15px !important;
	padding-left: 15px !important;
}
.padding-horizontal-20 {
	padding-right: 20px !important;
	padding-left: 20px !important;
}
.padding-horizontal-25 {
	padding-right: 25px !important;
	padding-left: 25px !important;
}
.padding-horizontal-30 {
	padding-right: 30px !important;
	padding-left: 30px !important;
}
.padding-horizontal-35 {
	padding-right: 35px !important;
	padding-left: 35px !important;
}
.padding-horizontal-40 {
	padding-right: 40px !important;
	padding-left: 40px !important;
}
.padding-horizontal-45 {
	padding-right: 45px !important;
	padding-left: 45px !important;
}
.padding-horizontal-50 {
	padding-right: 50px !important;
	padding-left: 50px !important;
}
.padding-horizontal-60 {
	padding-right: 60px !important;
	padding-left: 60px !important;
}
.padding-horizontal-70 {
	padding-right: 70px !important;
	padding-left: 70px !important;
}
.padding-horizontal-80 {
	padding-right: 80px !important;
	padding-left: 80px !important;
}
.padding-top-0 {
	padding-top: 0 !important;
}
.padding-top-3 {
	padding-top: 3px !important;
}
.padding-top-5 {
	padding-top: 5px !important;
}
.padding-top-10 {
	padding-top: 10px !important;
}
.padding-top-15 {
	padding-top: 15px !important;
}
.padding-top-20 {
	padding-top: 20px !important;
}
.padding-top-25 {
	padding-top: 25px !important;
}
.padding-top-30 {
	padding-top: 30px !important;
}
.padding-top-35 {
	padding-top: 35px !important;
}
.padding-top-40 {
	padding-top: 40px !important;
}
.padding-top-45 {
	padding-top: 45px !important;
}
.padding-top-50 {
	padding-top: 50px !important;
}
.padding-top-60 {
	padding-top: 60px !important;
}
.padding-top-70 {
	padding-top: 70px !important;
}
.padding-top-80 {
	padding-top: 80px !important;
}
.padding-bottom-0 {
	padding-bottom: 0 !important;
}
.padding-bottom-3 {
	padding-bottom: 3px !important;
}
.padding-bottom-5 {
	padding-bottom: 5px !important;
}
.padding-bottom-10 {
	padding-bottom: 10px !important;
}
.padding-bottom-15 {
	padding-bottom: 15px !important;
}
.padding-bottom-20 {
	padding-bottom: 20px !important;
}
.padding-bottom-25 {
	padding-bottom: 25px !important;
}
.padding-bottom-30 {
	padding-bottom: 30px !important;
}
.padding-bottom-35 {
	padding-bottom: 35px !important;
}
.padding-bottom-40 {
	padding-bottom: 40px !important;
}
.padding-bottom-45 {
	padding-bottom: 45px !important;
}
.padding-bottom-50 {
	padding-bottom: 50px !important;
}
.padding-bottom-60 {
	padding-bottom: 60px !important;
}
.padding-bottom-70 {
	padding-bottom: 70px !important;
}
.padding-bottom-80 {
	padding-bottom: 80px !important;
}
.padding-left-0 {
	padding-left: 0 !important;
}
.padding-left-3 {
	padding-left: 3px !important;
}
.padding-left-5 {
	padding-left: 5px !important;
}
.padding-left-10 {
	padding-left: 10px !important;
}
.padding-left-15 {
	padding-left: 15px !important;
}
.padding-left-20 {
	padding-left: 20px !important;
}
.padding-left-25 {
	padding-left: 25px !important;
}
.padding-left-30 {
	padding-left: 30px !important;
}
.padding-left-35 {
	padding-left: 35px !important;
}
.padding-left-40 {
	padding-left: 40px !important;
}
.padding-left-45 {
	padding-left: 45px !important;
}
.padding-left-50 {
	padding-left: 50px !important;
}
.padding-left-60 {
	padding-left: 60px !important;
}
.padding-left-70 {
	padding-left: 70px !important;
}
.padding-left-80 {
	padding-left: 80px !important;
}
.padding-right-0 {
	padding-right: 0 !important;
}
.padding-right-3 {
	padding-right: 3px !important;
}
.padding-right-5 {
	padding-right: 5px !important;
}
.padding-right-10 {
	padding-right: 10px !important;
}
.padding-right-15 {
	padding-right: 15px !important;
}
.padding-right-20 {
	padding-right: 20px !important;
}
.padding-right-25 {
	padding-right: 25px !important;
}
.padding-right-30 {
	padding-right: 30px !important;
}
.padding-right-35 {
	padding-right: 35px !important;
}
.padding-right-40 {
	padding-right: 40px !important;
}
.padding-right-45 {
	padding-right: 45px !important;
}
.padding-right-50 {
	padding-right: 50px !important;
}
.padding-right-60 {
	padding-right: 60px !important;
}
.padding-right-70 {
	padding-right: 70px !important;
}
.padding-right-80 {
	padding-right: 80px !important;
}
@media (max-width: 767px) {
	.padding-xs-0 {
		padding: 0 !important;
	}
}
@media (min-width: 768px) {
	.padding-sm-0 {
		padding: 0 !important;
	}
}
@media (min-width: 992px) {
	.padding-md-0 {
		padding: 0 !important;
	}
}
@media (min-width: 1200px) {
	.padding-lg-0 {
		padding: 0 !important;
	}
}
.example-wrap {
	margin-bottom: 30px;
}
.example-wrap .example-wrap {
	margin-bottom: 0;
}
.example {
	margin-top: 20px;
	margin-bottom: 20px;
}
.example:before, .example:after {
	display: table;
	content: " ";
}
.example:after {
	clear: both;
}
.example-title {
	text-transform: uppercase;
}
.example-title, h4.example-title {
	font-size: 14px;
}
.panel-body > .example-wrap:last-child {
	margin-bottom: 0;
}
.panel-body > .row:last-child > [class*="col-"]:last-child .example-wrap:last-child {
	margin-bottom: 0;
}
.example-well {
	position: relative;
	margin-bottom: 30px;
	background-color: #f3f7f9;
}
.example-well .center {
	position: absolute;
	top: 50%;
	left: 50%;
	display: inline-block;
	max-width: 100%;
	max-height: 100%;
	-webkit-transform: translate(-50%, -50%);
	    -ms-transform: translate(-50%, -50%);
	     -o-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
}
.example-dropdown .dropdown:before, .example-dropdown .dropup:before, .example-dropdown .dropdown:after, .example-dropdown .dropup:after {
	display: table;
	content: " ";
}
.example-dropdown .dropdown:after, .example-dropdown .dropup:after {
	clear: both;
}
.example-dropdown .dropdown > .dropdown-toggle, .example-dropdown .dropup > .dropdown-toggle {
	float: left;
}
.example-dropdown .dropdown > .dropdown-menu, .example-dropdown .dropup > .dropdown-menu {
	position: static;
	display: block;
	clear: left;
}
.example-dropdown .dropdown > .dropdown-menu-right, .example-dropdown .dropup > .dropdown-menu-right {
	float: right;
	clear: right;
}
.example-tooltip {
	position: relative;
	z-index: 1;
	display: inline-block;
}
.example-tooltip .tooltip {
	position: relative;
	margin-right: 25px;
	opacity: 1;
}
.example-grid .example-col, .example-blocks .example-col {
	min-height: 0;
	padding: 10px 15px 12px;
	background-color: #f3f7f9;
	border-radius: 0;
}
.example-grid .example-col {
	margin-bottom: 20px;
}
.example-grid .example-col .example-col {
	margin-top: 20px;
	margin-bottom: 0;
	background-color: #e2ecf1;
}
.example-popover {
	position: relative;
	z-index: 1;
	display: inline-block;
}
.example-popover .popover {
	position: relative;
	display: block;
	margin-right: 25px;
}
.example-buttons .btn, .example-buttons .btn-group, .example-buttons .btn-group-vertical {
	margin: 0 10px 10px 0;
}
.example-buttons .btn-group-vertical .btn, .example-buttons .btn-group .btn {
	margin-right: 0;
	margin-bottom: 0;
}
.example-buttons .modal .btn {
	margin: inherit;
}
.example-box {
	position: relative;
	padding: 45px 15px 15px;
	margin-right: 0;
	margin-left: 0;
	border: 1px solid #e4eaec;
}
.example-box:after {
	position: absolute;
	top: 15px;
	left: 15px;
	font-size: 12px;
	color: #959595;
	text-transform: uppercase;
	letter-spacing: 1px;
	content: "示例";
}
.example-avatars .avatar {
	margin-right: 20px;
	margin-bottom: 20px;
}
.example-avatars .avatar:last-child {
	margin-right: 20px;
}
.example-typography {
	position: relative;
	padding-left: 25%;
}
.example-typography .heading-note, .example-typography .text-note {
	position: absolute;
	bottom: 2px;
	left: 0;
	display: block;
	width: 260px;
	font-size: 13px;
	font-weight: 400;
	line-height: 13px;
	color: #aab2bd;
}
.example-typography .text-note {
	top: 10px;
	bottom: auto;
}
.example-responsive {
	min-height: .01%;
	overflow-x: auto;
}
@media screen and (max-width: 767px) {
	.example-responsive {
		width: 100%;
		overflow-y: hidden;
		-ms-overflow-style: -ms-autohiding-scrollbar;
	}
}
.example-well .page-header {
	padding: 30px 20px;
}