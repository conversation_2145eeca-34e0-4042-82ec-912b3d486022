<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--定义日志存放的位置-->
    <springProperty scope="context" name="portalLogPath" source="log.path" defaultValue="logs"/>
    <springProperty scope="context" name="logMaxFileSize" source="log.maxFileSize" defaultValue="50MB"/>
    <springProperty scope="context" name="logMaxHistory" source="log.maxHistory" defaultValue="45"/>
    <springProperty scope="context" name="totalLogSizeCap" source="log.totalLogSizeCap" defaultValue="20GB"/>
    <springProperty scope="context" name="errorLogSizeCap" source="log.errorLogSizeCap" defaultValue="5GB"/>

    <!-- ****************************************************************************************** -->
    <!-- ****************************** 非生产环境只在控制台打印日志 ************************************ -->
    <!-- ****************************************************************************************** -->
    <springProfile name="!prod">

        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
                    <!--<charset>utf-8</charset>-->
                </layout>
            </encoder>
        </appender>

        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>

    </springProfile>

    <!-- ****************************************************************************************** -->
    <!-- ********************** 生产环境日志记录在文件 **************************** -->
    <!-- ****************************************************************************************** -->
    <springProfile name="prod">

        <!-- 日志记录器，日期滚动记录 -->
        <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 正在记录的日志文件的路径及文件名 -->
            <file>${portalLogPath}/log_error.log</file>
            <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- 归档的日志文件的路径，例如今天是2018-11-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
                而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
                <fileNamePattern>${portalLogPath}/error/log-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <!-- 除按日志记录之外，还配置了日志文件不能超过50MB，若超过50MB，日志文件会以索引0开始，
                命名日志文件，例如log-error-2018-11-21.0.log -->
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${logMaxFileSize}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${logMaxHistory}</maxHistory>
                <totalSizeCap>${errorLogSizeCap}</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
            <!-- 追加方式记录日志 -->
            <append>true</append>
            <!-- 日志文件的格式 -->
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
                    <!--<charset>utf-8</charset>-->
                </layout>
            </encoder>
            <!-- 此日志文件只记录error级别的 -->
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>error</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 日志记录器，日期滚动记录 -->
        <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 正在记录的日志文件的路径及文件名 -->
            <file>${portalLogPath}/log_total.log</file>
            <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
                而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
                <fileNamePattern>${portalLogPath}/total/log-total-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <!-- 除按日志记录之外，还配置了日志文件不能超过50MB，若超过50MB，日志文件会以索引0开始，
                命名日志文件，例如log-error-2013-12-21.0.log -->
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${logMaxFileSize}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${logMaxHistory}</maxHistory>
                <totalSizeCap>${totalLogSizeCap}</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
            <!-- 追加方式记录日志 -->
            <append>true</append>
            <!-- 日志文件的格式 -->
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
                    <!--<charset>utf-8</charset>-->
                </layout>
            </encoder>
        </appender>

        <appender name="FILE_BIZRULE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${portalLogPath}/log_bizrule.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${portalLogPath}/bizrule/log-bizrule-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${logMaxFileSize}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${logMaxHistory}</maxHistory>
                <totalSizeCap>${errorLogSizeCap}</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
            <append>true</append>
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
                    <!--<charset>utf-8</charset>-->
                </layout>
            </encoder>
        </appender>

        <logger name="bizRuleLog" level="info" additivity="false">
            <appender-ref ref="FILE_BIZRULE"/>
        </logger>

        <root level="info">
            <appender-ref ref="FILE_ERROR"/>
            <appender-ref ref="FILE_ALL"/>
        </root>

    </springProfile>

</configuration>


