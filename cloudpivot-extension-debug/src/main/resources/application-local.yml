server:
  port: 8080
  servlet:
    context-path: /api    #应用的上下文路径，详细参考 云枢帮助中心-开发者手册-解决跨域安全问题
  domain: http://127.0.0.1:8080

spring:
  #数据源
  datasource:
    #数据库驱动:
    #driver-class-name: oracle.jdbc.OracleDriver # Oracle
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver # SQL Server
    driver-class-name: dm.jdbc.driver.DmDriver # DM
    #driver-class-name: org.postgresql.Driver # PostgreSQL
    #driver-class-name: com.mysql.cj.jdbc.Driver # MySQL
    #数据库连接：
    #url: ********************************************                                                                                     # Oracle
    #url: ***********************************************************************                                                                       # SQL Server
    url: jdbc:dm://10.41.0.218:5238  # DM

    #url: *******************************************
#    url: ***************************************************************************************************************************************************** # MySQL
    username: cloudpivot
    password: Test123456!
    hikari:
      maximum-pool-size: 10                                     # 连接池最大连接数. 根据系统并发用户数设置合理值
      minimum-idle: 1                                           # 连接池最小空闲连接数量
      #connection-test-query: select 1                           # 测试连接是否可用的查询语句 (MySQL, SQLServer)
      #connection-test-query: select 1 from dual                # 测试连接是否可用的查询语句 (Oracle)

  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    #database-platform: org.hibernate.dialect.Oracle12cDialect       # Oracle
    #database-platform: org.hibernate.dialect.SQLServerDialect       # SQL Server
    database-platform: org.hibernate.dialect.DmDialect              # DM
    #database-platform: com.authine.cloudpivot.plugins.databse.PostgreSelfDialect  # PostgreSQL
    #database-platform: org.hibernate.dialect.MySQL5InnoDBDialect     # MySQL
    show-sql: false
    #properties:
    #  hibernate:
    #    temp:
    #      use_jdbc_metadata_defaults: false       #数据库为PostgreSQL时需将此配置放开
  redis:
    host: ************  #地址
    database: 0         #
    password: Authine!2024  #密码
    port: 6379          #端口
    prefix: ""
  quartz:
    auto-startup: false  #本地启动时，不加入quartz集群

logging:
  level:
    com.authine.cloudpivot: info   # 云枢产品日志输出级别
    com.authine.cloudpivot.web.sso: info
    com.authine.cloudpivot.foundation.orm.impl.dml: trace # 表单操作SQL打印
    com.authine.cloudpivot.ext.controller: debug


cloudpivot:
  bizobject:
    bizmethod-invoke-timeout-seconds: 10      #业务集成默认超时时间，单位：秒
    db:
      type: dm                             #数据库类型名称，支持mysql, oracle, sqlserver, dm, postgresql
  swagger:
    enabled: true #开启Swagger API. API 访问地址: http://hostname:port/context-path/doc.html 或者 http://hostname:port/context-path/swagger-ui.html
    defaultRuleApiOn: true #为true时 swagger显示默认业务规则API
  report:
    datasourceurl: http://127.0.0.1
  license:
    licenseFile: cloudpivot.license #许可证文件
  login:
    dingtalk: false #钉钉方式登录
    accountpassword: false #账号密码登录
    dingtalk&accountpassword: true #钉钉扫码和账号密码方式登录
  attachment: # 文件存储方式
    oss:
      enabled: true     #是否启用
    sftp:
      enabled: false     #是否启用
  api:
    oauth:
      enabled: true   # 是否开启权限校验
      test:
        userid: afeb204e71a9d3050171a9d464e60001      # 模拟测试用户ID，不能用于生产环境
  webmvc:
    corsmappings: true           #配置是否允许跨域，详细参考 云枢帮助中心-开发者手册-解决跨域安全问题
    corsAllowedOrigins: '*'      #跨域请求白名单,多个地址以逗号分割
  dingtalk:
    client:
      dingtalk_server_proxy: http://172.18.14.204/
    is_syn_edu: true               #是否同步家校通讯录
  wechat:
    client:
      aes_key: IjsfohGaWd5nncXxUzOaF7PY8rYoufbf3zgOjd1B0IZ
      aes_token: LI1uHiaQvGer
    proxy:
      default_cp_base_url: http://*************/wx_base_url        #微信基数接口数据代理
      scan_qrconnect: http://*************/wx_scan         #微信扫码登录代理

  task:
    enabled: false                            #是否开启定时任务，在集群部署环境下只开启一个节点的定时任务
  workItem:
    participant_number: 1000                  #待办 传阅 加签 等参与者数量限制
  message:
    profile:
      active: simple                          #消息类型，simple：进程内消息，kafka：kafka中间件，默认simple
  event_push:
    enable: false                             #是否开启事件订阅推送
  orgSync: # 组织增量同步
    enable: true # 是否开启
    retryCount: 3 # 钉钉回调事件处理失败重试次数
    consumeCron: 0/10 * * * * ? # 消息同步日志cron表达式
    defaultPassword: 12345678       #默认创建用户的初始密码，必须大于6位数的长度

caffeine:
  region:
    default: 1000, 12h #默认内存缓存配置 单个region, 最大持有1000个缓存key, 写后12小时过期
  # 启用二级缓存
  l2-cache-open: true
zct:
  yunshuURL: http://************
  base_url: https://zctxc.crrcgc.cc:7773
  sysId: 100034
  sysName: 株辆XCBPM
  secretId: c8d0809ebee946678fdd9a8f7bfb1f11
  secretKey: e46f1cd4ab15f80c0510257c4df4ff4efe58b02850aa728fd93cc82d882273af
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCla6D/HTAGNCjcZ/Byc1cYRF92KXxQjAO+KItm7JRgzYsc2ndyebD8npbX6MgnzppuVZK8pim2CqTGPQycF5lD1sN5ZlPwgWfitVOv0GHYjzediH4XPzqjQ6tgqs5yvizI56CrKfxPFLsgCnqUfJ+/dpWuvlOfj13OxP+jML4y+wIDAQAB
  filterPattern: /login/zct
  privateKey: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDfqi5sY5u3th/rCVO9pr7QWyv+tGZCGGwk46be36OjTrzYQTFygqTBFVx3f6MvLhXFepZQK2ORn5I9yuiW1uPiyEFNDL1GvDe3Dcms3lSTJIPA2gWo1DR43fjSUeJHPgTd3RpJL3tz+whN7Kk/KcPLFSn9Bl2rncmpjM7SB60nhOOARywZ2IbfQdLzdGMYOarMXp9S7IF14PRQ6lZPaKHcNQcCgCxhxYrG09WR5J/DPY52IS7cA2zXXPswLnJmNAZ5gKawdhMO5sr1WqFMCGLVYQJ0jBLw8XDjPFW9D9Zq0SA11sJqBHtYMYAk1TmHsczmlsaTvRXswDUchl2n5wz/AgMBAAECggEBAMFZTpRob16a1HDMOVYDZYScrCWdMaEFl8cz/TdBwSYVlO6lPOeuxFod6zdqYVkRz8RNOs/HEmX6ueMtG2/6CDotvzWqQ514a/mUs7j64cXplPQMBURzXYD28nhuTNwlYvOghW6q21/hrMYgRhbSNe3DN+8Bp8wJtSEbFlc+R5OWkWyZkkCbcxt4xPKN7HPXdlLDU5xl02wh+M6d5fdW/+3JeYQ2LVPqlKOhsoiLkQACI56bDzyLGMFXDXQjCKOuTy9CFgIaKWlBOSjudayF5FhOi23fMPf0OEEwaz7VfjmcLARIJRxggcelRAWC3iCndkW+RnfPFZqax3cpfJfhGBECgYEA9hAx9vWJtrS8s/KBKLVQsA0asU4/wComAOkLqCdxVtoRDiFiF9oLvC+IdgrInzQ42u4jMesr2/9hlQ3pb1N8w7VtEuYML0fuK6jiObF59NbOhipGzJYRFL8Gl5DtTXNiu+O04bGPY1CU/8tSnDl1Wq6+gAg8pEpCrCn3TsKJdTsCgYEA6LJuKHPfrG2q9Vzh/UbwoiKfh8DVkYG2gA0BaHa7GUgQng5HQZueCrcdAH/TKdohoaScORzvALJQvBdnjuLb5zhKZFW7auWRk7GwodOjVDy3wa+O2lOoxT0x76oDJxZh2YqyNytL2MuCKwr9cTb3hf7RIEfSxHaO6dPos04+uw0CgYEAg2vU89zip8XSf53HJDHrHYxyND7hx8X7NENEKZSfcU7ZCx4DYU/hMqqEjN9nlbhzoWLNQ20iqMa2nMCobABAQ9sTnaNm4J3fdEwoP74tr/pGpdvQopaIGveIEs2iINq+4jFVO7H1LosseY5j2fLKXhu0vXcoxGqxP3jOkK9QcV8CgYEAuXwBcx1qXffNHmg7e7jgd11B7OQ5WRMcdlXnmW2LFkvrcxHOmu2AvOnZpFw/5kyKIl8kaCI4IUYCNOwzECKlR1oHcpMkIcF8mU46jcQ1nSAJGcrnBAFLtm1hMLlEahPdS5rTFEbe3qDZ0ZWhEG3QQG8uI50c1J3srVbNVqA4QMECgYAl583n//51+hEthKdBVU57c8H7s/KUReseY36bQjFGyUeQSa7vriwWtiFQRXmu2Lw5FZ57BsLcc9JYo+JG335q2Ewupal+/iBLiPehRYnN+ve2rx5gNPDk+7Gj4OaT3O8aRKMwnqvixjKe1prcq8RUst7Iyg7cQycPIga64NNWcw==
  signatureKey: a44e80af62ae3d3d7792012c4e759a9cd83daa962f51f8a09e0174ee75b3d7c0
oa:
  yunshuURL: http://************
  base_url: http://coa.crrcgc.cc
  filterPattern: /login/oa
  userInfoUri: ${oa.base_url}/service/ctp-user/auth/restore
  appID: ZLXCBPM
  secretKey: RdU5x73Rqt5S5Rt2
mes:
  secret: tr6NOHJeRhLI8u7r/nsYHWolJAtYJ2wWTC7S4x41KT33J60CyLUhfmJBuvWqAXCEO8nqPh+wxm1xnKohTPua5w==