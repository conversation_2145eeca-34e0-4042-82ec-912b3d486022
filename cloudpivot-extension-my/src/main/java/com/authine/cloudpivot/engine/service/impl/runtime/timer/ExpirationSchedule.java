package com.authine.cloudpivot.engine.service.impl.runtime.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.authine.cloudpivot.engine.api.facade.AdminFacade;
import com.authine.cloudpivot.engine.api.facade.DepartmentFacade;
import com.authine.cloudpivot.engine.api.facade.PermissionManagementFacade;
import com.authine.cloudpivot.engine.api.facade.UserFacade;
import com.authine.cloudpivot.engine.api.model.organization.DepartmentModel;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.engine.api.model.permission.AdminPermissionGroupModel;
import com.authine.cloudpivot.engine.api.model.system.AdminModel;
import com.authine.cloudpivot.engine.domain.runtime.BizWorkflowInstance;
import com.authine.cloudpivot.engine.domain.runtime.BizWorkflowToken;
import com.authine.cloudpivot.engine.domain.runtime.BizWorkitem;
import com.authine.cloudpivot.engine.domain.runtime.WorkflowRuntimeStateOperations;
import com.authine.cloudpivot.engine.domain.task.TimerJob;
import com.authine.cloudpivot.engine.domain.workflow.WorkflowTemplate;
import com.authine.cloudpivot.engine.enums.status.UserStatus;
import com.authine.cloudpivot.engine.enums.status.WorkItemApprovalStatus;
import com.authine.cloudpivot.engine.enums.status.WorkflowEndStatus;
import com.authine.cloudpivot.engine.enums.status.WorkflowInstanceStatus;
import com.authine.cloudpivot.engine.enums.status.WorkflowRemarkStatus;
import com.authine.cloudpivot.engine.enums.status.WorkflowTokenStatus;
import com.authine.cloudpivot.engine.enums.type.AdminType;
import com.authine.cloudpivot.engine.enums.type.IMMessageType;
import com.authine.cloudpivot.engine.service.impl.runtime.engine.ParticipantActivityExitHandler;
import com.authine.cloudpivot.engine.service.impl.runtime.engine.WorkflowEngine;
import com.authine.cloudpivot.engine.service.impl.runtime.persistence.BizPersistence;
import com.authine.cloudpivot.engine.service.impl.workflow.util.WorkflowExitHelper;
import com.authine.cloudpivot.engine.service.job.JobCallbackService;
import com.authine.cloudpivot.engine.service.job.JobService;
import com.authine.cloudpivot.engine.service.lock.WorkflowLockService;
import com.authine.cloudpivot.engine.service.runtime.BizWorkflowInstanceService;
import com.authine.cloudpivot.engine.service.runtime.BizWorkitemService;
import com.authine.cloudpivot.engine.service.runtime.WorkflowExceptionService;
import com.authine.cloudpivot.engine.service.runtime.context.InstanceContext;
import com.authine.cloudpivot.engine.service.runtime.ext.ActionResult;
import com.authine.cloudpivot.engine.utils.DateUtils;
import com.authine.cloudpivot.engine.workflow.enums.ActivityType;
import com.authine.cloudpivot.engine.workflow.enums.ParticipantType;
import com.authine.cloudpivot.engine.workflow.enums.RejectedSubmitStrategy;
import com.authine.cloudpivot.engine.workflow.enums.TimeoutStrategy;
import com.authine.cloudpivot.engine.workflow.model.activity.Activity;
import com.authine.cloudpivot.engine.workflow.model.activity.ParticipantActivity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大于8.2.36版本可删除此文件
 *
 * <AUTHOR> Create in 2025/07/07
 */
@Component
@Slf4j
public class ExpirationSchedule {

    private final int TIMEOUTSET = 10;
    @Autowired
    private AdminFacade adminService;
    @Autowired
    private UserFacade userFacade;
    @Autowired
    private PermissionManagementFacade permissionManagementFacade;
    @Autowired
    private WorkflowEngine workflowEngine;
    @Autowired
    private BizPersistence bizPersistence;
    @Autowired
    private BizWorkitemService bizWorkitemService;

    @Autowired
    private BizWorkflowInstanceService bizWorkflowInstanceService;
    @Autowired
    private JobService jobService;
    @Autowired
    private JobCallbackService JobCallbackService;
    @Autowired
    private DepartmentFacade departmentFacade;
    @Autowired
    private WorkflowExceptionService wfExceptionService;
    @Autowired
    private WorkflowLockService workflowLockService;

    /**
     * 超时逻辑处理
     * @return
     * @throws Exception
     */
    public void runOnce() {
        List<TimerJob> timerJobs = jobService.findTimeoutJob();
        if (CollectionUtils.isEmpty(timerJobs)) {
            return;
        }
        timerJobs.forEach(this::executeTimeJob);
    }

    /**
     * @param timerJob
     */
    private void executeTimeJob(TimerJob timerJob) {
        String taskId = timerJob.getTaskId();
        if (StringUtils.isBlank(taskId)) {
            jobService.delete(timerJob);
            return;
        }
        Date triggerTime = timerJob.getTriggerTime();
        Date currentTime = new Date();
        //triggerTime为空说明是历史数据
        if (triggerTime == null) {
            triggerTime = getTriggerTime(taskId);
            if (triggerTime == null) {
                JobCallbackService.callable(taskId, Boolean.FALSE);
                return;
            }
        }
        //如果任务已经超时
        if (triggerTime.before(currentTime)) {
            //超时任务执行，警告任务丢弃
            if (taskId.endsWith("_timeout")) {
                executeTimeout(timerJob.getMethodParams());
            }
            JobCallbackService.callable(taskId, Boolean.TRUE);
            // 十分钟内过期
        } else if (triggerTime.before(DateUtils.addMinutes(currentTime, TIMEOUTSET))) {
            jobService.addCronTask(taskId, timerJob.getMethodParams(), timerJob.getCronExpression());
            //添加到定时器
        } else {
            timerJob.setTriggerTime(triggerTime);
            jobService.update(timerJob);
        }
    }

    private Date getTriggerTime(String taskId) {
        Date triggerTime = null;
        String id = taskId.substring(0, taskId.indexOf('_'));
        if (StringUtils.contains(taskId, "workflow")) {
            BizWorkflowInstance bizWorkflowInstance = bizWorkflowInstanceService.get(id);
            if (bizWorkflowInstance == null) {
                return null;
            }

            //超时任务
            if (taskId.endsWith("_timeout")) {
                triggerTime = bizWorkflowInstance.getAllowedTime();
            }
            //超时警告1
            if (taskId.endsWith("_warning1")) {
                triggerTime = bizWorkflowInstance.getTimeoutWarning1();
            }
            //超时警告2
            if (taskId.endsWith("_warning2")) {
                triggerTime = bizWorkflowInstance.getTimeoutWarning2();
            }
            if (taskId.endsWith("_warning3")) {
                triggerTime = bizWorkflowInstance.getTimeoutWarning3();
            }
            if (taskId.endsWith("_warning4")) {
                triggerTime = bizWorkflowInstance.getTimeoutWarning4();
            }
            if (taskId.endsWith("_warning5")) {
                triggerTime = bizWorkflowInstance.getTimeoutWarning5();
            }
        } else {
            BizWorkitem bizWorkitem = bizWorkitemService.get(id);
            if (bizWorkitem == null) {
                return null;
            }
            //超时任务
            if (taskId.endsWith("_timeout")) {
                triggerTime = bizWorkitem.getAllowedTime();
            }
            //超时警告1
            if (taskId.endsWith("_warning1")) {
                triggerTime = bizWorkitem.getTimeoutWarning1();
            }
            //超时警告2
            if (taskId.endsWith("_warning2")) {
                triggerTime = bizWorkitem.getTimeoutWarning2();
            }
            if (taskId.endsWith("_warning3")) {
                triggerTime = bizWorkitem.getTimeoutWarning3();
            }
            if (taskId.endsWith("_warning4")) {
                triggerTime = bizWorkitem.getTimeoutWarning4();
            }
            if (taskId.endsWith("_warning5")) {
                triggerTime = bizWorkitem.getTimeoutWarning5();
            }
        }

        return triggerTime;
    }

    public void executeWorkflowTimeout(String jsonParam) {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        String workflowInstanceId = jsonObject.getString("workflowInstanceId");
        String notifyStrategy = jsonObject.getString("notifyStrategy");
        int messageType = jsonObject.getIntValue("messageType");
        String jobKey = workflowInstanceId + "_" + messageType;
        //先获取分布式锁，防止集群部署多次触发
        Lock lock = workflowLockService.getTimeJobLock(jobKey);
        boolean lockSuccess = false;
        try {
            lockSuccess = lock.tryLock(TIMEOUTSET, TimeUnit.SECONDS);
            //获取不到锁则放弃执行
            if (!lockSuccess) {
                return;
            }
            BizWorkflowInstance bizWorkflowInstance = bizWorkflowInstanceService.get(workflowInstanceId);
            if (bizWorkflowInstance == null || bizWorkflowInstance.getState() != WorkflowInstanceStatus.PROCESSING) {
                return;
            }
            if (messageType == IMMessageType.FINALEXPIRED.getIndex()) {
                expireWorkflow(bizWorkflowInstance);
                return;
            }
            if (messageType == IMMessageType.TimeEXPIRED.getIndex()) {
                long allowedTime = 0;
                if (bizWorkflowInstance.getAllowedTime() != null) {
                    allowedTime = bizWorkflowInstance.getAllowedTime().getTime();
                }
                long currentTime = System.currentTimeMillis();
                sendWorkflowNotice(workflowInstanceId, IMMessageType.TimeEXPIRED,
                        String.valueOf((allowedTime - currentTime) / 1000 / 60.0), notifyStrategy);
            }
        } catch (Exception e) {
            log.error("executeTimeout Exception ", e);
        } finally {
            if (lockSuccess) {
                lock.unlock();
            }
        }
    }

    /**
     * 执行超时任务
     * @param jsonParam
     */
    public void executeTimeout(String jsonParam) {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        String taskId = jsonObject.getString("taskId");
        TimerJob timerJob = jobService.findByTaskId(taskId);
        if (timerJob != null && timerJob.getStatus() == 0) {
            return;
        }
        if ("activity".equals(jsonObject.get("type"))) {
            executeActivityTimeout(jsonParam);
            return;
        }
        if (jsonObject.containsKey("workflowInstanceId")) {
            executeWorkflowTimeout(jsonParam);
            return;
        }
        String workitemId = jsonObject.getString("workitemId");
        String notifyStrategy = jsonObject.getString("notifyStrategy");
        int messageType = jsonObject.getIntValue("messageType");
        String jobKey = workitemId + "_" + messageType;
        //先获取分布式锁，防止集群部署多次触发
        Lock lock = workflowLockService.getTimeJobLock(jobKey);
        boolean lockSuccess = false;
        try {
            lockSuccess = lock.tryLock(TIMEOUTSET, TimeUnit.SECONDS);
            //获取不到锁则放弃执行
            if (!lockSuccess) {
                return;
            }
            BizWorkitem bizWorkitem = bizWorkitemService.get(workitemId);
            if (bizWorkitem == null) {
                return;
            }
            if (messageType == IMMessageType.FINALEXPIRED.getIndex()) {
                expire(bizWorkitem);
                return;
            }
            if (messageType == IMMessageType.TimeEXPIRED.getIndex()) {
                long allowedTime = 0;
                if (bizWorkitem.getAllowedTime() != null) {
                    allowedTime = bizWorkitem.getAllowedTime().getTime();
                }
                long currentTime = System.currentTimeMillis();
                warn(bizWorkitem, String.valueOf((allowedTime - currentTime) / 1000 / 60.0), notifyStrategy);
            }
        } catch (Exception e) {
            log.error("executeTimeout Exception ", e);
        } finally {
            if (lockSuccess) {
                lock.unlock();
            }
        }
    }

    /**
     * 超时警告
     * @param bizWorkItem
     * @param expireTime
     */
    private void warn(BizWorkitem bizWorkItem, String expireTime, String notifyStrategy) {
        bizWorkItem.setTimeoutWarningCount(bizWorkItem.getTimeoutWarningCount() + 1);
        bizWorkitemService.update(bizWorkItem);
        this.sendNotice(bizWorkItem, expireTime, IMMessageType.TimeEXPIRED, notifyStrategy);
    }

    /**
     * 超时
     * @param bizWorkItem
     */
    public void expire(BizWorkitem bizWorkItem) {
        try {
            switch (bizWorkItem.getTimeoutStrategy()) {
                case APPROVE:
                    this.executeAutoPass(bizWorkItem);
                    break;
                case FORWARD_MANAGER:
                    this.transferManager(bizWorkItem);
                    break;
                case CIRCULATE_MANAGER:
                    this.circulateManager(bizWorkItem);
                    break;
                case CANCLE_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkItem.getInstanceId(), false, WorkflowEndStatus.TIMEOUT_END);
                    break;
                case END_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkItem.getInstanceId(), true, WorkflowEndStatus.TIMEOUT_END);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("超时策略事件调用错误", e);
            //throw new EngineRuntimeException(ErrorCode.ExpirationHandle, e.getMessage());
        }
        this.sendNotice(bizWorkItem, "", IMMessageType.FINALEXPIRED, "timeoutNotifyStrategy");
    }

    public void expireWorkflow(BizWorkflowInstance bizWorkflowInstance) {
        try {
            WorkflowInstanceStatus state = bizWorkflowInstance.getState();
            if (state != WorkflowInstanceStatus.PROCESSING) {
                return;
            }
            switch (bizWorkflowInstance.getTimeoutStrategy()) {
                case MARK_TIMEOUT:
                    markBizWorkitemTimeout(bizWorkflowInstance);
                    break;
                case CANCLE_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkflowInstance.getId(), false, WorkflowEndStatus.TIMEOUT_END);
                    break;
                case END_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkflowInstance.getId(), true, WorkflowEndStatus.TIMEOUT_END);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("流程超时策略事件调用错误", e);
        }
        sendWorkflowNotice(bizWorkflowInstance.getId(), IMMessageType.FINALEXPIRED, "", null);
    }

    private void markBizWorkitemTimeout(BizWorkflowInstance bizWorkflowInstance) {
        List<BizWorkitem> bizWorkitemList = bizWorkitemService.getListByInstanceId(bizWorkflowInstance.getId());
        if (CollectionUtils.isEmpty(bizWorkitemList)) {
            return;
        }

        for (BizWorkitem bizWorkitem : bizWorkitemList) {
            bizWorkitem.setAllowedTime(bizWorkflowInstance.getAllowedTime());
            bizWorkitem.setTimeoutWarning1(null);
            bizWorkitem.setTimeoutWarning2(null);
            bizWorkitem.setTimeoutWarning3(null);
            bizWorkitem.setTimeoutWarning4(null);
            bizWorkitem.setTimeoutWarning5(null);
            bizWorkitem.setTimeoutWarnings(null);
            //bizWorkitem.setTimeoutStrategy(TimeoutStrategy.MARK_TIMEOUT);
            bizWorkitemService.update(bizWorkitem);
        }
    }


    /**
     * 获取指定用户的上级
     * @param owner
     * @return
     */
    private String getManager(String owner, String appCode) {
        //上级主管
        String managerId = getOUManagersOfUser(owner);
        if (StringUtils.isNotBlank(managerId)) {
            return managerId;
        }

        //数据管理员
        managerId = getDataAdmin(appCode);
        if (StringUtils.isNotBlank(managerId)) {
            return managerId;
        }

        //系统管理员
        managerId = getSysAdmin();
        return managerId;
    }

    private String getDataAdmin(String appCode) {
        List<AdminModel> managerList = adminService.getByAdminType(AdminType.APP_MNG);
        if (CollectionUtils.isEmpty(managerList)) {
            return null;
        }
        final Map<String, List<AdminModel>> managerMap =
                managerList.stream().collect(Collectors.groupingBy(AdminModel::getUserId));
        final List<String> adminIds = Lists.newArrayList();

        for (Map.Entry<String, List<AdminModel>> entry : managerMap.entrySet()) {
            for (AdminModel ma : entry.getValue()) {
                List<AdminPermissionGroupModel> adminGroups =
                        permissionManagementFacade.getAdminPermissionGroupListByAdminId(ma.getId());
                List<String> appCodes =
                        adminGroups.stream().map(AdminPermissionGroupModel::getAppPackages).collect(Collectors.toList());
                if (appCodes.contains(appCode)) {
                    adminIds.add(entry.getKey());
                    break;
                }
            }
        }
        for (String ad : adminIds) {
            UserModel user = userFacade.get(ad);
            if (user != null && user.getStatus() == UserStatus.ENABLE) {
                return user.getId();
            }
        }
        return null;
    }

    private String getSysAdmin() {
        //数据管理员
        List<AdminModel> managersOfUsers = adminService.getByAdminType(AdminType.SYS_MNG);
        if (CollectionUtils.isEmpty(managersOfUsers)) {
            return null;
        }
        for (AdminModel admin : managersOfUsers) {
            UserModel user = userFacade.get(admin.getUserId());
            if (user != null && user.getStatus() == UserStatus.ENABLE) {
                return user.getId();
            }
        }
        return null;
    }

    private String getOUManagersOfUser(String userId) {
        final UserModel user = userFacade.get(userId);
        if (user == null) {
            return null;
        }

        String departmentId = user.getDepartmentId();
        // 部门经理
        String mid = null;
        while (StringUtils.isNotEmpty(departmentId)) {
            final DepartmentModel department = departmentFacade.get(departmentId);
            if (department == null) {
                return null;
            }
            UserModel managerUser = null;
            if (StringUtils.isNotBlank(department.getManagerId())) {
                managerUser = userFacade.getByUserIdAndCorpId(department.getManagerId(), user.getCorpId());
            }
            //不能转给自己
            if (managerUser == null || userId.equals(managerUser.getId())) {
                departmentId = department.getParentId();
            } else {
                mid = managerUser.getId();
                break;
            }
        }
        return mid;
    }

    /**
     * 传阅上级主管
     */
    private void circulateManager(BizWorkitem workItem) {
        String managerId = this.getManager(workItem.getParticipant(), workItem.getAppCode());
        if (!StringUtils.isEmpty(managerId)) {
            workflowEngine.getLockRun().acquireWorkflowLock(workItem.getInstanceId(), () -> {
                //todo:传阅上级主管
                workflowEngine.circulateWorkItem(managerId, workItem);
            });
        }
    }

    /**
     * 转交上级主管
     */
    private void transferManager(BizWorkitem workItem) {
        String managerId = this.getManager(workItem.getParticipant(), workItem.getAppCode());
        if (!StringUtils.isEmpty(managerId)) {
            workflowEngine.getLockRun().acquireWorkflowLock(workItem.getInstanceId(), () -> {
                workflowEngine.forwarderWorkItem(workItem.getParticipant(), workItem.getId(), managerId, "");
            });
        }
    }

    /**
     * 自动审批通过
     */
    private void executeAutoPass(BizWorkitem workItem) {
        workflowEngine.getLockRun().acquireWorkflowLock(workItem.getInstanceId(), () -> {
            if (log.isTraceEnabled()) {
                log.trace("自动审批:workItem-{}", JSON.toJSONString(workItem));
            }
            BizWorkitem bizWorkitem = bizWorkitemService.get(workItem.getId());
            if (bizWorkitem == null) {
                return;
            }
            WorkflowRuntimeStateOperations.optWorkItemApprovalStatus(bizWorkitem, WorkItemApprovalStatus.TIMEOUT_PASS);
            bizWorkitem.setEndStatus(WorkflowRemarkStatus.TIMEOUT_PASS.name());
            bizPersistence.onWorkItemApprove(bizWorkitem);
            ActionResult result = workflowEngine.afterApproveWorkItem(bizWorkitem);
            if (result.isInterrupted()) {
                log.warn("------------------------Expiration Exception ： ", result.getException());
                wfExceptionService.setExceptioned(workItem.getInstanceId(), workItem.getWorkflowTokenId(), null,
                        "异常方法名： " + result.getException().getStackTrace()[2].getMethodName() + " 异常类名：" + result.getException().getStackTrace()[2].getFileName(),
                        result.getException());
            }
        });
    }

    /**
     * 通知处理人
     * @return
     */
    private void sendNotice(BizWorkitem workItem, String content, IMMessageType msgType, String notifyStrategy) {
        InstanceContext.getInstanceContext(workItem.getInstanceId(), workflowEngine).sendMessage(workItem, msgType,
                workItem.getParticipant(), content, workItem, notifyStrategy);
    }

    private void sendWorkflowNotice(String workflowInstanceId, IMMessageType msgType, String content, String notifyStrategy) {
        InstanceContext instanceContext = InstanceContext.getInstanceContext(workflowInstanceId, workflowEngine);
        instanceContext.sendMessage(instanceContext, msgType, null, content, null, notifyStrategy);
    }

    public void executeActivityTimeout(String jsonParam) {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        String workflowTokenId = jsonObject.getString("workflowTokenId");
        String notifyStrategy = jsonObject.getString("notifyStrategy");
        String timeoutStrategy = jsonObject.getString("timeoutStrategy");
        String allowedTime = jsonObject.getString("allowedTime");
        int messageType = jsonObject.getIntValue("messageType");
        String rejectedSubmitStrategy = jsonObject.getString("rejectedSubmitStrategy");
        boolean submitToReject = StringUtils.equals(rejectedSubmitStrategy, RejectedSubmitStrategy.SUBMIT_TO_REJECT.name());
        String jobKey = workflowTokenId + "_" + messageType;
        //先获取分布式锁，防止集群部署多次触发
        Lock lock = workflowLockService.getTimeJobLock(jobKey);
        boolean lockSuccess = false;
        try {
            lockSuccess = lock.tryLock(TIMEOUTSET, TimeUnit.SECONDS);
            //获取不到锁则放弃执行
            if (!lockSuccess) {
                return;
            }
            BizWorkflowToken workflowToken = workflowEngine.getWorkflowCoreApi().getBizWorkflowTokenService().get(workflowTokenId);
            if (workflowToken == null || workflowToken.getState() != WorkflowTokenStatus.UNFINISHED) {
                return;
            }
            if (messageType == IMMessageType.FINALEXPIRED.getIndex()) {
                expireActivity(workflowToken, TimeoutStrategy.get(timeoutStrategy), submitToReject);
                return;
            }
            if (messageType == IMMessageType.TimeEXPIRED.getIndex()) {
                warnActivity(allowedTime, workflowToken, notifyStrategy);
            }
        } catch (Exception e) {
            log.error("executeTimeout Exception ", e);
        } finally {
            if (lockSuccess) {
                lock.unlock();
            }
        }
    }

    public void expireActivity(BizWorkflowToken bizWorkflowToken, TimeoutStrategy timeoutStrategy, boolean submitToReject) {
        List<BizWorkitem> workItems = bizWorkitemService.getListByWorkflowTokenId(bizWorkflowToken.getId());
        try {
            if (timeoutStrategy == null) {
                return;
            }
            switch (timeoutStrategy) {
                case APPROVE:
                    workItems.forEach(this::executeAutoPass);
                    break;
                case CIRCULATE_MANAGER:
                    workItems.forEach(this::transferManager);
                    break;
                case FORWARD_MANAGER:
                    workItems.forEach(this::circulateManager);
                    break;
                case CANCLE_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkflowToken.getInstanceId(), false, WorkflowEndStatus.TIMEOUT_END);
                    break;
                case END_WORKFLOW:
                    workflowEngine.endWorkflow(bizWorkflowToken.getInstanceId(), true, WorkflowEndStatus.TIMEOUT_END);
                    break;
                case REJECT:
                    workItems.forEach(bizWorkitem -> executeAutoReject(bizWorkitem, bizWorkflowToken, submitToReject));
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("节点超时策略事件调用错误", e);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(workItems)){
            workItems.forEach(bizWorkItem -> this.sendNotice(bizWorkItem, "", IMMessageType.FINALEXPIRED, "timeoutNotifyStrategy"));
        }
    }

    private void warnActivity(String activityAllowedTime,
                              BizWorkflowToken bizWorkflowToken,
                              String notifyStrategy) {
        long allowedTime;
        if (StringUtils.isNotBlank(activityAllowedTime)) {
            allowedTime = bizWorkflowToken.getStartTime().getTime() + Long.parseLong(activityAllowedTime);
        } else {
            allowedTime = 0;
        }
        long currentTime = System.currentTimeMillis();
        List<BizWorkitem> workItems = bizWorkitemService.getListByWorkflowTokenId(bizWorkflowToken.getId());
        workItems.forEach(bizWorkitem -> warn(bizWorkitem, String.valueOf((allowedTime - currentTime) / 1000 / 60.0), notifyStrategy));
    }

    /**
     * 自动驳回到上一个节点
     */
    private void executeAutoReject(BizWorkitem workItem, BizWorkflowToken bizWorkflowToken, boolean submitToReject) {
        List<String> rejectToActivityCodes = getRejectActivities(bizWorkflowToken.getInstanceId(), bizWorkflowToken);
        workflowEngine.getLockRun().acquireWorkflowLock(workItem.getInstanceId(), () -> {
            WorkflowRuntimeStateOperations.optWorkItemApprovalStatus(workItem, WorkItemApprovalStatus.TIMEOUT_REJECT);
            workflowEngine.rejectWorkItem(workItem.getParticipant(), workItem, String.join(",", rejectToActivityCodes), submitToReject);
        });
    }

    protected List<String> getRejectActivities(String workflowInstanceId,
                                               BizWorkflowToken bizWorkflowToken) {
        BizWorkflowToken currentWorkflowToken = bizWorkflowToken;
        String activityCode = currentWorkflowToken.getActivityCode();
        BizWorkflowInstance instance = bizWorkflowInstanceService.get(workflowInstanceId);
        if (instance == null) {
            return Collections.emptyList();
        }
        List<String> rejectActivities = Lists.newArrayList();
        WorkflowTemplate workflowTemplate = workflowEngine.getWorkflowCoreApi().getWorkflowTemplateService().getPublishedTemplate(instance.getWorkflowCode(), instance.getWorkflowVersion());
        Map<String, Activity> activityMap = workflowTemplate.getActivities().stream().collect(Collectors.toMap(Activity::getActivityCode, Function.identity()));
        List<BizWorkflowToken> workflowTokens = workflowEngine.getWorkflowCoreApi().getBizWorkflowTokenService().getByInstanceId(workflowInstanceId);
        final Activity activity = activityMap.get(activityCode);
        if (activity instanceof ParticipantActivity) {
            Activity preActivity = null;
            while (true) {
                if (currentWorkflowToken == null) {
                    break;
                }
                preActivity = activityMap.get(currentWorkflowToken.getSourceActivityCode());
                if (preActivity.getActivityType() == ActivityType.SUB_INSTANCE) {
                    break;
                }
                if (preActivity.getActivityType() == ActivityType.PARTICIPANT) {
                    // 如果上个节点也是驳回的，继续往前找
                    String preActivityCode = preActivity.getActivityCode();
                    int currentTokenId = currentWorkflowToken.getTokenId();
                    BizWorkflowToken preToken = workflowTokens.stream().filter(item -> StringUtils.equals(item.getActivityCode(), preActivityCode) && item.getTokenId() < currentTokenId).max(Comparator.comparing(BizWorkflowToken::getTokenId)).orElse(null);
                    if (!isRejected((ParticipantActivity) preActivity, preToken) && !StringUtils.equals(preActivityCode, bizWorkflowToken.getActivityCode())) {
                        break;
                    }
                }
                String tempActivityCode = preActivity.getActivityCode();
                int tokenId = currentWorkflowToken.getTokenId();
                currentWorkflowToken = workflowTokens.stream().filter(item -> StringUtils.equals(item.getActivityCode(), tempActivityCode) && item.getTokenId() < tokenId).max(Comparator.comparing(BizWorkflowToken::getTokenId)).orElse(null);
            }
            if (preActivity.getActivityType() == ActivityType.PARTICIPANT || preActivity.getActivityType() == ActivityType.SUB_INSTANCE) {
                rejectActivities.add(preActivity.getActivityCode());
            }
        }
        //能驳回的节点必须是已经流转过的节点
        Set<String> workflowTokenSet = CollectionUtils.isEmpty(workflowTokens) ? new HashSet<>() : workflowTokens.stream().map(BizWorkflowToken::getActivityCode).collect(Collectors.toSet());
        List<String> result = Lists.newArrayList();
        List<String> allActivityCodes = Lists.newArrayList();
        for (String rejectActivity : rejectActivities) {
            if (workflowTokenSet.contains(rejectActivity) && !allActivityCodes.contains(rejectActivity)) {
                allActivityCodes.add(rejectActivity);
                if (currentWorkflowToken != null && StringUtils.equals(currentWorkflowToken.getSourceActivityCode(), rejectActivity)) {
                    result.add(rejectActivity);
                }
            }
        }
        return result;
    }


    private boolean isRejected(ParticipantActivity activity, BizWorkflowToken workflowToken) {
        if (activity.getParticipantType() == ParticipantType.MULTI_PARTICIPANT
                && !StringUtils.isEmpty(activity.getDisapproveExit())) {
            Boolean isDisapproveExitEq0 = WorkflowExitHelper.validateDisapproveExitEq0(activity.getDisapproveExit());
            if (isDisapproveExitEq0) {
                return false;
            }
            return new ParticipantActivityExitHandler().getExit(
                    activity.getDisapproveExit(),
                    workflowToken.getItemCount(),
                    workflowToken.getDisapprovalCount());
        }
        if (activity.getParticipantType() == ParticipantType.SINGLE_PARTICIPANT) {
            return workflowToken.getDisapprovalCount() > 0;
        }
        return false;
    }


}


