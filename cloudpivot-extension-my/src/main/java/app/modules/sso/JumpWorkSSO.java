package app.modules.sso;


import app.modules.configuration.ZCTConfig;

import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.authine.cloudpivot.engine.api.facade.OrganizationFacade;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.web.sso.exception.AccessTokenInvalidateException;
import com.authine.cloudpivot.web.sso.security.H3User;
import com.authine.cloudpivot.web.sso.security.UserDetailsServiceImpl;
import com.authine.cloudpivot.web.sso.template.ResponseTypeEnum;
import com.authine.cloudpivot.web.sso.template.SimpleOAuth2Template;
import com.authine.cloudpivot.web.sso.template.SimpleTemplateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

/**
 * 中车通单点
 */
@Component
@Slf4j
public class JumpWorkSSO extends SimpleOAuth2Template {

    @Autowired
    protected OrganizationFacade organizationFacade;
    @Autowired
    private UserDetailsServiceImpl userDetailsService;




    JumpWorkSSO() {
        super(SimpleTemplateConfig.builder()
                .requestMatcher(new AntPathRequestMatcher("/login/jump", HttpMethod.GET.name()))
                .apiMethod(Method.POST)
                .responseType(ResponseTypeEnum.JSON)
                .mobile(false)
                .redirectClientID("api")
                .build());
    }

    @Override
    public String getSourceId(String code) throws Exception {

        H3User h3User = this.userDetailsService.loadUserByUsername(code);
        if (h3User != null) {
            log.info("h3User = {}", JSON.toJSONString(h3User));
            String sourceId = h3User.getUserid();
            return sourceId;
        }
        log.warn(" h3 中无此用户：{}", code);
        throw new AccessTokenInvalidateException(" h3 中无此用户：" + code);
    }


    /**
     * @param sourceId 云枢原本是sourceId。---idaas中没有同步，所以使用H3User中的userid
     * @return com.authine.cloudpivot.engine.api.model.organization.UserModel
     * <AUTHOR>
     **/
    @Override
    public UserModel getUser(Object sourceId) {
        return organizationFacade.getUser(String.valueOf(sourceId));
    }
}