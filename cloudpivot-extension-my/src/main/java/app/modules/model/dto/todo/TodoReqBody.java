package app.modules.model.dto.todo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2022/11/07 10:33
 **/
@Getter
@Setter
public class TodoReqBody implements Serializable {

    /**
     * 消息ID
     */
    private String id;
    /**
     * 消息ID（用于区分是使用id还是msg_id更新数据）
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private transient String msgId;
    /**
     * 应用key（用于识别OA等三方应用租户）
     */
    private String appKey;
    /**
     * 流程实例ID（仅适用于OA待办）
     */
    @JSONField(name = "flowId")
    private String workflowId;
    /**
     * 企业ID
     */
    private Integer companyId;
    /**
     * 数据来源（1:OA, 2:第三方业务系统）
     */
    private String source;
    /**
     * 业务系统ID
     * 1. 业务系统注册到OA，由OA提供
     * 2. 通过应用商店添加的第三方业务系统，由第三方业务系统提供
     */
    private String sysId;
    /**
     * 业务系统名称
     */
    private String sysName;
    /**
     * 预留字段，业务系统会话头像
     */
    private String sysIcon;
    /**
     * 待办类型
     */
    private String type;
    /**
     * 业务系统内部类型
     */
    private String bizType;
    /**
     * 员工号
     */
    private String userNo;
    /**
     * 待办标题
     */
    private String title;
    /**
     * 待办内容
     */
    private String content;
    /**
     * 待办发布时间
     */
    private String releaseDate;
    /**
     * 主办人
     */
    private String sponsor;
    /**
     * 分厂信息
     */
    private String subsidiary;
    /**
     * 是否紧急
     */
    private String urgent;
    /**
     * 第三方待办页面URL地址，用于在PC端展示
     */
    private String pcUrl;
    /**
     * 第三方待办页面URL地址，用于在APP端展示
     */
    private String h5Url;
    /**
     * 预留字段，消息卡片类型
     */
    private String cardType;
    /**
     * 操作类型（1:待办转已办2：待阅转已阅）
     */
    private Integer actionType;
    /**
     * 扩展字段
     */
    private String extend;

}
