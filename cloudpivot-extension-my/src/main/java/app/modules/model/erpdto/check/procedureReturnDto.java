package app.modules.model.erpdto.check;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class procedureReturnDto {

    private String COMPANY_CODE;

    private String ORG_ID;

    private String USER_CODE;

    private String USER_NAME;

    private String USER_UNIQUE_ID;

    private String PROC_NAME;

    private String PROC_DESC;

    private String START_TIME;

    private String END_TIME;

    private String RETURN_FLAG;

    private String RETURN_MSG;
}
