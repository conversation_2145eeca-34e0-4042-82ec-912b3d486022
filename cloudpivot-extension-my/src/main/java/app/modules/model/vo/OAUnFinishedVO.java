package app.modules.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class OAUnFinishedVO {
    private String _MSG_;
    private String Code;
    private String Message;
    private String AppID;
    private String Count;
    private String _TIME_;
    private String timestamp;
    private WorkItemOA data;


    @Data
    public static class WorkItemOA{
        private PageInfo pageInfo;
        private List<Detail> list;
        private String info;
    }

    @Data
    public static class PageInfo{
        private String pageInfo;
        private String totalPage;
        private String totalCount;
        private String nowPage;
    }

    @Data
    public static class Detail{
        private String cmpyCode;
        private String docNumber;
        private String doneCmpyName;
        private String doneDeptCode;
        private String doneDeptName;
        private String doneUserCode;
        private String doneUserName;
        private String draftCmpy;
        private String draftDept;
        private String draftTime;
        private String draftUser;
        private String draftUserCode;
        private String sendCmpyName;
        private String sendDeptCode;
        private String sendDeptName;
        private String sendUserCode;
        private String sendUserName;
        private String todoAlias;
        private String todoAppUrl;
        private String todoUrl;
        private String todoCatalog;
        private String todoCode;
        private String todoEmergy;
        private String todoGroup;
        private String todoId;
        private String todoNode;
        private String todoSendTime;
        private String todoTitle;
        private String todoShortName;
        private String todoFullName;
        private String withdrawFlag;
        private String withdrawUser;
        private String mindContent;
        private String remindContent;
        private String hasFlow;
        private String isCreater;
        private String operType;
    }
}
