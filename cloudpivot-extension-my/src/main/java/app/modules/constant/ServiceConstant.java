package app.modules.constant;

public class ServiceConstant {

    /**
     * 项目产品信息 服务编码
     */
    public static String PROJ_ITEM_CUR_SERVICE = "DC_MD_PROJ_ITEM_CUR_V";

    /**
     * 在产项目对应的产品列表 方法编码
     */
    public static String PROJ_ITEM_CUR_METHOD = "DC_MD_PROJ_ITEM_CUR_V";


    /**
     * 项目产品信息 服务编码
     */
    public static String EPM_BOM = "epm_bom";

    public static String CIM_EPM_EDM_BOM_DELETE_1 = "CIM_EPM_EDM_BOM_DELETE_1";

    /**
     * 在产项目对应的产品列表 方法编码
     */
    public static String EPM_BOM_SELECT = "CIM_EPM_EDM_BOM_SELECT_1";

    /**
     * 在产项目对应的产品列表 方法编码
     */
    public static String EPM_BOM_INSERT = "CIM_EPM_EDM_BOM_INSERT_1";

    public static String ERP_INV_MASTER_QUERY_SERVICE = "epm_erp_inv_master_query";

    public static String ERP_SCRAP_MATERIAL_QUERY_METHOD = "erp_bjyl_query";

    public static String SEARCH_BOM_IS_EXISTS = "search_bom_is_exists";

    public static String SEARCH_BOM_BY_PARENT_CODE = "search_bom_by_parent_code";

    // ERP CHECK
    public static String ERP_EPM_CHECK = "erp_epm_check";

    public static String CIM_EPM_ITEM_DEFAULT_SP = "cim_epm_item_default_sp";

    public static String CIM_EPM_ITEM_CHECK_SP = "cim_epm_item_check_sp";

    public static String CIM_EPM_RETURN_PROCEDURE_QUERY = "cim_epm_return_procedure_query";

    public static String CIM_EPM_BOM_CHECK_SP = "cim_epm_bom_check_sp";

    public static String CIM_EPM_BOM_PUSH_SP = "cim_epm_bom_push_sp";

    public static String CIM_EPM_ITEM_PUSH_SP = "cim_epm_item_push_sp";

    public static String CIM_EPM_RETURN_PROCEDURE_CLEAN = "cim_epm_return_procedure_clean";

    public static String CIM_EPM_ITEM_CHECK_RESULT = "cim_epm_item_check_result";

    public static String CIM_EPM_ITEM_CHECK_RESULT_SELECT_1 = "CIM_EPM_ITEM_CHECK_RESULT_SELECT_1";

    public static String CIM_EPM_BOM_CHECK_RESULT = "cim_epm_bom_check_result";

    public static String CIM_EPM_BOM_CHECK_RESULT_SELECT_1 = "CIM_EPM_BOM_CHECK_RESULT_SELECT_1";

    public static String EDM_STAFF = "edm_staff";

    public static String USER_CODE_TO_UNIQUE_ID = "user_code_to_unique_id";
}
