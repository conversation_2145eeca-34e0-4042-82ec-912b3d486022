package app.modules.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "zct")
public class ZCTConfig {
    private String base_url;
    private Integer sysId;
    private String sysName;
    private String secretId;
    private String secretKey;
    private String filterPattern;
    private String userInfoUri;
    private String publicKey;
    private String privateKey;
    private String signatureKey;

}
