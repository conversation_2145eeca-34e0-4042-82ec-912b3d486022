package app.modules.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "oa")
public class OAConfig {
    /**
     *
     */
    private String filterPattern;
    /**
     *
     */
    private String userInfoUri;

    /**
     * 系统唯一识别码
     */
    private String appID;
    /**
     * 密码串
     */
    private String secretKey;
    private String yunshuURL;
}
