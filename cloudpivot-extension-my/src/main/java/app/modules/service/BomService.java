package app.modules.service;

import app.modules.model.dto.processflow.ProcessFlowSyncRequest;
import app.modules.model.entity.Bom;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.erpdto.bom.ErpBomItem;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.erpdto.bom.ErpProjectItem;

import java.util.List;
import java.util.Map;

public interface BomService {
    List<ProcessProjectInfo> processFlowToBoms(Map<ProcessProjectInfo, List<ProcessFlow>> processFlows);

    /**
     * 根据 projectCode、productCode、userId 读取BON数据
     *
     * @param projectCode
     * @param productCode
     * @param userId
     * @return
     */
    List<Bom> read(String projectCode, String productCode, String userId);


    /**
     * epm bom 转变成 erp bom
     *
     * @param bomList
     * @return
     */
    public List<ErpBomItem> epmBom2ErpBom(List<Bom> bomList);

    /**
     * 将 erp bom 数据 传入 erp
     *
     * @param serviceCode
     * @param methodCode
     * @param listInputParams
     */
    public void epmBomSendErpBom(String serviceCode, String methodCode, List<Map<String, Object>> listInputParams);

    /**
     * 根据项目 将epm中的bom数据传输到erp
     *
     * @param processFlowSyncRequest
     * @return
     */
    void epmBomSendErpByProject(ProcessFlowSyncRequest processFlowSyncRequest);

    /**
     * 根据用户 id清空数据
     */
    void deleteAllById();

    void deleteErpBom(ErpProjectItem item);
}
