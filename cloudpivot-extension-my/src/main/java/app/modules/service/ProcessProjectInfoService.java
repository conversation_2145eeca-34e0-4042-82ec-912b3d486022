package app.modules.service;

import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.erpdto.bom.ErpProjectItem;

import java.util.List;
import java.util.Map;

public interface ProcessProjectInfoService {
    public List<ProcessProjectInfo> readByCurrentUser(String projectCode, String productCode);

    public Map<ProcessProjectInfo, List<ProcessFlow>> readFlowProjectItems(String projectCode, String productCode, String userId);

    public void insertOrUpdate(ProcessProjectInfo processFlow);

    public String insert(ProcessProjectInfo processFlow);

    public void update(ProcessProjectInfo processFlow);

    void changeState(ErpProjectItem projectInfo, String sendErpStart, String userId);

    public String getProjectStatus(String projectCode, String productCode);

    public List<ProcessProjectInfo> read(String projectCode, String productCode, String userId);

    public void delete(String projectCode, String productCode, String userId);
}
