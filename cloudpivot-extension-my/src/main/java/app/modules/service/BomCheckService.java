package app.modules.service;

import app.modules.model.dto.check.MaterialData;

import java.util.List;
import java.util.Map;

public interface BomCheckService {
    /**
     * CIM_EPM_ITEM_DEFAULT_SP
     * CIM_EPM_ITEM_CHECK_SP
     *
     * @param companyId
     * @param userCode
     * @param defaultFlag
     * @return
     */
    boolean checkItemAttributes(String companyId, String userCode, String defaultFlag);

    boolean checkBom(String companyId, String userCode, String defaultFlag);

    List<Map<String, Object>> getProcInfo(String userCode, String s);

    boolean epmItemPush(String companyId, String userCode, String defaultFlag);

    boolean epmBomPush(String companyId, String userCode, String defaultFlag);

    //MaterialData getMaterialCheckData(String companyId, String userCode, String defaultFlag);
}
