package app.modules.service.impl;

import app.modules.constant.ServiceConstant;
import app.modules.model.dto.check.MaterialData;
import app.modules.service.BomCheckService;
import app.modules.utils.BizServiceFacadeUtil;
import app.modules.utils.MapUtils;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.foundation.orm.api.model.BizObjectQueryObject;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class BomCheckServiceImpl implements BomCheckService {

    @Autowired
    private EngineService engineService;

    @Override
    public boolean checkItemAttributes(String companyId, String userId, String defaultFlag) {
        // 获取状态，判断数据传输是否成功传输到ERP_BOM中
        // 检查数据是否完整

        final String[] executeSpList = new String[]{ServiceConstant.CIM_EPM_ITEM_DEFAULT_SP, ServiceConstant.CIM_EPM_ITEM_CHECK_SP};
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        Map<String, Object> params = new HashMap<>();
        params.put("PI_COMPANY_CODE", companyId);
        params.put("PI_DEFAULT_FLAG", userId);
        params.put("PI_TRANS_USER_CODE", userId);

        for (String s : executeSpList) {

            Map<String, Object> stringObjectMap = BizServiceFacadeUtil.executeSp(bizServiceFacade, ServiceConstant.ERP_EPM_CHECK, s, params);
            boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
            Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
            String msg = (String) stringObjectMap.getOrDefault("msg", false);
            if (!success) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
            }

            List<Map<String, Object>> select = getProcInfo(userId, s.toUpperCase());
            if (select == null || select.isEmpty()) {
                continue;
            }
            Map<String, Object> procReturnMap = select.get(0);
            String returnFlag = (String) procReturnMap.get("RETURN_FLAG");
            if (!Objects.equals(returnFlag, "Y")) {
                String resultMsg = (String) procReturnMap.get("RETURN_MSG");
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("数据出现问题: %s", resultMsg));
            }
        }

        return true;
    }

    @Override
    public boolean checkBom(String companyId, String userCode, String defaultFlag) {
        final String[] permissionList = new String[]{ServiceConstant.CIM_EPM_ITEM_DEFAULT_SP, ServiceConstant.CIM_EPM_ITEM_CHECK_SP};
        permissionCheck(userCode, permissionList);

        final String[] executeSpList = new String[]{ServiceConstant.CIM_EPM_BOM_CHECK_SP};
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        Map<String, Object> params = new HashMap<>();
        params.put("PI_COMPANY_CODE", companyId);
        params.put("PI_DEFAULT_FLAG", defaultFlag);
        params.put("PI_TRANS_USER_CODE", userCode);

        for (String s : executeSpList) {

            Map<String, Object> stringObjectMap = BizServiceFacadeUtil.executeSp(bizServiceFacade, ServiceConstant.ERP_EPM_CHECK, s, params);
            boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
            Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
            String msg = (String) stringObjectMap.getOrDefault("msg", false);
            if (!success) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
            }

            List<Map<String, Object>> select = getProcInfo(userCode, s.toUpperCase());
            if (select == null || select.isEmpty()) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "存储过程没有结果");
            }
            Map<String, Object> procReturnMap = select.get(0);
            String returnFlag = (String) procReturnMap.get("RETURN_FLAG");
            if (Objects.equals(returnFlag, "N")) {
                String resultMsg = (String) procReturnMap.get("RETURN_MSG");
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("数据出现问题: %s", resultMsg));
            }
        }
        return true;
    }

    @Override
    public List<Map<String, Object>> getProcInfo(String userCode, String s) {
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        Map<String, Object> procedureResultParams = new HashMap<>();
        procedureResultParams.put("USER_CODE", userCode);
        procedureResultParams.put("PROC_NAME", s.toUpperCase());

        List<Map<String, Object>> select = BizServiceFacadeUtil.select(bizServiceFacade,
                ServiceConstant.ERP_EPM_CHECK,
                ServiceConstant.CIM_EPM_RETURN_PROCEDURE_QUERY,
                procedureResultParams);

        return select;
    }

    @Override
    public boolean epmItemPush(String companyId, String userCode, String defaultFlag) {
        // checkStatus
        final String[] permissionList = new String[]{ServiceConstant.CIM_EPM_ITEM_DEFAULT_SP, ServiceConstant.CIM_EPM_ITEM_CHECK_SP};
        permissionCheck(userCode, permissionList);

        final String[] executeSpList = new String[]{ServiceConstant.CIM_EPM_ITEM_PUSH_SP};
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        Map<String, Object> params = new HashMap<>();
        params.put("PI_COMPANY_CODE", companyId);
        params.put("PI_DEFAULT_FLAG", defaultFlag);
        params.put("PI_TRANS_USER_CODE", userCode);

        for (String s : executeSpList) {

            Map<String, Object> stringObjectMap = BizServiceFacadeUtil.executeSp(bizServiceFacade, ServiceConstant.ERP_EPM_CHECK, s, params);
            boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
            Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
            String msg = (String) stringObjectMap.getOrDefault("msg", false);
            if (!success) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
            }

            List<Map<String, Object>> select = getProcInfo(userCode, s.toUpperCase());
            if (select == null || select.isEmpty()) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "存储过程没有结果");
            }
            Map<String, Object> procReturnMap = select.get(0);
            String returnFlag = (String) procReturnMap.get("RETURN_FLAG");
            if (Objects.equals(returnFlag, "N")) {
                String resultMsg = (String) procReturnMap.get("RETURN_MSG");
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("数据出现问题: %s", resultMsg));
            }
        }
        return true;
    }

    private void permissionCheck(String userCode, String[] permissionList) {
        for (String s : permissionList) {
            List<Map<String, Object>> select = getProcInfo(userCode, s.toUpperCase());
            if (select == null || select.isEmpty()) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "请在推送数据前，进行数据检查");
            }
        }
    }

    @Override
    public boolean epmBomPush(String companyId, String userCode, String defaultFlag) {
        final String[] permissionList = new String[]{ServiceConstant.CIM_EPM_ITEM_DEFAULT_SP, ServiceConstant.CIM_EPM_ITEM_CHECK_SP, ServiceConstant.CIM_EPM_BOM_CHECK_SP, ServiceConstant.CIM_EPM_ITEM_PUSH_SP};
        permissionCheck(userCode, permissionList);

        final String[] executeSpList = new String[]{ServiceConstant.CIM_EPM_BOM_PUSH_SP};
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        Map<String, Object> params = new HashMap<>();
        params.put("PI_COMPANY_CODE", companyId);
        params.put("PI_DEFAULT_FLAG", defaultFlag);
        params.put("PI_TRANS_USER_CODE", userCode);

        for (String s : executeSpList) {

            Map<String, Object> stringObjectMap = BizServiceFacadeUtil.executeSp(bizServiceFacade, ServiceConstant.ERP_EPM_CHECK, s, params);
            boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
            Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
            String msg = (String) stringObjectMap.getOrDefault("msg", false);
            if (!success) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
            }

            List<Map<String, Object>> select = getProcInfo(userCode, s.toUpperCase());
            if (select == null || select.isEmpty()) {
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "存储过程没有结果");
            }
            Map<String, Object> procReturnMap = select.get(0);
            String returnFlag = (String) procReturnMap.get("RETURN_FLAG");
            if (Objects.equals(returnFlag, "N")) {
                String resultMsg = (String) procReturnMap.get("RETURN_MSG");
                throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", resultMsg));
            }
        }
        return true;
    }
}
