package app.modules.service.impl;

import app.modules.configuration.ZCTConfig;
import app.modules.service.MsgNotifyRestTests;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.authine.cloudpivot.engine.api.model.dto.MessageDTO;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.engine.api.model.runtime.WorkflowInstanceModel;
import com.authine.cloudpivot.engine.domain.organization.RelatedCorpSetting;
import com.authine.cloudpivot.engine.enums.type.IMMessageType;
import com.authine.cloudpivot.engine.service.message.IMessageService;
import com.authine.cloudpivot.engine.service.organization.exception.DataSourceException;
import com.authine.cloudpivot.engine.service.system.RelatedCorpSettingService;
import com.authine.cloudpivot.foundation.util.common.CommonStrUtils;
import com.authine.cloudpivot.web.api.service.EngineService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifyAuthRequestBody;
import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifyAuthResponseBody;
import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifySendRequestBody;
import app.modules.model.vo.ThirdpartyRequest;
import app.modules.model.vo.ThirdpartyRequestHead;
import app.modules.model.vo.ThirdpartyResponse;
import org.springframework.web.client.RestTemplate;
import org.apache.commons.lang3.time.DateFormatUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

@Service
@Slf4j
public class ZCTMessageServiceImpl implements IMessageService {
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private ZCTConfig zctConfig;
    @Autowired
    private EngineService engineService;
    @Autowired
    private RelatedCorpSettingService relatedCorpSettingService;

    ZCTMessageServiceImpl(ZCTConfig zctConfig) {
        restTemplate = new RestTemplateBuilder()
                .rootUri(zctConfig.getBase_url())
                .requestFactory(this::createClientHttpRequestFactory)
                .build();
    }

    public String msgNotifyAuth() throws Exception {
        String token = "";
        Integer sysId = zctConfig.getSysId();
        String sysName = zctConfig.getSysName();
        String secretId = zctConfig.getSecretId();
        String secretKey = zctConfig.getSecretKey();

        ThirdpartyMsgNotifyAuthRequestBody requestBody = new ThirdpartyMsgNotifyAuthRequestBody();
        requestBody.setSysId(String.valueOf(sysId));
        requestBody.setSecretId(secretId);
        requestBody.setSecretKey(secretKey);
        ThirdpartyRequest<ThirdpartyMsgNotifyAuthRequestBody> thirdpartyRequest = new ThirdpartyRequest<>();
        thirdpartyRequest.setHead(new ThirdpartyRequestHead(String.valueOf(System.currentTimeMillis())));
        thirdpartyRequest.setBody(requestBody);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        // 认证接口token传空
        //headers.set("token", "");

        String uri = "/thirdparty/msg/notify/auth";
        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String bodyJson = objectMapper.writeValueAsString(thirdpartyRequest);
        headers.set("signature", calcSignature(secretKey, "POST", uri, "application/json", bodyJson.getBytes(StandardCharsets.UTF_8)));

        ResponseEntity<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(bodyJson.getBytes(StandardCharsets.UTF_8), headers), new ParameterizedTypeReference<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>>() {
        });
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            log.info("中车通auth返回结果：{}", JSON.toJSONString(responseEntity.getBody(), SerializerFeature.PrettyFormat));
            token = responseEntity.getBody().getBody().getToken();
        }
        return token;
    }

    @Override
    public List<String> sendMsg(MessageDTO message) throws DataSourceException {
        Integer sysId = zctConfig.getSysId();
        String sysName = zctConfig.getSysName();
        String secretId = zctConfig.getSecretId();
        String secretKey = zctConfig.getSecretKey();


        String corpId = engineService.getRelatedCorpSettingFacade().getMain().getCorpId();
        RelatedCorpSetting relatedCorpSetting = this.relatedCorpSettingService.getRelatedCorpSettingByCorpId(corpId);
        List<String> receiverStaffNoList =
                message.getReceiverList().stream().map(m -> engineService.getUserFacade().getByUserIdAndCorpId(m,
                        corpId).getUsername()).collect(Collectors.toList());

        long currentTimeMillis = System.currentTimeMillis();
        ThirdpartyMsgNotifySendRequestBody requestBody = new ThirdpartyMsgNotifySendRequestBody();
        // 全局唯一
        String msgId = currentTimeMillis + "" + RandomUtils.nextInt(1000, 10000);
        requestBody.setMsgId(msgId);
        requestBody.setSysId(String.valueOf(sysId));
        requestBody.setSysName(sysName);
        requestBody.setSysIcon("http://zctxc.crrcgc.cc:8888/vtm/2466fc0c84220a57ecb7f0c9d6ad2460/zct_xinchuang/1726713438140050239.png?sign=606bc7941cae4a52190b878f67654e85&timestamp=1729131145532");
        // 接收者类型（1：receivers为员工号，2：receivers为中车通账号）
        requestBody.setReceiverType(1);
        requestBody.setReceivers(receiverStaffNoList);
        requestBody.setBizType("");
        requestBody.setTitle(String.format("标题【%s】", message.getTitle()));
        requestBody.setSubTitle(String.format("子标题【%s】", message.getTitle()));
        requestBody.setContentTitle(String.format("内容标题【%s】", message.getTitle()));
        requestBody.setContent(String.format("内容【%s】", message.getContent()));
        requestBody.setReleaseDate(DateFormatUtils.format(new Date(currentTimeMillis), "yyyy-MM-dd HH:mm:ss"));
        //主办人对应发起人
        String createdBy = message.getCreatedBy();
        UserModel creater = engineService.getOrganizationFacade().getUser(createdBy);
        if (creater != null) {
            requestBody.setSponsor(creater.getName());
        }


        if (IMMessageType.SYSTEM_WORKITEM_URGE == message.getMessageType()) {
            requestBody.setUrgent("true");
        } else {
            requestBody.setUrgent("false");
        }
        String path = "/" + message.getUrl();
        String mobileMessageUrl = CommonStrUtils.handleUrlPath(relatedCorpSetting.getMobileServerUrl()) + path;
        String pcMessageUrl = CommonStrUtils.handleUrlPath(relatedCorpSetting.getPcServerUrl()) + path;
        requestBody.setH5Url(mobileMessageUrl);
        requestBody.setWebUrl(pcMessageUrl);
        requestBody.setExtend("云枢");
        ThirdpartyRequest<ThirdpartyMsgNotifySendRequestBody> thirdpartyRequest = new ThirdpartyRequest<>();
        thirdpartyRequest.setHead(new ThirdpartyRequestHead(String.valueOf(System.currentTimeMillis())));
        thirdpartyRequest.setBody(requestBody);

        // 获取方式参考msgNotifyAuth
        String token = null;
        try {
            token = msgNotifyAuth();
        } catch (Exception ex) {
            log.info("获取中车通token异常：", ex);
        }
        String uri = "/thirdparty/msg/notify/send";
        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String bodyJson = null;
        try {
            bodyJson = objectMapper.writeValueAsString(thirdpartyRequest);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        log.info("传递参数：{}",bodyJson);
        headers.set("signature", calcSignature(secretKey, "POST", uri, "application/json", bodyJson.getBytes(StandardCharsets.UTF_8)));

        ResponseEntity<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(bodyJson.getBytes(StandardCharsets.UTF_8), headers), new ParameterizedTypeReference<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>>() {
        });
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            log.info("中车通send返回结果：{}", JSON.toJSONString(responseEntity.getBody(), SerializerFeature.PrettyFormat));
        }
        return null;
    }

    @Override
    public String initChannel() {
        return "ZCT";
    }

    /**
     * 计算签名
     *
     * @param method
     * @param uri
     * @param contentType
     * @param body
     * @return
     */
    private String calcSignature(String secretKey, String method, String uri, String contentType, byte[] body) {
        String bodySha = SecureUtil.sha256(new ByteArrayInputStream(body));
        HMac hMac = SecureUtil.hmacSha256(secretKey);
        hMac.getEngine().update(method.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(uri.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(contentType.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(bodySha.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(hMac.getEngine().doFinal());
    }

    private ClientHttpRequestFactory createClientHttpRequestFactory() {
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

        OkHttpClient client = new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory, trustManager)
                .build();

        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory(client);
        httpRequestFactory.setReadTimeout(3000);
        httpRequestFactory.setConnectTimeout(3000);

        return httpRequestFactory;
    }
}
