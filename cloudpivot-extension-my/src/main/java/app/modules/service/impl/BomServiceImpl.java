package app.modules.service.impl;

import app.modules.constant.SchemaCodeConstant;
import app.modules.constant.ServiceConstant;
import app.modules.constant.UserConstant;
import app.modules.model.dto.processflow.ProcessFlowSyncItem;
import app.modules.model.dto.processflow.ProcessFlowSyncRequest;
import app.modules.model.entity.Bom;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.entity.baseEntity.EpmBusinessEntity;
import app.modules.model.erpdto.bom.ErpBomItem;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.BaseCodeService;
import app.modules.service.BomService;
import app.modules.service.ProcessProjectInfoService;
import app.modules.service.ProcessFlowService;
import app.modules.utils.BizServiceFacadeUtil;
import app.modules.utils.DataOpUtil;
import app.modules.utils.TreeBuilder;
import cn.hutool.core.bean.BeanUtil;
import com.authine.cloudpivot.engine.api.facade.BizObjectFacade;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.api.model.bizservice.BizDatabaseConnectionPoolModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceMethodModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel;
import com.authine.cloudpivot.engine.component.query.api.FilterExpression;
import com.authine.cloudpivot.engine.component.query.api.Page;
import com.authine.cloudpivot.engine.component.query.api.helper.Q;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.domain.organization.User;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.awt.print.PrinterException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BomServiceImpl implements BomService {

    @Autowired
    private EngineService engineService;

    @Resource
    @Lazy
    private BaseCodeService baseCodeServiceServiceImpl;

    @Resource
    @Lazy
    private ProcessProjectInfoService ProcessProjectInfoServiceImpl;

    @Resource
    @Lazy
    private ProcessFlowService processFlowServiceImpl;

    @Resource
    private ProjectItemCurServiceImpl projectItemCurServiceImpl;

    @Autowired
    private RedissonClient redissonClient;

    @Transactional
    @Override
    public List<ProcessProjectInfo> processFlowToBoms(Map<ProcessProjectInfo, List<ProcessFlow>> processFlows) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        List<ProcessProjectInfo> result = new ArrayList<>();

        for (Map.Entry<ProcessProjectInfo, List<ProcessFlow>> entry : processFlows.entrySet()) {
            ProcessProjectInfo key = entry.getKey();
            List<ProcessFlow> value = entry.getValue();

            String projectCode = key.getProjectCode();
            String productCode = key.getProductCode();
            String productName = key.getProductName();

            // value 过滤 删除的
            value = value.stream().peek(o -> {
                String virture = "Y".equalsIgnoreCase(o.getVirture()) ? "Y" : "N";
                o.setVirture(virture);
            }).filter(TreeBuilder::shouldSave).collect(Collectors.toList());

            List<Map<String, Object>> collect = value.stream().map(ProcessFlow::toMap).collect(Collectors.toList());

            List<Bom> bomList = new LinkedList<>();

            for (Map<String, Object> stringObjectMap : collect) {
                Bom tempBom = new Bom();

                tempBom.setProjectWorkCode(key.getProjWorkCode());
                tempBom.setProjectuid(projectCode);
                tempBom.setProductCode(productCode);
                tempBom.setProductName(productName);
                tempBom.setBomSeqNo((String) stringObjectMap.get("MY_INDEX"));
                tempBom.setDrawNo((String) stringObjectMap.get("PIC_NO"));
                tempBom.setSingleQty((String) stringObjectMap.get("AMOUNT"));
                tempBom.setSingleWeight((String) stringObjectMap.get("ITEM_WEIGHT"));
                tempBom.setSumWeight((String) stringObjectMap.get("TOTAL_WEIGHT"));
                tempBom.setItemModel((String) stringObjectMap.get("MATERIAL"));
                tempBom.setPlanRemark((String) stringObjectMap.get("REMARK"));
                tempBom.setPlanNote((String) stringObjectMap.get("COMMENT"));
                // 设置子项信息
                tempBom.setItemCode((String) stringObjectMap.get("ITEM_NO"));
                tempBom.setItemName((String) stringObjectMap.get("ITEM_NAME"));
                tempBom.setPartId((String) stringObjectMap.get("PART_ID"));
                // 设置父项信息
                tempBom.setParentItemCode((String) stringObjectMap.get("PARENT_PART_NUMBER"));
                tempBom.setParentItemName((String) stringObjectMap.get("PARENT_NAME"));
                tempBom.setParentId((String) stringObjectMap.get("PARENT_ID"));
                // 设置工艺行程
                tempBom.setWorkRoute((String) stringObjectMap.get("WORK_ROUTE"));
                tempBom.setWgNo((String) stringObjectMap.get("WG_NO"));
                tempBom.setCoopNo((String) stringObjectMap.get("COOP_NO"));
                tempBom.setTcNo((String) stringObjectMap.get("TC_NO"));
                tempBom.setGjNo((String) stringObjectMap.get("GJ_NO"));
                tempBom.setCyjitNo((String) stringObjectMap.get("CYJIT_NO"));
                tempBom.setZzNo((String) stringObjectMap.get("ZZ_NO"));
                tempBom.setCymrpNo((String) stringObjectMap.get("CYMRP_NO"));
                tempBom.setBlNo((String) stringObjectMap.get("BL_NO"));
                // 设置标志
                tempBom.setPhantomFlag((String) stringObjectMap.get("VIRTURE"));
                tempBom.setMpFlag((String) stringObjectMap.get("MP_FLAG"));
                tempBom.setCoopFlag((String) stringObjectMap.get("COOP_FLAG"));
                tempBom.setTmpItemFlag((String) stringObjectMap.get("VIRTURE"));
                // 设置责任单位
                tempBom.setDeptCode((String) stringObjectMap.get("DEPT_CODE"));
                tempBom.setDeptName((String) stringObjectMap.get("DEPT_NAME"));

                tempBom.setCompanyCode((String) stringObjectMap.get("COMPANY_CODE"));
                tempBom.setTreeDisplayField(tempBom.getItemCode() + " " + tempBom.getItemName());
                bomList.add(tempBom);
            }

            // 进行去重 + 属性合并
            Map<String, Bom> uniqueBoms = bomList.stream()
                    .collect(Collectors.toMap(
                            bom -> bom.getParentItemCode() + "|" + bom.getItemCode(),
                            Bom::new, // 复制一个 Bom 对象（防止引用问题）
                            (existing, replacement) -> {
                                // 如果遇到重复 key，合并 remark 字段
                                existing.setBomSeqNo((existing.getBomSeqNo() == null ? "" : existing.getBomSeqNo() + ", ") + replacement.getBomSeqNo());
                                return existing;
                            }
                    ));

            List<Bom> deduped = new ArrayList<>(uniqueBoms.values());

            Map<ProcessProjectInfo, List<ProcessFlow>> erpBomMap = new HashMap<>();

            // 合并所有 Bom 并转换为 BizObjectModel
            bomList = deduped.stream()    // 遍历 Map 中的每个 List<Bom>
                    .filter(Objects::nonNull).collect(Collectors.toList());         // 过滤空对象

            List<BizObjectModel> bizObjectModels = bomList.stream()
                    .map(Bom::toMap)                      // 调用 toMap 方法
                    .map(map -> new BizObjectModel(
                            SchemaCodeConstant.BOM,
                            map,
                            false
                    ))                                                     // 构建 BizObjectModel
                    .collect(Collectors.toList());                         // 收集结果

            // 构建 bom 的父子关系
            Map<String, String> parentIdMap = new HashMap<>();
            for (BizObjectModel bizObjectModel : bizObjectModels) {
                String genId = DataOpUtil.genId();
                Map<String, Object> data = bizObjectModel.getData();
                String itemCode = (String) data.get("ITEM_CODE");
                bizObjectModel.setId(genId);
                parentIdMap.put(itemCode, genId);
            }

            for (BizObjectModel bizObjectModel : bizObjectModels) {
                Map<String, Object> data = bizObjectModel.getData();
                String parentItemCode = (String) data.get("PARENT_ITEM_CODE");
                String parentId = parentIdMap.get(parentItemCode);
                bizObjectModel.put("PARENT_BOM_ID", parentId);
            }

            deleteBom(projectCode, productCode, userId);
            bizObjectFacade.batchSaveBizObjectModel(userId, bizObjectModels, null);
            result.add(key);
        }

        return result;
    }

    public List<ErpBomItem> epmBom2ErpBom(List<Bom> bomList) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }

        // 获取当前日期
        Date currentDate = new Date();

        // 定义格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");

        // 格式化
        String formattedDate = sdf.format(currentDate);

        List<ErpBomItem> result = new ArrayList<>();
        for (Bom bom : bomList) {
            ErpBomItem item = new ErpBomItem();
            BeanUtil.copyProperties(bom, item);

            item.setCompanyCode(bom.getCompanyCode());

            item.setParentCode(bom.getParentItemCode());
            item.setParentName(bom.getParentItemName());
            item.setChildCode(bom.getItemCode());
            item.setChildName(bom.getItemName());
            item.setChildModel(bom.getItemModel());
            item.setChildNorm(bom.getItemNorm());

            item.setChildQty(bom.getSingleQty());
            item.setNetWeight(bom.getSingleWeight());
            item.setStartUseDate(formattedDate);
            item.setEndUseDate("9999/12/31");
            item.setNote1(bom.getPlanRemark());
            item.setNote2(bom.getPlanNote());
            item.setPlanStatus("I");
            item.setDrawingNo(bom.getDrawNo());

            item.setPhantomFlag(bom.getTmpItemFlag());
            item.setWorkroute(bom.getWorkRoute());
            item.setDeptCode(bom.getDeptCode());
            item.setDeptName(bom.getDeptName());
            item.setTransUserCode(userById.getUserId().substring(userById.getUserId().length() - 6));
            item.setTransUserName(userById.getName());

            item.setCompanyCode(bom.getCompanyCode());

            result.add(item);
        }
        return result;
    }

    @Override
    public void epmBomSendErpBom(String serviceCode,
                                 String methodCode,
                                 List<Map<String, Object>> listInputParams) {

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel method = bizServiceFacade.getBizServiceMethodByCode(serviceCode, methodCode);
        if (method == null) {
            throw new RuntimeException("业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + methodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }


        // 3. 执行方法并返回结果
        BizServiceFacadeUtil.batchInsert(
                bizServiceFacade,
                serviceCode,
                methodCode,
                listInputParams
        );
    }

    public List<Bom> read(String projectCode, String productCode, String userId) {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        // 根据projectCode和productCode以及 userId(creater) 是否为空构建 查询条件

        BizObjectQueryModel bizObjectQueryObject = getBizObjectQueryObject(projectCode, productCode, userId, null, null);
        Page<BizObjectModel> bizObjectModelPage = bizObjectFacade.queryBizObjects(bizObjectQueryObject);
        List<Bom> result = new ArrayList<>();
        if (bizObjectModelPage != null && CollectionUtils.isNotEmpty(bizObjectModelPage.getContent())) {
            List<? extends BizObjectModel> content = bizObjectModelPage.getContent();
            for (BizObjectModel bizObjectModel : content) {
                // 根据请求的数据类型进行判断，如果是新产业判断是否存在会出现不同项目号，如果不是，不同产品
                Map<String, Object> data = bizObjectModel.getData();
                Bom processFlow = EpmBusinessEntity.fromMapStatic(Bom.class, data);
                result.add(processFlow);
            }
        }
        return result;

    }

    public BizObjectQueryModel getBizObjectQueryObject(String projectCode, String productCode, String creater, List<String> groupByFields, List<String> orderByFields) {

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject = new BizObjectQueryModel();
        bizObjectQueryObject.setSchemaCode(SchemaCodeConstant.BOM);
        bizObjectQueryObject.setQueryCode(SchemaCodeConstant.BOM);
        FilterExpression.Item projectCodeFilter = Q.it("PROJECTUID", FilterExpression.Op.Eq, projectCode);
        FilterExpression.Item productCodeFilter = Q.it("PRODUCT_CODE", FilterExpression.Op.Eq, productCode);
        FilterExpression.Item createrCodeFilter = Q.it("creater", FilterExpression.Op.Eq, creater);
        if (projectCode == null && productCode == null && creater != null) {
            bizObjectQueryObject.setFilterExpr(createrCodeFilter);
        } else if (projectCode == null && productCode == null && creater == null) {

        } else if (projectCode == null && productCode != null && creater != null) {
            FilterExpression.And finalFilter = Q.and(productCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode == null && productCode != null && creater == null) {
            bizObjectQueryObject.setFilterExpr(productCodeFilter);
        } else if (projectCode != null && productCode != null && creater == null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode != null && productCode == null && creater == null) {
            bizObjectQueryObject.setFilterExpr(projectCodeFilter);
        } else if (projectCode != null && productCode != null && creater != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode != null && productCode == null && creater != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        }

        if (groupByFields != null && !groupByFields.isEmpty()) {
            bizObjectQueryObject.setGroupByFields(groupByFields);
        }

        if (orderByFields != null) {
            bizObjectQueryObject.setOrderByFields(orderByFields);
        } else {
            List<String> defaultOrderByFields = new ArrayList<>();
            defaultOrderByFields.add("ITEM_CODE");
            bizObjectQueryObject.setOrderByFields(defaultOrderByFields);
        }

        BizObjectQueryModel.Options queryOptions = null;
        if (groupByFields != null && !groupByFields.isEmpty()) {
            queryOptions = DataOpUtil.getQueryOptionsByList(groupByFields);
        } else {
            queryOptions = DataOpUtil.getAllQueryOptions(new Bom());
        }

        bizObjectQueryObject.setOptions(queryOptions);
        return bizObjectQueryObject;
    }

    /**
     * 因为该方法涉及到 使用业务集成的方法往ORACLE数据库中写入数据，所以涉及到事务的使用，因此该方法需要考虑未传送完整的情况
     *
     * @param processFlowSyncRequest
     */
    @Override
    public void epmBomSendErpByProject(ProcessFlowSyncRequest processFlowSyncRequest) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        RLock lock = redissonClient.getLock("epmBomSendErp:" + userId);
        lock.lock();

        // 检查 request是否为空
        if (processFlowSyncRequest == null || processFlowSyncRequest.getItems() == null || processFlowSyncRequest.getItems().length < 1) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "请求参数错误");
        }

        ProcessFlowSyncItem[] items = processFlowSyncRequest.getItems();

        // 从 erp 读取 项目信息，判断参数是否正确
        List<ErpProjectItem> read = projectItemCurServiceImpl.read(new HashMap<>());
        List<ErpProjectItem> projectInfos = new ArrayList<>();

        for (ProcessFlowSyncItem processFlowSyncItem : items) {

            boolean hasExists = false;
            for (ErpProjectItem item : read) {
                if (processFlowSyncItem.getProjectCode().equals(item.getProjectCode()) && processFlowSyncItem.getProductCode().equals(item.getProductCode())) {
                    projectInfos.add(item);
                    hasExists = true;
                    break;
                }
            }

            if (!hasExists) {
                String errMsg = String.format("请求参数错误, 项目不存在, projectCode: %s, productCode: %s", processFlowSyncItem.getProjectCode(), processFlowSyncItem.getProductCode());
                throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), errMsg);
            }
        }

        long count = projectInfos.stream().map(ErpProjectItem::getProjectCode).distinct().count();
        if (count > 1) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "数据出现问题，此时同时存在多个项目");
        }

        ErpProjectItem erpProjectItem = projectInfos.get(0);
        deleteErpBom(erpProjectItem);

        // 先清除日志，再写入
        deleteCimEpmReturnProcedure(userId);

        // 根据 projectInfos 读取 bom 数据
        for (ErpProjectItem projectInfo : projectInfos) {
            ProcessProjectInfoServiceImpl.changeState(projectInfo, "sendErpStart", userId);
            List<Bom> boms = read(projectInfo.getProjectCode(), projectInfo.getProductCode(), null);
            List<ErpBomItem> erpBomItems = epmBom2ErpBom(boms);
            List<Map<String, Object>> collect = erpBomItems.stream().map(ErpBomItem::toMap).collect(Collectors.toList());
            epmBomSendErpBom(ServiceConstant.EPM_BOM, ServiceConstant.EPM_BOM_INSERT, collect);
            ProcessProjectInfoServiceImpl.changeState(projectInfo, "sendErpEnd", userId);
        }
    }

    private void deleteCimEpmReturnProcedure(String userId) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        User userById = openEngine.getUserById(userId);
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId();
        Map<String, Object> deleteInputParam = new HashMap<>();
        deleteInputParam.put("USER_CODE", userCode);
        bizServiceFacade.testBizServiceMethod(ServiceConstant.ERP_EPM_CHECK, ServiceConstant.CIM_EPM_RETURN_PROCEDURE_CLEAN, deleteInputParam);
    }


    @Override
    public void deleteAllById() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        deleteBom(null, null, userId);
    }

    void deleteBom(String projectCode, String productCode, String creater) {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject = getBizObjectQueryObject(projectCode, productCode, creater, null, null);
        Page<BizObjectModel> bomModelPage = bizObjectFacade.queryBizObjects(bizObjectQueryObject);

        List<String> bomObjectIds = new ArrayList<>();
        if (bomModelPage != null && CollectionUtils.isNotEmpty(bomModelPage.getContent())) {
            List<? extends BizObjectModel> content = bomModelPage.getContent();
            for (BizObjectModel bizObjectModel : content) {
                bomObjectIds.add(bizObjectModel.getId());
            }
        }
        for (String processObjectId : bomObjectIds) {
            bizObjectFacade.deleteBizObject(SchemaCodeConstant.BOM, processObjectId);
        }
    }

    @Override
    public void deleteErpBom(ErpProjectItem item) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        User userById = openEngine.getUserById(userId);
        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);

        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(ServiceConstant.EPM_BOM);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + ServiceConstant.EPM_BOM);
        }

        BizServiceMethodModel method = bizServiceFacade.getBizServiceMethodByCode(ServiceConstant.EPM_BOM, ServiceConstant.CIM_EPM_EDM_BOM_DELETE_1);
        if (method == null) {
            throw new RuntimeException("业务方法不存在: serviceCode=" + ServiceConstant.EPM_BOM + ", methodCode=" + ServiceConstant.CIM_EPM_EDM_BOM_DELETE_1);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(ServiceConstant.EPM_BOM);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + ServiceConstant.EPM_BOM);
        }

        Map<String, Object> deleteInputParams = new HashMap<>();
        deleteInputParams.put("PROJECTUID", item.getProjectCode());
        deleteInputParams.put("TRANS_USER_CODE", userCode);

        // 3. 执行方法并返回结果
        bizServiceFacade.testBizServiceMethod(
                ServiceConstant.EPM_BOM,
                ServiceConstant.CIM_EPM_EDM_BOM_DELETE_1,
                deleteInputParams
        );
    }

}
