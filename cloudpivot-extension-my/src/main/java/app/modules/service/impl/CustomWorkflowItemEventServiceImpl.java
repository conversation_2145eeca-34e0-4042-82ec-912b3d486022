package app.modules.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.authine.cloudpivot.engine.api.model.runtime.CirculateItemModel;
import com.authine.cloudpivot.engine.api.model.runtime.WorkItemModel;
import com.authine.cloudpivot.engine.enums.status.WorkItemStatus;
import com.authine.cloudpivot.engine.service.runtime.BizWorkflowTokenService;
import com.authine.cloudpivot.engine.workflow.spi.WorkflowItemEventService;
import com.authine.cloudpivot.engine.workflow.spi.event.CirculateDeletedItemEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.CirculateItemCreatedEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.CirculateItemFinishedEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.WorkItemCreatedEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.WorkItemDeletedEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.WorkItemFinishedEvent;
import com.authine.cloudpivot.engine.workflow.spi.event.WorkflowItemEvent;

/**
 * 流程事件监听处理
 *
 * <AUTHOR> Create in 2025/07/11
 */
@Service
public class CustomWorkflowItemEventServiceImpl implements WorkflowItemEventService {
    private static final Logger LOG = LoggerFactory.getLogger(CustomWorkflowItemEventServiceImpl.class);
    @Resource
    private BizWorkflowTokenService bizWorkflowTokenService;

    @Override
    public void onEvent(WorkflowItemEvent event) {
        if (event instanceof WorkItemCreatedEvent) {
            /* 创建待办事件，系统保存待办任务记录后抛出 */
            WorkItemModel data = ((WorkItemCreatedEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((WorkItemCreatedEvent) event).getData().getWorkflowTokenId().equals("0")) {
                createToDo(data);
                LOG.info("===>>创建待办事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
            }
        } else if (event instanceof WorkItemFinishedEvent) {
            /* 完成待办事件，系统更新待办任务记录后抛出 */
            WorkItemModel data = ((WorkItemFinishedEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((WorkItemFinishedEvent) event).getData().getWorkflowTokenId().equals("0")) {
                completedToDo(data);
                LOG.info("===>>完成待办事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
            }
        } else if (event instanceof WorkItemDeletedEvent) {
            /* 删除待办事件，系统删除待办任务记录后抛出 */
            WorkItemModel data = ((WorkItemDeletedEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((WorkItemDeletedEvent) event).getData().getWorkflowTokenId().equals("0")) {
                if (data.getState().equals(WorkItemStatus.FINISHED)) {
                    deleteFinishedToDo(data);
                    LOG.info("===>>删除已办事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
                } else {
                    deleteToDo(data);
                    LOG.info("===>>删除待办事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
                }
            }
        } else if (event instanceof CirculateItemCreatedEvent) {
            /* 创建传阅事件，系统保存传阅任务记录后抛出 */
            CirculateItemModel data = ((CirculateItemCreatedEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((CirculateItemCreatedEvent) event).getData().getWorkflowTokenId().equals("0")) {
                createCirculate(data);
                LOG.info("===>>创建传阅事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
            }
        } else if (event instanceof CirculateItemFinishedEvent) {
            /* 完成传阅事件，系统更新传阅任务记录后抛出 */
            CirculateItemModel data = ((CirculateItemFinishedEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((CirculateItemFinishedEvent) event).getData().getWorkflowTokenId().equals("0")) {
                completedCirculate(data);
                LOG.info("===>>完成传阅事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
            }
        } else if (event instanceof CirculateDeletedItemEvent) {
            /* 删除传阅事件，系统删除传阅任务记录后抛出 */
            CirculateItemModel data = ((CirculateDeletedItemEvent) event).getData();
            /* 获取步骤 跳过第一步 */
            if (!((CirculateDeletedItemEvent) event).getData().getWorkflowTokenId().equals("0")) {
                if (data.getState().equals(WorkItemStatus.FINISHED)) {
                    deleteFinishedCirculate(data);
                    LOG.info("===>>删除已阅事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
                } else {
                    deleteCirculate(data);
                    LOG.info("===>>删除待阅事件 流程实例：{} 节点名称：{} 流程状态：{}", data.getInstanceName(), data.getActivityName(), data.getState().getName());
                }
            }
        }
    }

    /**
     * 创建待办
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link WorkItemModel}
     */
    private void createToDo(WorkItemModel item) {
    }

    /**
     * 完成待办
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link WorkItemModel}
     */
    private void completedToDo(WorkItemModel item) {
    }

    /**
     * 删除已办
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link WorkItemModel}
     */
    private void deleteFinishedToDo(WorkItemModel item) {
    }

    /**
     * 删除待办
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link WorkItemModel}
     */
    private void deleteToDo(WorkItemModel item) {
    }

    /**
     * 创建待阅
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link CirculateItemModel}
     */
    private void createCirculate(CirculateItemModel item) {
    }

    /**
     * 完成待阅
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link CirculateItemModel}
     */
    private void completedCirculate(CirculateItemModel item) {
    }

    /**
     * 删除已阅
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link CirculateItemModel}
     */
    private void deleteFinishedCirculate(CirculateItemModel item) {
    }

    /**
     * 删除待阅
     *
     * <AUTHOR> Create in 2025/06/26
     * @param item {@link CirculateItemModel}
     */
    private void deleteCirculate(CirculateItemModel item) {
    }
}