package app.modules.service.impl;

import app.modules.constant.SchemaCodeConstant;
import app.modules.constant.UserConstant;
import app.modules.model.dto.processflow.*;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.entity.baseEntity.EpmBusinessEntity;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.*;
import app.modules.utils.DataOpUtil;
import app.modules.utils.TreeBuilder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.authine.cloudpivot.engine.api.facade.BizObjectFacade;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel;
import com.authine.cloudpivot.engine.component.query.api.*;
import com.authine.cloudpivot.engine.component.query.api.helper.Q;
import com.authine.cloudpivot.engine.enums.ErrCode;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.foundation.orm.api.model.BizObjectQueryObject;
import com.authine.cloudpivot.foundation.orm.api.model.QueryRelationPermission;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessFlowServiceImpl implements ProcessFlowService {

    @Autowired
    private EngineService engineService;

    @Resource
    private ProcessProjectInfoService processProjectInfoServiceImpl;

    @Resource
    private ProjectItemCurService projectItemCurServiceImpl;

    @Resource
    private BaseCodeService baseCodeServiceImpl;

    @Resource
    private BomService bomServiceImpl;

    @Autowired
    private RedissonClient redissonClient;

    private static final String url = "http://10.41.245.74:8083/rest/v1/queryRoute";

    private void processFlowItemSync(ErpProjectItem item) {
        // 获取当前用户
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        List<ProcessProjectInfo> read = processProjectInfoServiceImpl.readByCurrentUser(item.getProjectCode(), item.getProductCode());

        ProcessProjectInfo processProjectInfo = item.toProcessProjectInfo();
        processProjectInfo.setId(null);
        String id = "";
        if (read != null && !read.isEmpty()) {
            id = read.get(0).getId();
            processProjectInfo.setId(id);
            processProjectInfoServiceImpl.update(processProjectInfo);
        } else {
            id = DataOpUtil.genId();
            processProjectInfo.setId(id);
            processProjectInfoServiceImpl.insert(processProjectInfo);
        }

        // insert or update
        processProjectInfoServiceImpl.insertOrUpdate(processProjectInfo);

        // 数据同步操作
        // 1. 获取工艺平台数据
        // 2. 写入数据
        JSONObject params = new JSONObject();
        params.putOnce("partNumber", item.getProductCode().trim());
        // 1. 获取数据
        HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").body(params.toString()).execute();
        if (response.isOk()) {

            String responseBody = response.body();

            ProcessFlowResContainer processFlowResContainer = JSONUtil.toBean(responseBody, ProcessFlowResContainer.class);
            if (processFlowResContainer == null) {
                throw new PortalException(50001, "获取数据为空");
            }
            // 将 processFlowRes 转换成 ProcessFlow
            List<ProcessFlow> processFlowRes = processFlowResContainer.getData().stream()
                    .map(o -> {
                        ProcessFlow processFlow = new ProcessFlow();
                        BeanUtil.copyProperties(o, processFlow);
                        processFlow.setMyIndex(o.getIndex());
                        processFlow.setGenFlag(false);
                        return processFlow;
                    })
                    .collect(Collectors.toList());

            for (ProcessFlow processFlowRe : processFlowRes) {
                processFlowRe.setProjectCode(item.getProjectCode());
                processFlowRe.setProductCode(item.getProductCode());
                processFlowRe.setOriginWorkRoute(processFlowRe.getWorkRoute());
            }

            partInsertOrUpdate(id, processProjectInfo, processFlowRes);
        } else {
            int status = response.getStatus();
            throw new PortalException(status * 100L, "网络请求错误，无法获取工艺平台数据");
        }
    }

    @Transactional
    @Override
    public void processFlowSync(List<ProcessFlowSyncItem> processProjectItems) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        RLock lock = redissonClient.getLock("processFlowSync:" + userId);
        lock.lock();
        try {
            if (processProjectItems.isEmpty()) {
                throw new PortalException(ErrCode.REQUEST_NULL, "当前请求不合法");
            }


            // 获取 	在产项目对应的产品列表
            List<ErpProjectItem> read = projectItemCurServiceImpl.read(new HashMap<>());
            log.error("processFlowSync read: {}", ArrayUtils.toString(read));

            List<ErpProjectItem> projectInfos = read.stream()
                    .map(ErpProjectItem::trim)
                    .filter(erpItem -> processProjectItems.stream().anyMatch(syncItem ->
                            syncItem.getProjectCode().trim().equals(erpItem.getProjectCode().trim()) &&
                                    syncItem.getProductCode().trim().equals(erpItem.getProductCode().trim())
                    ))
                    .collect(Collectors.toList());

            boolean newIndustryItem = isNewIndustryItem(projectInfos);
            if (!newIndustryItem) {
                throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "当前不支持非新产业项目");
            }


            // 判断是否是新产业项目，是否有多个产品的可能性，如果当前项目是新产业项目
            List<ErpProjectItem> shouldDeleteProjectItems = getShouldDeleteProjects(projectInfos, newIndustryItem);
            log.error("processFlowSync shouldDeleteProjectItems: {}", ArrayUtils.toString(shouldDeleteProjectItems));
            // 重新同步数据
        /*for (ErpProjectItem item : projectInfos) {
            processFlowItemSync(item);
            shouldDeleteProjectItems.removeIf(o -> Objects.equals(o.getProjectCode(), item.getProjectCode()) && Objects.equals(o.getProductCode(), item.getProductCode()));
        }*/

            // 重新同步数据
            Iterator<ErpProjectItem> iterator = shouldDeleteProjectItems.iterator();
            while (iterator.hasNext()) {
                ErpProjectItem item = iterator.next();
                processFlowItemSync(item);
                iterator.remove();
            }

            for (ErpProjectItem erpProjectItem : shouldDeleteProjectItems) {
                log.error("processFlowSync start sync: {}", ObjectUtils.toString(erpProjectItem));
                processProjectInfoServiceImpl.delete(erpProjectItem.getProjectCode(), erpProjectItem.getProductCode(), userId);
                deleteProcessFlow(erpProjectItem.getProjectCode(), erpProjectItem.getProductCode(), userId);
                log.error("processFlowSync end sync: {}", ObjectUtils.toString(erpProjectItem));
            }
        } finally {
            lock.unlock();
        }
    }

    private List<ErpProjectItem> getShouldDeleteProjects(List<ErpProjectItem> erpProjectItems, boolean newIndustryItem) {
        log.error("getShouldDeleteProjects: {}", ArrayUtils.toString(erpProjectItems));
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        List<ErpProjectItem> result = new ArrayList<>();

        ErpProjectItem erpProjectItemFirst = erpProjectItems.get(0);
        String projectCode = erpProjectItemFirst.getProjectCode();
        List<ProcessProjectInfo> read = processProjectInfoServiceImpl.read(projectCode, null, userId);
        long count = read.stream().map(ProcessProjectInfo::getProjectCode).distinct().count();
        if (count > 1) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), "数据出现问题，此时同时存在多个项目");
        }
        if (read.isEmpty()) {
            result.addAll(erpProjectItems);
            return result;
        }

        // 1. 如果是新产业项目，则判断 projectCode 和 数据中的是否一致，不一致，全部删除，一致就删除当前的项目
        if (newIndustryItem) {
            // 经过了验证，说明，当前的projectCode肯定是唯一的
            ProcessProjectInfo processProjectInfo = read.get(0);
            String projectCode1 = processProjectInfo.getProjectCode();

            if (Objects.equals(projectCode1, projectCode)) {
                //ErpProjectItem erpProjectItem = getErpProjectItem(processProjectItem);
                result.addAll(erpProjectItems);
            } else {
                // 不相等，就删除当前数据库中所有项目
                for (ProcessProjectInfo info : read) {
                    ErpProjectItem erpProjectItem = info.toErpProjectItem();
                    result.add(erpProjectItem);
                }
            }
            return result;
        } else {
            // 对应非新产业项目，直接清空
            for (ProcessProjectInfo info : read) {
                ErpProjectItem erpProjectItem = info.toErpProjectItem();
                result.add(erpProjectItem);
            }
            return result;
        }
    }

    @NotNull
    private static ErpProjectItem getErpProjectItem(ProcessFlowSyncItem processProjectItem) {
        ErpProjectItem erpProjectItem = new ErpProjectItem();

        erpProjectItem.setProjectCode(processProjectItem.getProjectCode());
        erpProjectItem.setProjectName(processProjectItem.getProjectName());
        erpProjectItem.setProductCode(processProjectItem.getProductCode());
        erpProjectItem.setProductName(processProjectItem.getProductName());
        erpProjectItem.setProjWorkCode(processProjectItem.getProjWorkCode());
        erpProjectItem.setProjEpmWorkCode(processProjectItem.getProjEpmWorkCode());
        erpProjectItem.setNewIndustryFlag(processProjectItem.getNewIndustryFlag());
        erpProjectItem.setProjYear(processProjectItem.getProjYear());
        return erpProjectItem;
    }

    @Override
    public List<ErpProjectItem> readCreaterProjectInfo() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        List<String> groupByList = new ArrayList<>();
        groupByList.add("PROJECT_CODE");
        groupByList.add("PRODUCT_CODE");
        groupByList.add("creater");

        List<String> orderByFields = new ArrayList<>();
        orderByFields.add("PROJECT_CODE");
        orderByFields.add("PRODUCT_CODE");
        orderByFields.add("creater");

        BizObjectQueryModel bizObjectQueryObject = getBizObjectQueryObject(null, null, userId, groupByList, orderByFields);
        Page<Map<String, Object>> groupQueryPage = bizObjectFacade.queryBizObjects(this.getBizObjectQueryObject(bizObjectQueryObject));
        List<? extends Map<String, Object>> content = groupQueryPage.getContent();


        List<ErpProjectItem> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(content)) {
            for (Map<String, Object> stringObjectMap : content) {
                ErpProjectItem erpProjectItem = new ErpProjectItem();
                erpProjectItem.setProjectCode((String) stringObjectMap.get("PROJECT_CODE"));
                erpProjectItem.setProjectName((String) stringObjectMap.get("PROJECT_NAME"));
                erpProjectItem.setProductCode((String) stringObjectMap.get("PRODUCT_CODE"));
                erpProjectItem.setProductName((String) stringObjectMap.get("PRODUCT_NAME"));

                result.add(erpProjectItem);
            }
        }
        return result;

    }

    public BizObjectQueryObject getBizObjectQueryObject(BizObjectQueryModel bizObjectQueryObject) {
        return new BizObjectQueryObject() {
            public String getSchemaCode() {
                return bizObjectQueryObject.getSchemaCode();
            }

            public FilterExpression getFilterExpression() {
                return bizObjectQueryObject.getFilterExpr();
            }

            public Pageable getPageable() {
                return bizObjectQueryObject.getPageable();
            }

            public List<String> getGroupByFields() {
                return bizObjectQueryObject.getOrderByFields();
            }

            public Map<String, Object> getQuotes() {
                return bizObjectQueryObject.getQuotes();
            }

            public boolean queryParticipants() {
                return bizObjectQueryObject.getQueryParticipant();
            }

            public boolean queryUrgencyLevel() {
                return bizObjectQueryObject.getQueryUrgencyLevel();
            }

            public boolean orderUrgencyLevel() {
                return bizObjectQueryObject.getOrderUrgencyLevel();
            }

            public boolean queryInstanceStatus() {
                return bizObjectQueryObject.getQueryInstanceStatus();
            }

            public boolean queryActivityName() {
                return bizObjectQueryObject.getQueryActivityName();
            }

            public boolean childCondition() {
                return bizObjectQueryObject.getChildCondition();
            }

            public List<String> queryRelevanceDataFields() {
                return bizObjectQueryObject.getQueryRelevanceDataFields();
            }

            public boolean showTotal() {
                return bizObjectQueryObject.getShowTotal() == null || bizObjectQueryObject.getShowTotal();
            }

            public List<QueryRelationPermission> queryRelationPermissions() {
                return bizObjectQueryObject.getRelationPermissions();
            }
        };
    }

    public BizObjectQueryModel getBizObjectQueryObject(String projectCode, String productCode, String creater, List<String> groupByFields, List<String> orderByFields) {

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject = new BizObjectQueryModel();
        bizObjectQueryObject.setSchemaCode(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP);
        bizObjectQueryObject.setQueryCode(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP);
        FilterExpression.Item projectCodeFilter = Q.it("PROJECT_CODE", FilterExpression.Op.Eq, projectCode);
        FilterExpression.Item productCodeFilter = Q.it("PRODUCT_CODE", FilterExpression.Op.Eq, productCode);
        FilterExpression.Item createrCodeFilter = Q.it("creater", FilterExpression.Op.Eq, creater);
        if (projectCode == null && productCode == null && creater != null) {
            bizObjectQueryObject.setFilterExpr(createrCodeFilter);
        } else if (projectCode == null && productCode == null && creater == null) {

        } else if (projectCode == null && productCode != null && creater != null) {
            FilterExpression.And finalFilter = Q.and(productCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode == null && productCode != null && creater == null) {
            bizObjectQueryObject.setFilterExpr(productCodeFilter);
        } else if (projectCode != null && productCode != null && creater == null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode != null && productCode == null && creater == null) {
            bizObjectQueryObject.setFilterExpr(projectCodeFilter);
        } else if (projectCode != null && productCode != null && creater != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        } else if (projectCode != null && productCode == null && creater != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, createrCodeFilter);
            bizObjectQueryObject.setFilterExpr(finalFilter);
        }

        if (groupByFields != null && !groupByFields.isEmpty()) {
            bizObjectQueryObject.setGroupByFields(groupByFields);
        }

        if (orderByFields != null) {
            bizObjectQueryObject.setOrderByFields(orderByFields);
        } else {
            List<String> defaultOrderByFields = new ArrayList<>();
            defaultOrderByFields.add("MY_INDEX");
            bizObjectQueryObject.setOrderByFields(defaultOrderByFields);
        }

        BizObjectQueryModel.Options queryOptions = null;
        if (groupByFields != null && !groupByFields.isEmpty()) {
            queryOptions = DataOpUtil.getQueryOptionsByList(groupByFields);
        } else {
            queryOptions = DataOpUtil.getAllQueryOptions(new ProcessFlow());
        }

        bizObjectQueryObject.setOptions(queryOptions);
        return bizObjectQueryObject;
    }

    @Override
    public List<ProcessFlow> read(String projectCode, String productCode, String userId) {
        if (StrUtil.isBlank(projectCode)) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "项目编码不能为空");
        }

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject = getBizObjectQueryObject(projectCode, productCode, userId, null, null);
        Page<BizObjectModel> bizObjectModelPage = bizObjectFacade.queryBizObjects(bizObjectQueryObject);
        List<ProcessFlow> result = new ArrayList<>();
        if (bizObjectModelPage != null && CollectionUtils.isNotEmpty(bizObjectModelPage.getContent())) {
            List<? extends BizObjectModel> content = bizObjectModelPage.getContent();
            for (BizObjectModel bizObjectModel : content) {
                // 根据请求的数据类型进行判断，如果是新产业判断是否存在会出现不同项目号，如果不是，不同产品
                Map<String, Object> data = bizObjectModel.getData();
                ProcessFlow processFlow = EpmBusinessEntity.fromMapStatic(ProcessFlow.class, data);
                processFlow.setVirtualObjectId(bizObjectModel.getId());
                result.add(processFlow);
            }
        }
        return result;
    }


    @Override
    public boolean isNewIndustryItem(List<ErpProjectItem> items) {
        boolean hasAnyBlank = items.stream().anyMatch(o -> {
            return StringUtils.isAnyBlank(o.getProductCode(), o.getProductCode());
        });

        boolean hasNoNewIndustryItem = false;
        if (items.size() > 1) {
            hasNoNewIndustryItem = items.stream().anyMatch(o -> {
                return !"Y".equals(o.getNewIndustryFlag());
            });
        }

        if (hasAnyBlank || hasNoNewIndustryItem) {
            throw new PortalException(ResultEnum.ILLEGAL_PARAMETER_ERR.getErrCode(), "请求参数错误");
        }

        if (items.size() == 1) {
            return "Y".equals(items.get(0).getNewIndustryFlag());
        }

        return true;
    }

    @Override
    public List<ProcessProjectInfo> processFlowSyncRequestToBoms(ProcessFlowSyncRequest processFlowSyncRequest) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        RLock lock = redissonClient.getLock("epmBomSendErp:" + userId);
        lock.lock();
        try {
            List<ProcessProjectInfo> projectInfos = new ArrayList<>();

            for (ProcessFlowSyncItem item : processFlowSyncRequest.getItems()) {
                Map<ProcessProjectInfo, List<ProcessFlow>> flowProjectInfoListMap = processProjectInfoServiceImpl.readFlowProjectItems(item.getProjectCode(), item.getProductCode(), userId);
                projectInfos = bomServiceImpl.processFlowToBoms(flowProjectInfoListMap);
                bomServiceImpl.epmBomSendErpByProject(processFlowSyncRequest);
            }
            return projectInfos;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean partInsertOrUpdate(String projectObjectId, ProcessProjectInfo processProjectInfo, List<ProcessFlow> partProcessFlows) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        String projectCode = processProjectInfo.getProjectCode();
        String productCode = processProjectInfo.getProductCode();

        /*List<ProcessFlow> allProcessFlows = read(projectCode, productCode, userId);

        Map<String, ProcessFlow> allProcessFlowMap = new HashMap<>();
        for (ProcessFlow allProcessFlow : allProcessFlows) {
            allProcessFlowMap.put(allProcessFlow.getUniqueId(), allProcessFlow);
            allProcessFlow.setProjectCode(projectCode);
            allProcessFlow.setProductCode(productCode);
        }*/

        for (ProcessFlow partProcessFlow : partProcessFlows) {
            partProcessFlow.setProjectCode(projectCode);
            partProcessFlow.setProductCode(productCode);
        }

        //List<ProcessNode> allProcessNodes = TreeBuilder.buildTree(allProcessFlows);
        List<ProcessNode> partProcessNodes = TreeBuilder.buildTree(partProcessFlows);

        //ProcessNode allDataNode = allProcessNodes.isEmpty() ? null : allProcessNodes.get(0);
        ProcessNode partDataNode = partProcessNodes.isEmpty() ? null : partProcessNodes.get(0);

        // 批量保存新增和更新
        List<BizObjectModel> allModels = new ArrayList<>();
        List<ProcessFlow> processFlows = TreeBuilder.flattenToList(partDataNode);
        List<BizObjectModel> insertModels = new ArrayList<>();
        for (ProcessFlow processFlow : processFlows) {
            BizObjectModel bizObjectModel = new BizObjectModel(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processFlow.toMap(), false);
            bizObjectModel.setId(DataOpUtil.genId());
            insertModels.add(bizObjectModel);
        }
        allModels.addAll(insertModels);

        if (!allModels.isEmpty()) {
            // 设置父子关系
            Map<String, BizObjectModel> modelMap = new HashMap<>();
            for (BizObjectModel objectModel : allModels) {
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if (tempMyIndex == null || tempMyIndex.isEmpty()) {
                    modelMap.put("root", objectModel);
                    continue;
                }
                modelMap.put(tempMyIndex, objectModel);
            }

            for (BizObjectModel objectModel : allModels) {
                String parentIndex = (String) objectModel.getData().get("PARENT_INDEX");
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if ((parentIndex == null || parentIndex.isEmpty()) && (tempMyIndex == null || tempMyIndex.isEmpty())) {
                    continue;
                } else if (parentIndex == null || parentIndex.isEmpty()) {
                    objectModel.getData().put("PROJECT_PRODUCT_CODE", projectObjectId);
                    if (modelMap.containsKey("root")) {
                        objectModel.getData().put("REF_PARENT_INDEX", modelMap.get("root").getId());
                    }
                    continue;
                }

                objectModel.getData().put("PROJECT_PRODUCT_CODE", projectObjectId);
                if (modelMap.containsKey(parentIndex)) {
                    objectModel.getData().put("REF_PARENT_INDEX", modelMap.get(parentIndex).getId());
                }
            }
            deleteProcessFlow(projectCode, productCode, userId);
            bizObjectFacade.batchSaveBizObjectModel(userId, allModels, null);
        }

        // 使用新的比较器进行结构比较
        /*ComparisonResult comparisonResult = ProcessTreeComparator.compareTrees(allDataNode, partDataNode);

        List<ProcessFlow> toDelete = comparisonResult.getToDelete().stream().map(ProcessNode::getProcessFlow).collect(Collectors.toList());
        List<ProcessFlow> toUpdate = comparisonResult.getToUpdate().stream().map(ProcessNode::getProcessFlow).collect(Collectors.toList());
        List<ProcessFlow> toInsert = comparisonResult.getToInsert().stream().map(ProcessNode::getProcessFlow).collect(Collectors.toList());

        // 处理删除操作
        for (ProcessFlow processFlow : toDelete) {
            if (processFlow.getVirtualObjectId() != null) {
                bizObjectFacade.deleteBizObject(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processFlow.getVirtualObjectId());
            }
        }

        // 处理新增操作
        List<BizObjectModel> insertModels = new ArrayList<>();
        for (ProcessFlow processFlow : toInsert) {
            BizObjectModel bizObjectModel = new BizObjectModel(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processFlow.toMap(), false);
            bizObjectModel.setId(DataOpUtil.genId());
            insertModels.add(bizObjectModel);
        }

        // 处理更新操作
        List<BizObjectModel> updateModels = new ArrayList<>();
        for (ProcessFlow processFlow : toUpdate) {
            ProcessFlow originalFlow = allProcessFlowMap.get(processFlow.getUniqueId());
            if (originalFlow != null && originalFlow.getVirtualObjectId() != null) {
                BizObjectModel bizObjectModel = new BizObjectModel(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processFlow.toMap(), false);
                bizObjectModel.setId(originalFlow.getVirtualObjectId());
                updateModels.add(bizObjectModel);
            }
        }

        // 批量保存新增和更新
        List<BizObjectModel> allModels = new ArrayList<>();
        allModels.addAll(insertModels);
        allModels.addAll(updateModels);

        if (!allModels.isEmpty()) {
            // 设置父子关系
            Map<String, BizObjectModel> modelMap = new HashMap<>();
            for (BizObjectModel objectModel : allModels) {
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if (tempMyIndex == null || tempMyIndex.isEmpty()) {
                    modelMap.put("root", objectModel);
                    continue;
                }
                modelMap.put(tempMyIndex, objectModel);
            }

            for (BizObjectModel objectModel : allModels) {
                String parentIndex = (String) objectModel.getData().get("PARENT_INDEX");
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if ((parentIndex == null || parentIndex.isEmpty()) && (tempMyIndex == null || tempMyIndex.isEmpty())) {
                    continue;
                } else if (parentIndex == null || parentIndex.isEmpty()) {
                    objectModel.getData().put("PROJECT_PRODUCT_CODE", projectObjectId);
                    if (modelMap.containsKey("root")) {
                        objectModel.getData().put("REF_PARENT_INDEX", modelMap.get("root").getId());
                    }
                    continue;
                }

                objectModel.getData().put("PROJECT_PRODUCT_CODE", projectObjectId);
                if (modelMap.containsKey(parentIndex)) {
                    objectModel.getData().put("REF_PARENT_INDEX", modelMap.get(parentIndex).getId());
                }
            }

            bizObjectFacade.batchSaveBizObjectModel(userId, allModels, null);
        }*/

        return true;
    }

    @Override
    public boolean insertByDelete(ErpProjectItem processProjectInfo, List<ProcessFlow> partProcessFlows) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        String projectCode = processProjectInfo.getProjectCode();
        String productCode = processProjectInfo.getProductCode();

        // 判断项目是否存在，不存在新增，存在获取
        List<ProcessProjectInfo> read = processProjectInfoServiceImpl.readByCurrentUser(projectCode, productCode);
        String projectid = null;
        if (read.isEmpty()) {
            projectid = processProjectInfoServiceImpl.insert(processProjectInfo.toProcessProjectInfo());
        } else {
            projectid = read.get(0).getId();
        }

        for (ProcessFlow partProcessFlow : partProcessFlows) {
            partProcessFlow.setProjectCode(projectCode);
            partProcessFlow.setProductCode(productCode);
            partProcessFlow.setProductName(processProjectInfo.getProductName());
        }

        List<ProcessNode> partProcessNodes = TreeBuilder.buildTreeForExcel(partProcessFlows);

        List<ProcessFlow> processFlows = new ArrayList<>();
        for (ProcessNode partProcessNode : partProcessNodes) {
            List<ProcessFlow> tempProcessFlows = TreeBuilder.flattenToList(partProcessNode);
            processFlows.addAll(tempProcessFlows);
        }

        List<BizObjectModel> insertModels = new ArrayList<>();
        for (ProcessFlow processFlow : processFlows) {
            BizObjectModel bizObjectModel = new BizObjectModel(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processFlow.toMap(), false);
            bizObjectModel.setId(DataOpUtil.genId());
            insertModels.add(bizObjectModel);
        }
        List<BizObjectModel> allModels = new ArrayList<>(insertModels);

        if (!allModels.isEmpty()) {
            // 设置父子关系
            Map<String, BizObjectModel> modelMap = new HashMap<>();
            for (BizObjectModel objectModel : allModels) {
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if (tempMyIndex == null || tempMyIndex.isEmpty()) {
                    modelMap.put("root", objectModel);
                    continue;
                }
                modelMap.put(tempMyIndex, objectModel);
            }

            for (BizObjectModel objectModel : allModels) {
                String parentIndex = (String) objectModel.getData().get("PARENT_INDEX");
                String tempMyIndex = (String) objectModel.getData().get("MY_INDEX");
                if ((parentIndex == null || parentIndex.isEmpty()) && (tempMyIndex == null || tempMyIndex.isEmpty())) {
                    continue;
                } else if (parentIndex == null || parentIndex.isEmpty()) {
                    objectModel.getData().put("PROJECT_PRODUCT_CODE", projectid);
                    if (modelMap.containsKey("root")) {
                        objectModel.getData().put("REF_PARENT_INDEX", modelMap.get("root").getId());
                    }
                    continue;
                }

                objectModel.getData().put("PROJECT_PRODUCT_CODE", projectid);
                if (modelMap.containsKey(parentIndex)) {
                    objectModel.getData().put("REF_PARENT_INDEX", modelMap.get(parentIndex).getId());
                }
            }
            deleteProcessFlow(projectCode, productCode, userId);
            bizObjectFacade.batchSaveBizObjectModel(userId, allModels, null);
        }
        return true;
    }

    void deleteProcessFlow(String projectCode, String productCode, String creater) {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject = getBizObjectQueryObject(projectCode, productCode, creater, null, null);
        Page<BizObjectModel> processModelPage = bizObjectFacade.queryBizObjects(bizObjectQueryObject);

        List<String> processObjectIds = new ArrayList<>();
        if (processModelPage != null && CollectionUtils.isNotEmpty(processModelPage.getContent())) {
            List<? extends BizObjectModel> content = processModelPage.getContent();
            for (BizObjectModel bizObjectModel : content) {
                processObjectIds.add(bizObjectModel.getId());
            }
        }
        for (String processObjectId : processObjectIds) {
            bizObjectFacade.deleteBizObject(SchemaCodeConstant.TD_PROJECT_PLAN_TEMP, processObjectId);
        }
    }

    public boolean deleteAllById() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        deleteProcessFlow(null, null, userId);
        return true;
    }

    @Transactional
    @Override
    public void processFlowSyncForExcel(List<ErpProjectItem> read, List<ProcessFlow> dataList) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        RLock lock = redissonClient.getLock("processFlowSync:" + userId);
        lock.lock();
        try {
            if (read.isEmpty()) {
                throw new PortalException(ErrCode.REQUEST_NULL, "当前请求不合法");
            }

            if (dataList.isEmpty()) {
                throw new PortalException(ErrCode.REQUEST_NULL, "Excel数据为空");
            }

            boolean newIndustryItem = isNewIndustryItem(read);
            if (!newIndustryItem) {
                throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "当前不支持非新产业项目");
            }

            // 判断是否是新产业项目，是否有多个产品的可能性
            // 判断是否是新产业项目，是否有多个产品的可能性，如果当前项目是新产业项目
            //List<ProcessFlowSyncItem> collect = read.stream().map(ErpProjectItem::toProcessFlowSyncItem).collect(Collectors.toList());

            List<ErpProjectItem> shouldDeleteProjectItems = getShouldDeleteProjects(read, newIndustryItem);

            // 重新同步数据
            Iterator<ErpProjectItem> iterator = shouldDeleteProjectItems.iterator();
            while (iterator.hasNext()) {
                ErpProjectItem item = iterator.next();
                insertByDelete(item, dataList);
                iterator.remove();
            }

            for (ErpProjectItem erpProjectItem : shouldDeleteProjectItems) {
                processProjectInfoServiceImpl.delete(erpProjectItem.getProjectCode(), erpProjectItem.getProductCode(), userId);
                deleteProcessFlow(erpProjectItem.getProjectCode(), erpProjectItem.getProductCode(), userId);
            }
        } finally {
            lock.unlock();
        }
    }

}
