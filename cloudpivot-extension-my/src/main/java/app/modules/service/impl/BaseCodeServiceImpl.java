package app.modules.service.impl;

import app.modules.constant.SchemaCodeConstant;
import app.modules.constant.UserConstant;
import app.modules.model.entity.BaseCode;
import app.modules.model.entity.baseEntity.EpmBusinessEntity;
import app.modules.service.BaseCodeService;
import com.authine.cloudpivot.engine.api.facade.BizObjectFacade;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel;
import com.authine.cloudpivot.engine.component.query.api.FilterExpression;
import com.authine.cloudpivot.engine.component.query.api.Page;
import com.authine.cloudpivot.engine.component.query.api.helper.Q;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class BaseCodeServiceImpl implements BaseCodeService {
    @Autowired
    private EngineService engineService;


    @Override
    public List<BaseCode> getBaseCodeList() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        List<BaseCode> result = new ArrayList<>();

        BizObjectQueryModel bizObjectQueryObject1 = new BizObjectQueryModel();
        bizObjectQueryObject1.setSchemaCode(SchemaCodeConstant.BASE_CODE);
        bizObjectQueryObject1.setQueryCode(SchemaCodeConstant.BASE_CODE);
        Page<BizObjectModel> bizObjectModelPage1 = bizObjectFacade.queryBizObjects(bizObjectQueryObject1);

        if (bizObjectModelPage1 != null && CollectionUtils.isNotEmpty(bizObjectModelPage1.getContent())) {
            List<? extends BizObjectModel> content = bizObjectModelPage1.getContent();
            for (BizObjectModel bizObjectModel : content) {
                // 根据请求的数据类型进行判断，如果是新产业判断是否存在会出现不同项目号，如果不是，不同产品
                Map<String, Object> data = bizObjectModel.getData();
                BaseCode baseCode = EpmBusinessEntity.fromMapStatic(BaseCode.class, data);
                result.add(baseCode);
            }
        }
        return result;
    }
}
