package app.modules.service.impl;

import com.authine.cloudpivot.engine.api.model.dto.MessageDTO;
import com.authine.cloudpivot.engine.domain.organization.RelatedCorpSetting;
import com.authine.cloudpivot.engine.service.message.IMessageService;
import com.authine.cloudpivot.engine.service.organization.exception.DataSourceException;
import com.authine.cloudpivot.engine.service.system.RelatedCorpSettingService;
import com.authine.cloudpivot.web.api.service.EngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发送短信消息
 */
@Service
@Slf4j
public class ZCSMSMessageServiceImpl implements IMessageService {
    @Autowired
    private EngineService engineService;
    @Autowired
    private RelatedCorpSettingService relatedCorpSettingService;

    @Override
    public List<String> sendMsg(MessageDTO message) throws DataSourceException {
        String corpId = engineService.getRelatedCorpSettingFacade().getMain().getCorpId();
        RelatedCorpSetting relatedCorpSetting = this.relatedCorpSettingService.getRelatedCorpSettingByCorpId(corpId);
        List<String> receiverMobileList =
                message.getReceiverList().stream().map(m -> engineService.getUserFacade().getByUserIdAndCorpId(m,
                        corpId).getMobile()).collect(Collectors.toList());
        for (String mobile : receiverMobileList) {
            Map<String, Object> parameter = new HashMap<>();
            parameter.put("pi_ReportMobile",mobile);
            parameter.put("pi_ReportMessage1",message.getContent());
            engineService.getBizServiceFacade().testBizServiceMethod("SMS_SEND",
                    "short_message_send_sp", parameter);

        }

        return null;
    }

    @Override
    public String initChannel() {
        return "ZCSMS";
    }
}
