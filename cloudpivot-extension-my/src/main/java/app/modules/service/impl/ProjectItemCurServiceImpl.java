package app.modules.service.impl;

import app.modules.constant.ServiceConstant;
import app.modules.constant.UserConstant;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.ProjectItemCurService;
import app.modules.utils.BizServiceFacadeUtil;
import cn.hutool.core.bean.BeanUtil;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.api.model.bizservice.BizDatabaseConnectionPoolModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceMethodModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceModel;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectItemCurServiceImpl implements ProjectItemCurService {

    @Autowired
    private EngineService engineService;

    @Override
    public List<ErpProjectItem> read(Map<String, Object> selectInputParams) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        String serviceCode = ServiceConstant.PROJ_ITEM_CUR_SERVICE;
        String selectMethodCode = ServiceConstant.PROJ_ITEM_CUR_METHOD;

        bizServiceFacade.refresh(serviceCode);

        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel selectMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, selectMethodCode);
        if (selectMethod == null) {
            throw new RuntimeException("查询业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + selectMethodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }

        // 3. 查询数据
        Map<String, Object> insertOutputMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                selectMethodCode,
                selectInputParams
        );

        // 提取 "data" 字段中的 List<Map<String, Object>>
        Map<String, Object> dataMap = (Map<String, Object>) insertOutputMap.get("data");
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataMap.get("data");

        // 转换为 List<ErpProjectItem>

        return dataList.stream()
                .map(map -> BeanUtil.toBean(map, ErpProjectItem.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<ErpProjectItem> getRealErpProjectItem(List<ErpProjectItem> old) {
        List<ErpProjectItem> read = this.read(new HashMap<>());

        List<ErpProjectItem> projectInfos = read.stream()
                .map(ErpProjectItem::trim)
                .filter(erpItem -> old.stream().anyMatch(syncItem ->
                        syncItem.getProjectCode().trim().equals(erpItem.getProjectCode().trim()) &&
                                syncItem.getProductCode().trim().equals(erpItem.getProductCode().trim())
                ))
                .collect(Collectors.toList());
        return projectInfos;
    }

    @Override
    public List<ErpProjectItem> getRealErpProjectItem(ProcessProjectInfo old) {
        ErpProjectItem erpProjectItem = old.toErpProjectItem();
        List<ErpProjectItem> erpProjectItems = new ArrayList<>();
        erpProjectItems.add(erpProjectItem);
        return getRealErpProjectItem(erpProjectItems);
    }

}
