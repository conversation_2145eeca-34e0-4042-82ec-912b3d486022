package app.modules.service.impl;

import app.modules.constant.ServiceConstant;
import app.modules.constant.UserConstant;
import app.modules.model.entity.ProcessFlow;
import app.modules.service.ErpService;
import app.modules.utils.BizServiceFacadeUtil;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ErpServiceImpl implements ErpService {

    @Autowired
    private EngineService engineService;

    @Override
    public ProcessFlow getScrapMaterialFromErp(String queryItemCode) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        Map<String, Object> inputParam = new HashMap<>();
        inputParam.put("ITEM_CODE", queryItemCode);

        List<Map<String, Object>> select = BizServiceFacadeUtil.select(bizServiceFacade,
                ServiceConstant.ERP_INV_MASTER_QUERY_SERVICE,
                ServiceConstant.ERP_SCRAP_MATERIAL_QUERY_METHOD,
                inputParam);

        if (select.isEmpty()) {
            return null;
        }
        assert select.size() < 2;

        Map<String, Object> stringObjectMap = select.get(0);
        if (stringObjectMap.isEmpty()) {
            return null;
        }

        String itemCode = (String) stringObjectMap.get("ITEM_CODE");
        String itemName = (String) stringObjectMap.get("ITEM_NAME");
        String coopFlag = (String) stringObjectMap.get("COOP_FLAG");
        String mpFlag = (String) stringObjectMap.get("MP_FLAG");
        String netWeight = (String) stringObjectMap.get("NET_WEIGHT");
        String itemNorm = (String) stringObjectMap.get("ITEM_NORM");
        String itemModel = (String) stringObjectMap.get("ITEM_MODEL");
        String phantomFlag = (String) stringObjectMap.get("PHANTOM_FLAG");
        String deptCode = (String) stringObjectMap.get("DEPT_CODE");

        ProcessFlow processFlow = new ProcessFlow();
        processFlow.setItemNo(itemCode);
        processFlow.setItemName(itemName);
        processFlow.setCoopFlag(coopFlag);
        processFlow.setMpFlag(mpFlag);
        processFlow.setItemWeight(netWeight);
        processFlow.setItemNorm(itemNorm);
        processFlow.setItemModel(itemModel);
        processFlow.setPhantomFlag(phantomFlag);
        processFlow.setVirture(phantomFlag);
        processFlow.setDeptCode(deptCode);

        return processFlow;
    }
}
