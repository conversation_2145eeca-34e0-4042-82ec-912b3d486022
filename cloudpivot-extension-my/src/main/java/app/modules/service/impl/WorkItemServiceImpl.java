package app.modules.service.impl;

import app.modules.configuration.ZCTConfig;
import app.modules.model.dto.todo.TodoReq;
import app.modules.model.dto.todo.TodoReqBody;
import app.modules.service.WorkItemService;
import app.modules.utils.TodoTokenUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.engine.domain.organization.RelatedCorpSetting;
import com.authine.cloudpivot.engine.open.domain.workflow.WorkItem;
import com.authine.cloudpivot.engine.service.system.RelatedCorpSettingService;
import com.authine.cloudpivot.web.api.service.EngineService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WorkItemServiceImpl implements WorkItemService {
    @Autowired
    private ZCTConfig zctConfig;

    private final RestTemplate restTemplate;

    @Autowired
    private EngineService engineService;

    @Autowired
    private RelatedCorpSettingService relatedCorpSettingService;

    WorkItemServiceImpl(ZCTConfig zctConfig) {
        restTemplate = new RestTemplateBuilder()
                .rootUri(zctConfig.getBase_url())
                .requestFactory(this::createClientHttpRequestFactory)
                .build();
    }

    @Override
    public void createWorkItem(WorkItem workItem) {
        String participant = workItem.getParticipant();
        UserModel user = engineService.getOrganizationFacade().getUser(participant);
        String originator = workItem.getOriginator();
        UserModel startUser = engineService.getOrganizationFacade().getUser(originator);
        String account = user.getUsername();
        String corpId = engineService.getRelatedCorpSettingFacade().getMain().getCorpId();
        RelatedCorpSetting relatedCorpSetting = this.relatedCorpSettingService.getRelatedCorpSettingByCorpId(corpId);
        TodoReqBody reqBody = new TodoReqBody();
        // 唯一标识待办数据，全局唯一
        String id = workItem.getId();
        reqBody.setId(id);
        reqBody.setTitle(workItem.getInstanceName());
        reqBody.setContent(workItem.getActivityName());
        reqBody.setSysId(zctConfig.getSysId().toString());
        reqBody.setSysName(zctConfig.getSysName());
        reqBody.setSysIcon("http://zctxc.crrcgc.cc:8888/schedule_icons/1.png");
        reqBody.setType(zctConfig.getSysId().toString());
        reqBody.setSource("2");
        reqBody.setCardType("1");
        reqBody.setUserNo(account);
        reqBody.setBizType("1");
        reqBody.setReleaseDate(workItem.getStartTime().toString());
        reqBody.setUrgent("true");
        reqBody.setSponsor(startUser.getName());
        reqBody.setSubsidiary("中国中车集团有限公司");
        String pcUrl = String.format("%s/form/detail?workitemId=%s&workflowInstanceId=%s",
                relatedCorpSetting.getPcServerUrl(), workItem.getId(), workItem.getInstanceId());
        String mobileUrl = String.format("%s#/form/detail?workitemId=%s&workflowInstanceId=%s",
                relatedCorpSetting.getMobileServerUrl(), workItem.getId(), workItem.getInstanceId());
        reqBody.setPcUrl(pcUrl);
        reqBody.setH5Url(mobileUrl);
        // 操作类型（1:待办转已办2：待阅转已阅）
        reqBody.setActionType(1);
        reqBody.setExtend("");
        TodoReq req = new TodoReq(reqBody);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("userStaffNo", account);
        userInfo.put("account", account);

        String token = generateToken(userInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String body = JSON.toJSONString(req);
        log.info("body:{}", body);
        String signature = calcSignature("POST", "/schedule-todo/sync", "application/json", body.getBytes(StandardCharsets.UTF_8));
        headers.set("signature", signature);

        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("/schedule-todo/sync", HttpMethod.POST, new HttpEntity<>(body.getBytes(StandardCharsets.UTF_8), headers), JSONObject.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            if (responseEntity.getBody() != null) {
                log.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody()));
                if (responseEntity.getBody().containsKey("Response")) {
                    JSONObject responseHead = responseEntity.getBody().getJSONObject("Response").getJSONObject("head");
                    if ("000000".equals(responseHead.getString("statusCode"))) {
                        log.info("待办数据同步成功");
                    } else {
                        log.error("待办数据同步失败：{}-{}", responseHead.getString("statusCode"), responseHead.getString("statusMsg"));
                    }
                }
            }
        }
    }

    @Override
    public void completedToDo(WorkItem workItem) {

    }

    /**
     * 待办数据状态同步（待办转已办）
     */
    public void resultSync(WorkItem workItem) {
        String participant = workItem.getParticipant();
        UserModel user = engineService.getOrganizationFacade().getUser(participant);
        String account = user.getUsername();

        TodoReqBody reqBody = new TodoReqBody();
        reqBody.setId(workItem.getId());
        reqBody.setSysId(zctConfig.getSysId().toString());
        reqBody.setSysName(zctConfig.getSysName());
        reqBody.setUserNo(account);

        TodoReq req = new TodoReq(reqBody);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("userStaffNo", account);
        userInfo.put("account", account);

        String token = generateToken(userInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String body = JSON.toJSONString(req);
        log.info("body:{}", body);
        String signature = calcSignature("POST", "/schedule-todo/result/sync", "application/json", body.getBytes(StandardCharsets.UTF_8));
        headers.set("signature", signature);

        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("/schedule-todo/result/sync", HttpMethod.POST, new HttpEntity<>(body.getBytes(StandardCharsets.UTF_8), headers), JSONObject.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            if (responseEntity.getBody() != null) {
                log.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody()));
                if (responseEntity.getBody().containsKey("Response")) {
                    JSONObject responseHead = responseEntity.getBody().getJSONObject("Response").getJSONObject("head");
                    if ("000000".equals(responseHead.getString("statusCode"))) {
                        log.info("待办数据状态同步成功");
                    } else {
                        log.error("待办数据状态同步失败：{}-{}", responseHead.getString("statusCode"), responseHead.getString("statusMsg"));
                    }
                }
            }
        }
    }

    /**
     * 生成token
     *
     * @param userInfo
     * @return
     */
    private String generateToken(Map<String, String> userInfo) {
        return TodoTokenUtil.genTokenRSA(userInfo, zctConfig.getPrivateKey(), DateUtils.addDays(new Date(), 7));
    }

    /**
     * 计算签名
     *
     * @param method
     * @param uri
     * @param contentType
     * @param body
     * @return
     */
    private String calcSignature(String method, String uri, String contentType, byte[] body) {
        String bodySha = SecureUtil.sha256(new ByteArrayInputStream(body));
        HMac hMac = SecureUtil.hmacSha256(zctConfig.getSignatureKey());
        hMac.getEngine().update(method.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(uri.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(contentType.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(bodySha.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(hMac.getEngine().doFinal());
    }

    private ClientHttpRequestFactory createClientHttpRequestFactory() {
        // 忽略ssl证书
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

        // 业务系统也可以使用除okhttp之外的其他客户端组件（如apache http client等）
        OkHttpClient client = new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory, trustManager)
                .build();

        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory(client);
        httpRequestFactory.setReadTimeout(3000);
        httpRequestFactory.setConnectTimeout(3000);

        return httpRequestFactory;
    }
}
