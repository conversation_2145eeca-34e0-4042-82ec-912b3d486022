package app.modules.service.impl;

import com.authine.cloudpivot.engine.enums.type.MessageSettingType;
import com.authine.cloudpivot.engine.service.message.IMessageDesigntimeService;
import com.authine.cloudpivot.engine.service.message.model.IMessageMetaData;
import com.authine.cloudpivot.engine.service.message.model.IMessageSetting;
import org.springframework.stereotype.Service;

/**
 * 中车通消息渠道
 */
@Service
public class ZCTConfigServiceImpl implements IMessageDesigntimeService {

    private static final IMessageMetaData METADATA = new IMessageMetaData("ZCT", "ZCT", "中车通消息",
            MessageSettingType.BuildOut);
    @Override
    public IMessageMetaData getConfig() {
        return METADATA;
    }

    @Override
    public IMessageSetting getSettingConfig() {
        return null;
    }

    @Override
    public Boolean checkMessageSetting(IMessageSetting iMessageSetting) {
        return true;
    }
}
