package app.modules.service.impl;

import app.modules.constant.SchemaCodeConstant;
import app.modules.constant.UserConstant;
import app.modules.model.dto.processflow.ProcessFlowSyncRequest;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.entity.baseEntity.EpmBusinessEntity;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.ProcessProjectInfoService;
import app.modules.service.ProcessFlowService;
import app.modules.utils.DataOpUtil;
import com.authine.cloudpivot.engine.api.facade.BizObjectFacade;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel;
import com.authine.cloudpivot.engine.component.query.api.FilterExpression;
import com.authine.cloudpivot.engine.component.query.api.Page;
import com.authine.cloudpivot.engine.component.query.api.helper.Q;
import com.authine.cloudpivot.engine.domain.runtime.BizObject;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ProcessProjectInfoServiceImpl implements ProcessProjectInfoService {
    @Autowired
    private EngineService engineService;

    @Resource
    private ProcessProjectInfoService ProcessProjectInfoServiceImpl;

    @Resource
    private ProcessFlowService processFlowServiceImpl;


    @Override
    public List<ProcessProjectInfo> readByCurrentUser(String projectCode, String productCode) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject1 = new BizObjectQueryModel();
        bizObjectQueryObject1.setSchemaCode(SchemaCodeConstant.PROCESS_PROJECT_INFO);
        bizObjectQueryObject1.setQueryCode(SchemaCodeConstant.PROCESS_PROJECT_INFO);
        FilterExpression.Item projectCodeFilter = Q.it("PROJECT_CODE", FilterExpression.Op.Eq, projectCode);
        FilterExpression.Item productCodeFilter = Q.it("PRODUCT_CODE", FilterExpression.Op.Eq, productCode);
        FilterExpression.Item createrFilter = Q.it("creater", FilterExpression.Op.Eq, userId);
        //FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter);
        if (productCode == null && projectCode != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, createrFilter);
            bizObjectQueryObject1.setFilterExpr(finalFilter);
        } else if (projectCode == null && productCode != null) {
            FilterExpression.And finalFilter = Q.and(productCodeFilter, createrFilter);
            bizObjectQueryObject1.setFilterExpr(finalFilter);
        } else if (projectCode != null) {
            FilterExpression.And finalFilter = Q.and(projectCodeFilter, productCodeFilter, createrFilter);
            bizObjectQueryObject1.setFilterExpr(finalFilter);
        }

        Page<BizObjectModel> bizObjectModelPage1 = bizObjectFacade.queryBizObjects(bizObjectQueryObject1);

        List<ProcessProjectInfo> result = new ArrayList<>();
        if (bizObjectModelPage1 != null && CollectionUtils.isNotEmpty(bizObjectModelPage1.getContent())) {
            List<? extends BizObjectModel> content = bizObjectModelPage1.getContent();
            for (BizObjectModel bizObjectModel : content) {
                // 根据请求的数据类型进行判断，如果是新产业判断是否存在会出现不同项目号，如果不是，不同产品
                Map<String, Object> data = bizObjectModel.getData();
                ProcessProjectInfo processProjectInfo = EpmBusinessEntity.fromMapStatic(ProcessProjectInfo.class, data);
                processProjectInfo.setId(bizObjectModel.getId());
                result.add(processProjectInfo);
            }
        }
        return result;
    }

    @Override
    public Map<ProcessProjectInfo, List<ProcessFlow>> readFlowProjectItems(String projectCode, String productCode, String userId) {
        Map<ProcessProjectInfo, List<ProcessFlow>> result = new HashMap<>();
        List<ProcessProjectInfo> processProjectInfo = this.readByCurrentUser(projectCode, productCode);

        for (ProcessProjectInfo projectInfo : processProjectInfo) {
            ProcessFlowSyncRequest processFlowSyncRequest = new ProcessFlowSyncRequest();
            BeanUtils.copyProperties(projectInfo, processFlowSyncRequest);
            List<ProcessFlow> processFlows = processFlowServiceImpl.read(projectInfo.getProjectCode(), projectInfo.getProductCode(), userId);
            result.put(projectInfo, processFlows);
        }
        return result;
    }


    @Override
    public void insertOrUpdate(ProcessProjectInfo processProjectInfo) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        if (processProjectInfo.getId() == null) {
            insert(processProjectInfo);
        } else {
            update(processProjectInfo);
        }
    }

    @Override
    public String insert(ProcessProjectInfo processFlow) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        String id = DataOpUtil.genId();

        // 3. 构建 entity list
        BizObjectModel bizObjectModel = new BizObjectModel(SchemaCodeConstant.PROCESS_PROJECT_INFO, processFlow.toMap(), false);
        bizObjectModel.setId(id);
        return bizObjectFacade.saveBizObjectModel(userId, bizObjectModel, "id");
    }

    @Override
    public void update(ProcessProjectInfo processFlow) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        String id = processFlow.getId();
        Map<String, Object> map = processFlow.toMap();
        map.remove("ID");
        BizObject bizObject = new BizObject(SchemaCodeConstant.PROCESS_PROJECT_INFO, id, map);
        bizObject.setObjectId(processFlow.getId());
        bizObjectFacade.updateBizObject(bizObject);
    }

    @Override
    public void changeState(ErpProjectItem projectInfo, String sendErpStart, String userId) {
        if (StringUtils.isAnyBlank(projectInfo.getProjectCode(), projectInfo.getProductCode())) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "项目号和产品号不能为空");
        }
        List<ProcessProjectInfo> projectInfos = this.read(projectInfo.getProjectCode(), projectInfo.getProductCode(), userId);
        if (projectInfos.isEmpty()) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "项目号产品号不存在");
        }
        ProcessProjectInfo processProjectInfo = projectInfos.get(0);
        processProjectInfo.setProjectStatus(sendErpStart);
        this.update(processProjectInfo);
    }

    @Override
    public String getProjectStatus(String projectCode, String productCode) {
        if (StringUtils.isAnyBlank(projectCode, productCode)) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "项目号和产品号不能为空");
        }
        List<ProcessProjectInfo> projectInfos = this.readByCurrentUser(projectCode, productCode);
        if (projectInfos.isEmpty()) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "项目号产品号不存在");
        }
        return projectInfos.get(0).getProjectStatus();
    }

    @Override
    public List<ProcessProjectInfo> read(String projectCode, String productCode, String userId) {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        BizObjectQueryModel bizObjectQueryObject1 = new BizObjectQueryModel();
        bizObjectQueryObject1.setSchemaCode(SchemaCodeConstant.PROCESS_PROJECT_INFO);
        bizObjectQueryObject1.setQueryCode(SchemaCodeConstant.PROCESS_PROJECT_INFO);
        FilterExpression.Item projectCodeFilter = Q.it("PROJECT_CODE", FilterExpression.Op.Eq, projectCode);
        FilterExpression.Item productCodeFilter = Q.it("PRODUCT_CODE", FilterExpression.Op.Eq, productCode);
        FilterExpression.Item createrFilter = Q.it("creater", FilterExpression.Op.Eq, userId);
        FilterExpression.And finalFilter = Q.and(projectCodeFilter, createrFilter);
        if (productCode != null) {
            finalFilter.items.add(productCodeFilter);
        }
        if (projectCode != null) {
            finalFilter.items.add(projectCodeFilter);
        }
        if (userId != null) {
            finalFilter.items.add(createrFilter);
        }
        bizObjectQueryObject1.setFilterExpr(finalFilter);

        Page<BizObjectModel> bizObjectModelPage1 = bizObjectFacade.queryBizObjects(bizObjectQueryObject1);

        List<ProcessProjectInfo> result = new ArrayList<>();
        if (bizObjectModelPage1 != null && CollectionUtils.isNotEmpty(bizObjectModelPage1.getContent())) {
            List<? extends BizObjectModel> content = bizObjectModelPage1.getContent();
            for (BizObjectModel bizObjectModel : content) {
                // 根据请求的数据类型进行判断，如果是新产业判断是否存在会出现不同项目号，如果不是，不同产品
                Map<String, Object> data = bizObjectModel.getData();
                ProcessProjectInfo processProjectInfo = EpmBusinessEntity.fromMapStatic(ProcessProjectInfo.class, data);
                processProjectInfo.setId(bizObjectModel.getId());
                result.add(processProjectInfo);
            }
        }
        return result;
    }

    @Override
    public void delete(String projectCode, String productCode, String userId) {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();

        List<ProcessProjectInfo> read = this.read(projectCode, productCode, userId);
        for (ProcessProjectInfo processProjectInfo : read) {
            bizObjectFacade.deleteBizObject(SchemaCodeConstant.PROCESS_PROJECT_INFO, processProjectInfo.getId());
        }
    }

}
