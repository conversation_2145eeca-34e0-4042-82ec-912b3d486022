package app.modules.service;

import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifyAuthRequestBody;
import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifyAuthResponseBody;
import app.modules.model.dto.thirdparty.ThirdpartyMsgNotifySendRequestBody;
import app.modules.model.vo.ThirdpartyRequest;
import app.modules.model.vo.ThirdpartyRequestHead;
import app.modules.model.vo.ThirdpartyResponse;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Date;

@Slf4j
public class MsgNotifyRestTests {

    private static final Logger logger = LoggerFactory.getLogger(MsgNotifyRestTests.class);

    private static RestTemplate restTemplate = new RestTemplateBuilder()
            .rootUri("https://zctxc.crrcgc.cc:7773")
            .requestFactory(MsgNotifyRestTests::createClientHttpRequestFactory)
            .build();
    private static ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        //msgNotifyAuth();
        msgNotifySend();
    }

    /**
     * 认证接口
     */

    public static void msgNotifyAuth() throws Exception {
        Integer sysId = 100034;
        String sysName = "株辆XCBPM";
        String secretId = "c8d0809ebee946678fdd9a8f7bfb1f11";
        String secretKey = "e46f1cd4ab15f80c0510257c4df4ff4efe58b02850aa728fd93cc82d882273af";

        ThirdpartyMsgNotifyAuthRequestBody requestBody = new ThirdpartyMsgNotifyAuthRequestBody();
        requestBody.setSysId(String.valueOf(sysId));
        requestBody.setSecretId(secretId);
        requestBody.setSecretKey(secretKey);
        ThirdpartyRequest<ThirdpartyMsgNotifyAuthRequestBody> thirdpartyRequest = new ThirdpartyRequest<>();
        thirdpartyRequest.setHead(new ThirdpartyRequestHead(String.valueOf(System.currentTimeMillis())));
        thirdpartyRequest.setBody(requestBody);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        // 认证接口token传空
        headers.set("token", "");

        String uri = "/thirdparty/msg/notify/auth";
        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String bodyJson = objectMapper.writeValueAsString(thirdpartyRequest);
        headers.set("signature", calcSignature(secretKey, "POST", uri, "application/json", bodyJson.getBytes(StandardCharsets.UTF_8)));

        ResponseEntity<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(bodyJson.getBytes(StandardCharsets.UTF_8), headers), new ParameterizedTypeReference<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>>() {
        });
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            logger.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody(), SerializerFeature.PrettyFormat));
        }
    }

    /**
     * 消息通知发送
     */

    public static void msgNotifySend() throws Exception {
        Integer sysId = 100034;
        String sysName = "株辆XCBPM";
        String secretId = "c8d0809ebee946678fdd9a8f7bfb1f11";
        String secretKey = "e46f1cd4ab15f80c0510257c4df4ff4efe58b02850aa728fd93cc82d882273af";
        String userStaffNo = "zzclcs1";

        long currentTimeMillis = System.currentTimeMillis();
        ThirdpartyMsgNotifySendRequestBody requestBody = new ThirdpartyMsgNotifySendRequestBody();
        // 全局唯一
        String msgId = currentTimeMillis + "" + RandomUtils.nextInt(1000, 10000);
        requestBody.setMsgId(msgId);
        requestBody.setSysId(String.valueOf(sysId));
        requestBody.setSysName(sysName);
        requestBody.setSysIcon("http://zctxc.crrcgc.cc:8888/vtm/2466fc0c84220a57ecb7f0c9d6ad2460/zct_xinchuang/1726713438140050239.png?sign=606bc7941cae4a52190b878f67654e85&timestamp=1729131145532");
        // 接收者类型（1：receivers为员工号，2：receivers为中车通账号）
        requestBody.setReceiverType(1);
        requestBody.setReceivers(Lists.newArrayList(userStaffNo));
        requestBody.setBizType("");
        requestBody.setTitle(String.format("标题【%d】", currentTimeMillis));
        requestBody.setSubTitle(String.format("子标题【%d】", currentTimeMillis));
        requestBody.setContentTitle(String.format("内容标题【%d】", currentTimeMillis));
        requestBody.setContent(String.format("内容【%d】", currentTimeMillis));
        requestBody.setReleaseDate(DateFormatUtils.format(new Date(currentTimeMillis), "yyyy-MM-dd HH:mm:ss"));
        requestBody.setSponsor("");
        requestBody.setUrgent("false");
        requestBody.setH5Url("https://crrcgc.cc/crrcgc/2025-01/13/article_2025011318433582301.html");
        requestBody.setWebUrl("https://crrcgc.cc/crrcgc/2025-01/13/article_2025011318433582301.html");
        requestBody.setExtend("");
        ThirdpartyRequest<ThirdpartyMsgNotifySendRequestBody> thirdpartyRequest = new ThirdpartyRequest<>();
        thirdpartyRequest.setHead(new ThirdpartyRequestHead(String.valueOf(System.currentTimeMillis())));
        thirdpartyRequest.setBody(requestBody);

        // 获取方式参考msgNotifyAuth
        String token = "";
        String uri = "/thirdparty/msg/notify/send";
        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String bodyJson = objectMapper.writeValueAsString(thirdpartyRequest);
        headers.set("signature", calcSignature(secretKey, "POST", uri, "application/json", bodyJson.getBytes(StandardCharsets.UTF_8)));

        ResponseEntity<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(bodyJson.getBytes(StandardCharsets.UTF_8), headers), new ParameterizedTypeReference<ThirdpartyResponse<ThirdpartyMsgNotifyAuthResponseBody>>() {
        });
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            logger.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody(), SerializerFeature.PrettyFormat));
        }
    }

    /**
     * 计算签名
     *
     * @param method
     * @param uri
     * @param contentType
     * @param body
     * @return
     */
    private static String calcSignature(String secretKey, String method, String uri, String contentType, byte[] body) {
        String bodySha = SecureUtil.sha256(new ByteArrayInputStream(body));
        HMac hMac = SecureUtil.hmacSha256(secretKey);
        hMac.getEngine().update(method.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(uri.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(contentType.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(bodySha.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(hMac.getEngine().doFinal());
    }

    private static ClientHttpRequestFactory createClientHttpRequestFactory() {
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

        OkHttpClient client = new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory, trustManager)
                .build();

        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory(client);
        httpRequestFactory.setReadTimeout(3000);
        httpRequestFactory.setConnectTimeout(3000);

        return httpRequestFactory;
    }

}