package app.modules.service;

import app.modules.model.dto.todo.TodoReq;
import app.modules.model.dto.todo.TodoReqBody;
import app.modules.utils.TodoTokenUtil;
import cn.hutool.core.lang.generator.Generator;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class TodoDemo {

    private static final String privateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDfqi5sY5u3th/rCVO9pr7QWyv+tGZCGGwk46be36OjTrzYQTFygqTBFVx3f6MvLhXFepZQK2ORn5I9yuiW1uPiyEFNDL1GvDe3Dcms3lSTJIPA2gWo1DR43fjSUeJHPgTd3RpJL3tz+whN7Kk/KcPLFSn9Bl2rncmpjM7SB60nhOOARywZ2IbfQdLzdGMYOarMXp9S7IF14PRQ6lZPaKHcNQcCgCxhxYrG09WR5J/DPY52IS7cA2zXXPswLnJmNAZ5gKawdhMO5sr1WqFMCGLVYQJ0jBLw8XDjPFW9D9Zq0SA11sJqBHtYMYAk1TmHsczmlsaTvRXswDUchl2n5wz/AgMBAAECggEBAMFZTpRob16a1HDMOVYDZYScrCWdMaEFl8cz/TdBwSYVlO6lPOeuxFod6zdqYVkRz8RNOs/HEmX6ueMtG2/6CDotvzWqQ514a/mUs7j64cXplPQMBURzXYD28nhuTNwlYvOghW6q21/hrMYgRhbSNe3DN+8Bp8wJtSEbFlc+R5OWkWyZkkCbcxt4xPKN7HPXdlLDU5xl02wh+M6d5fdW/+3JeYQ2LVPqlKOhsoiLkQACI56bDzyLGMFXDXQjCKOuTy9CFgIaKWlBOSjudayF5FhOi23fMPf0OEEwaz7VfjmcLARIJRxggcelRAWC3iCndkW+RnfPFZqax3cpfJfhGBECgYEA9hAx9vWJtrS8s/KBKLVQsA0asU4/wComAOkLqCdxVtoRDiFiF9oLvC+IdgrInzQ42u4jMesr2/9hlQ3pb1N8w7VtEuYML0fuK6jiObF59NbOhipGzJYRFL8Gl5DtTXNiu+O04bGPY1CU/8tSnDl1Wq6+gAg8pEpCrCn3TsKJdTsCgYEA6LJuKHPfrG2q9Vzh/UbwoiKfh8DVkYG2gA0BaHa7GUgQng5HQZueCrcdAH/TKdohoaScORzvALJQvBdnjuLb5zhKZFW7auWRk7GwodOjVDy3wa+O2lOoxT0x76oDJxZh2YqyNytL2MuCKwr9cTb3hf7RIEfSxHaO6dPos04+uw0CgYEAg2vU89zip8XSf53HJDHrHYxyND7hx8X7NENEKZSfcU7ZCx4DYU/hMqqEjN9nlbhzoWLNQ20iqMa2nMCobABAQ9sTnaNm4J3fdEwoP74tr/pGpdvQopaIGveIEs2iINq+4jFVO7H1LosseY5j2fLKXhu0vXcoxGqxP3jOkK9QcV8CgYEAuXwBcx1qXffNHmg7e7jgd11B7OQ5WRMcdlXnmW2LFkvrcxHOmu2AvOnZpFw/5kyKIl8kaCI4IUYCNOwzECKlR1oHcpMkIcF8mU46jcQ1nSAJGcrnBAFLtm1hMLlEahPdS5rTFEbe3qDZ0ZWhEG3QQG8uI50c1J3srVbNVqA4QMECgYAl583n//51+hEthKdBVU57c8H7s/KUReseY36bQjFGyUeQSa7vriwWtiFQRXmu2Lw5FZ57BsLcc9JYo+JG335q2Ewupal+/iBLiPehRYnN+ve2rx5gNPDk+7Gj4OaT3O8aRKMwnqvixjKe1prcq8RUst7Iyg7cQycPIga64NNWcw==";
    private static final String signatureKey = "a44e80af62ae3d3d7792012c4e759a9cd83daa962f51f8a09e0174ee75b3d7c0";

    public static void main(String[] args) {
        TodoDemo todoDemo = new TodoDemo();
        todoDemo.init();
        // 待办
        todoDemo.sync();
        // 待办转已办
         //todoDemo.resultSync("1945401775041282048");
        // 撤回
        // todoDemo.revoke("1903662884185964544");
    }

    private final Generator<Long> idGenerator = new SnowflakeGenerator();
    private RestTemplate restTemplate;

    String sysId = "ZLXCBPM";
    String sysName = "株辆XCBPM";
    String userStaffNo = "zzclcs1";

    public void init() {
        restTemplate = new RestTemplateBuilder()
                .rootUri("https://zctxc.crrcgc.cc:7773")
                .requestFactory(this::createClientHttpRequestFactory)
                .build();
    }

    /**
     * 待办数据同步
     */
    public void sync() {
        String account = userStaffNo;

        TodoReqBody reqBody = new TodoReqBody();
        // 唯一标识待办数据，全局唯一
        String id = idGenerator.next().toString();
        reqBody.setId(id);
        reqBody.setTitle("“省”之有道 绿色高效丨系列新能源机车之“动力电池”机车");
        reqBody.setContent("数据剖析“省”之有道");
        reqBody.setSysId(sysId);
        reqBody.setSysName(sysName);
        reqBody.setSysIcon("http://zctxc.crrcgc.cc:8888/schedule_icons/1.png");
        reqBody.setType(sysId);
        reqBody.setSource("2");
        reqBody.setCardType("1");
        reqBody.setUserNo(userStaffNo);
        reqBody.setBizType("1");
        reqBody.setReleaseDate("2024-08-04");
        reqBody.setUrgent("true");
        reqBody.setSponsor("中车AI助手");
        reqBody.setSubsidiary("中国中车集团有限公司");
        reqBody.setPcUrl("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        reqBody.setH5Url("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        // 操作类型（1:待办转已办2：待阅转已阅）
        reqBody.setActionType(1);
        reqBody.setExtend("");
        TodoReq req = new TodoReq(reqBody);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("userStaffNo", userStaffNo);
        userInfo.put("account", account);

        String token = generateToken(userInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String body = JSON.toJSONString(req);
        log.info("body:{}", body);
        String signature = calcSignature("POST", "/schedule-todo/sync", "application/json", body.getBytes(StandardCharsets.UTF_8));
        headers.set("signature", signature);

        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("/schedule-todo/sync", HttpMethod.POST, new HttpEntity<>(body.getBytes(StandardCharsets.UTF_8), headers), JSONObject.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            if (responseEntity.getBody() != null) {
                log.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody()));
                if(responseEntity.getBody().containsKey("Response")) {
                    JSONObject responseHead = responseEntity.getBody().getJSONObject("Response").getJSONObject("head");
                    if ("000000".equals(responseHead.getString("statusCode"))) {
                        log.info("待办数据同步成功");
                    } else {
                        log.error("待办数据同步失败：{}-{}", responseHead.getString("statusCode"), responseHead.getString("statusMsg"));
                    }
                }
            }
        }
    }

    /**
     * 待办数据状态同步（待办转已办）
     */
    public void resultSync(String id) {
        String account = userStaffNo;

        TodoReqBody reqBody = new TodoReqBody();
        reqBody.setId(id);
        reqBody.setSysId(sysId);
        reqBody.setSysName(sysName);
        reqBody.setUserNo(userStaffNo);
        // 如果需要修改传
        // reqBody.setPcUrl("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        // 如果需要修改传
        // reqBody.setH5Url("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        TodoReq req = new TodoReq(reqBody);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("userStaffNo", userStaffNo);
        userInfo.put("account", account);

        String token = generateToken(userInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String body = JSON.toJSONString(req);
        log.info("body:{}", body);
        String signature = calcSignature("POST", "/schedule-todo/result/sync", "application/json", body.getBytes(StandardCharsets.UTF_8));
        headers.set("signature", signature);

        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("/schedule-todo/result/sync", HttpMethod.POST, new HttpEntity<>(body.getBytes(StandardCharsets.UTF_8), headers), JSONObject.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            if (responseEntity.getBody() != null) {
                log.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody()));
                if(responseEntity.getBody().containsKey("Response")) {
                    JSONObject responseHead = responseEntity.getBody().getJSONObject("Response").getJSONObject("head");
                    if ("000000".equals(responseHead.getString("statusCode"))) {
                        log.info("待办数据状态同步成功");
                    } else {
                        log.error("待办数据状态同步失败：{}-{}", responseHead.getString("statusCode"), responseHead.getString("statusMsg"));
                    }
                }
            }
        }
    }

    /**
     * 待办数据撤回
     */
    public void revoke(String id) {
        String account = userStaffNo;

        TodoReqBody reqBody = new TodoReqBody();
        reqBody.setId(id);
        reqBody.setSysId(sysId);
        reqBody.setSysName(sysName);
        reqBody.setUserNo(userStaffNo);
        // 如果需要修改传
        // reqBody.setPcUrl("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        // 如果需要修改传
        // reqBody.setH5Url("https://www.crrcgc.cc/crrcgc/2024-08/04/article_2024080418564668574.html");
        TodoReq req = new TodoReq(reqBody);

        Map<String, String> userInfo = new HashMap<>();
        userInfo.put("userStaffNo", userStaffNo);
        userInfo.put("account", account);

        String token = generateToken(userInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token", token);

        // 为了能够成功验签，先将对象序列化为json字符串，再进行签名，请求体里面放入的json字符串要跟签名时的字符串一致
        String body = JSON.toJSONString(req);
        log.info("body:{}", body);
        String signature = calcSignature("POST", "/schedule-todo/revoke", "application/json", body.getBytes(StandardCharsets.UTF_8));
        headers.set("signature", signature);

        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("/schedule-todo/revoke", HttpMethod.POST, new HttpEntity<>(body.getBytes(StandardCharsets.UTF_8), headers), JSONObject.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            if (responseEntity.getBody() != null) {
                log.info("返回结果：{}", JSON.toJSONString(responseEntity.getBody()));
                if(responseEntity.getBody().containsKey("Response")) {
                    JSONObject responseHead = responseEntity.getBody().getJSONObject("Response").getJSONObject("head");
                    if ("000000".equals(responseHead.getString("statusCode"))) {
                        log.info("待办数据撤回成功");
                    } else {
                        log.error("待办数据撤回失败：{}-{}", responseHead.getString("statusCode"), responseHead.getString("statusMsg"));
                    }
                }
            }
        }
    }


    /**
     * 生成token
     *
     * @param userInfo
     * @return
     */
    private String generateToken(Map<String, String> userInfo) {
        return TodoTokenUtil.genTokenRSA(userInfo, privateKey, DateUtils.addDays(new Date(), 7));
    }

    /**
     * 计算签名
     *
     * @param method
     * @param uri
     * @param contentType
     * @param body
     * @return
     */
    private String calcSignature(String method, String uri, String contentType, byte[] body) {
        String bodySha = SecureUtil.sha256(new ByteArrayInputStream(body));
        HMac hMac = SecureUtil.hmacSha256(signatureKey);
        hMac.getEngine().update(method.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(uri.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(contentType.getBytes(StandardCharsets.UTF_8));
        hMac.getEngine().update(bodySha.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(hMac.getEngine().doFinal());
    }

    private ClientHttpRequestFactory createClientHttpRequestFactory() {
        // 忽略ssl证书
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

        // 业务系统也可以使用除okhttp之外的其他客户端组件（如apache http client等）
        OkHttpClient client = new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory, trustManager)
                .build();

        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory(client);
        httpRequestFactory.setReadTimeout(3000);
        httpRequestFactory.setConnectTimeout(3000);

        return httpRequestFactory;
    }

}
