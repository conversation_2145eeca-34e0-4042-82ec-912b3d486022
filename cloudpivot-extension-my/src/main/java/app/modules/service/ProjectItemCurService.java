package app.modules.service;

import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.erpdto.bom.ErpProjectItem;

import java.util.List;
import java.util.Map;

public interface ProjectItemCurService {

    public List<ErpProjectItem> read(Map<String, Object> selectInputParams);

    /**
     * @param old
     * @return
     */
    public List<ErpProjectItem> getRealErpProjectItem(List<ErpProjectItem> old);

    /**
     * @param old
     * @return
     */
    public List<ErpProjectItem> getRealErpProjectItem(ProcessProjectInfo old);
}
