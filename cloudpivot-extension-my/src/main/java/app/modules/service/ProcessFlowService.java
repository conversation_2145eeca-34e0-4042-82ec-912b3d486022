package app.modules.service;

import app.modules.model.dto.processflow.ProcessFlowSyncItem;
import app.modules.model.dto.processflow.ProcessFlowSyncRequest;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.erpdto.bom.ErpProjectItem;

import java.util.List;

public interface ProcessFlowService {
    /**
     * 从工艺技术平台获取工程行程相关数据
     *
     * @param processFlowSyncRequest
     */
    void processFlowSync(List<ProcessFlowSyncItem> processFlowSyncRequest);

    /**
     * 数据库中获取数据
     */
    List<ErpProjectItem> readCreaterProjectInfo();

    /**
     * 数据库中获取数据
     *
     * @param projectCode
     * @param productCode
     */
    List<ProcessFlow> read(String projectCode, String productCode, String userId);

    /**
     * 将 processFlowSyncRequest 转换成 bom 数据
     *
     * @param processFlowSyncRequest
     * @return
     */
    List<ProcessProjectInfo> processFlowSyncRequestToBoms(ProcessFlowSyncRequest processFlowSyncRequest);

    /**
     * 判是否全部都是新产业项目
     *
     * @param items
     * @return
     */
    boolean isNewIndustryItem(List<ErpProjectItem> items);

    boolean partInsertOrUpdate(String projectObjectId, ProcessProjectInfo processProjectInfo, List<ProcessFlow> partProcessFlows);

    boolean insertByDelete(ErpProjectItem processProjectInfo, List<ProcessFlow> partProcessFlows);

    /**
     * 根据用户id清空数据
     *
     * @return
     */
    boolean deleteAllById();


    void processFlowSyncForExcel(List<ErpProjectItem> read, List<ProcessFlow> dataList);
}
