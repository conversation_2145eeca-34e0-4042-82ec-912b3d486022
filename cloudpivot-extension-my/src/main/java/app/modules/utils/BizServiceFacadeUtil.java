package app.modules.utils;

import app.modules.constant.UserConstant;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.api.model.bizservice.BizDatabaseConnectionPoolModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceMethodModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceModel;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.service.EngineService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BizServiceFacadeUtil {

    @Autowired
    private EngineService engineService;

    public static List<Map<String, Object>> select(BizServiceFacade bizServiceFacade, String serviceCode, String selectMethodCode, Map<String, Object> selectInputParams) {
        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel selectMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, selectMethodCode);
        if (selectMethod == null) {
            throw new RuntimeException("查询业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + selectMethodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }

        // 3. 查询数据
        Map<String, Object> selectOutputMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                selectMethodCode,
                selectInputParams
        );

        // 提取 "data" 字段中的 List<Map<String, Object>>
        Map<String, Object> dataMap = MapUtils.getMap(selectOutputMap, "data");
        List<Map<String, Object>> dataList = null;
        if (dataMap != null) {
            dataList = MapUtils.getList(dataMap, "data");
        }
        return dataList;
    }

    public static Map<String, Object> executeSp(BizServiceFacade bizServiceFacade, String executeServiceCode, String executeMethodCode, Map<String, Object> spInputParams) {
        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(executeServiceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + executeServiceCode);
        }

        BizServiceMethodModel selectMethod = bizServiceFacade.getBizServiceMethodByCode(executeServiceCode, executeMethodCode);
        if (selectMethod == null) {
            throw new RuntimeException("查询业务方法不存在: serviceCode=" + executeServiceCode + ", methodCode=" + executeMethodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(executeServiceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + executeServiceCode);
        }

        // 3. 查询数据
        Map<String, Object> executeOutputMap = bizServiceFacade.testBizServiceMethod(
                executeServiceCode,
                executeMethodCode,
                spInputParams
        );

        //Map<String, Object> dataMap = MapUtils.getMap(executeOutputMap, "data");
        return executeOutputMap;
    }

    public static void batchInsert(BizServiceFacade bizServiceFacade, String serviceCode, String insertMethodCode, List<Map<String, Object>> insertInputParams) {
        for (Map<String, Object> insertInputParam : insertInputParams) {
            insert(bizServiceFacade, serviceCode, insertMethodCode, insertInputParam);
        }
    }

    public static Map<String, Object> insert(BizServiceFacade bizServiceFacade, String serviceCode, String insertMethodCode, Map<String, Object> insertInputParams) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();

        Map<String, Object> insertOutputMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                insertMethodCode,
                insertInputParams
        );

        return insertOutputMap;
    }

    public static Map<String, Object> get(BizServiceFacade bizServiceFacade, String serviceCode, String queryMethodCode, Map<String, Object> queryInputParams) {
        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel selectMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, queryMethodCode);
        if (selectMethod == null) {
            throw new RuntimeException("查询业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + queryMethodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }

        // 3. 查询数据
        Map<String, Object> queryOutputMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                queryMethodCode,
                queryInputParams
        );


        return queryOutputMap;
    }

    public void batchUpdateOrInsert(String serviceCode, String selectMethodCode, String insertMethodCode, String updateMethodCode, Map<String, Object> selectInputParams, Map<String, Object> inputParams) {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel selectMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, selectMethodCode);
        if (selectMethod == null) {
            throw new RuntimeException("查询业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + selectMethodCode);
        }

        BizServiceMethodModel updateMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, updateMethodCode);
        if (updateMethod == null) {
            throw new RuntimeException("更新业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + updateMethodCode);
        }

        BizServiceMethodModel insertMethod = bizServiceFacade.getBizServiceMethodByCode(serviceCode, insertMethodCode);
        if (insertMethodCode == null) {
            throw new RuntimeException("插入业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + insertMethodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }


        // 3. 查询数据
        Map<String, Object> insertOutputMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                selectMethodCode,
                selectInputParams
        );

        //ErpBomSelectResponse response =

        // 解析查询数据

    }

}
