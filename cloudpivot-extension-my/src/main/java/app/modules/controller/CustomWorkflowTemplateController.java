package app.modules.controller;


import com.authine.cloudpivot.engine.api.model.workflow.WorkflowTemplateHeaderModel;
import com.authine.cloudpivot.engine.enums.ErrCode;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.handler.CustomizedOrigin;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "设计时::流程::模板多流程")
@RestController
@RequestMapping("/api/workflow")
@Slf4j
@Validated
@CustomizedOrigin(level = 1)
public class CustomWorkflowTemplateController extends BaseController {
    @ApiOperation(value = "新建流程", notes = "创建流程模板头和对应的默认流程模板")
    @PostMapping("/create")
    public ResponseResult<Void> create(@RequestBody WorkflowTemplateHeaderModel model) {

        List<WorkflowTemplateHeaderModel> headerModels = getWorkflowManagementFacade().getWorkflowTemplateHeadersBySchemaCode(model.getSchemaCode(), Boolean.FALSE);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(headerModels)) {
            return getErrResponseResult(null, ErrCode.NOT_CREATE_TEMPLATE);
        }

        String userId = getUserId();
        model.setCreatedBy(userId);
        model.setPcOriginate(Boolean.TRUE);
        model.setMobileOriginate(Boolean.TRUE);
        getWorkflowManagementFacade().addWorkflowTemplate(model);

        clearWorkflowCache(userId);

        return getOkResponseResult("成功创建流程, workflowCode = ".concat(model.getWorkflowCode()));
    }

    private void clearWorkflowCache(String userId) {
        if (log.isDebugEnabled()) {
            log.debug("clearWorkflowCache. userId = {}", userId);
        }

        redisCacheService.clearUserMWorkflows(userId);
        redisCacheService.clearUserPCWorkflows(userId);
    }
}
