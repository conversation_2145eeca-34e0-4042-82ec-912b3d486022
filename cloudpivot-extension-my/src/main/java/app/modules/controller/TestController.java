package app.modules.controller;

import app.modules.constant.UserConstant;
import com.authine.cloudpivot.engine.api.facade.BizObjectFacade;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.api.model.bizservice.BizDatabaseConnectionPoolModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceMethodModel;
import com.authine.cloudpivot.engine.api.model.bizservice.BizServiceModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectModel;
import com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel;
import com.authine.cloudpivot.engine.component.query.api.Page;
import com.authine.cloudpivot.engine.domain.runtime.BizObject;
import com.authine.cloudpivot.engine.enums.type.QueryDisplayType;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.service.EngineService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "测试模块::测试")
@RestController
@RequestMapping("/api/test")
@Slf4j
public class TestController {


    @Autowired
    private EngineService engineService;

    @ApiOperation(value = "子表测试")
    @GetMapping("/childTableTest")
    public void childTableTest() {
        BizObjectFacade bizObjectFacade = engineService.getBizObjectFacade();
        // child_table read test
        BizObjectQueryModel bizQueryModel = new BizObjectQueryModel();
        bizQueryModel.setSchemaCode("TD_PROJECT_PLAN");
        bizQueryModel.setQueryCode("TD_PROJECT_PLAN");
        //bizQueryModel.setCustomQueryFields();
        //bizQueryModel.setDisplay(true);
        com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel.Options options = new com.authine.cloudpivot.engine.api.model.runtime.BizObjectQueryModel.Options();
        options.setQueryDisplayType(QueryDisplayType.APPEND);
        List<String> queryField = new ArrayList<>();
        queryField.add("ITEMS_INFO");
        options.setCustomDisplayColumns(queryField);
        bizQueryModel.setOptions(options);
        Page<BizObjectModel> bizObjectModelPage = bizObjectFacade.queryBizObjects(bizQueryModel);
        List<? extends BizObjectModel> content = bizObjectModelPage.getContent();
        BizObjectModel bizObjectModel = content.get(0);
        Map<String, Object> data = bizObjectModel.getData();

        BizObject oldBizObject = new BizObject();


        BizObject bizObject = new BizObject("TD_PROJECT_PLAN", null, bizObjectModel.getData());
        BeanUtils.copyProperties(bizObject, oldBizObject);

        bizObject.setOldBizObject(oldBizObject);
        Object itemsInfo = bizObject.getData().get("ITEMS_INFO");
        List<Map<String, Object>> itemInfos = (List<Map<String, Object>>) itemsInfo;
        itemInfos.get(0).replace("MY_INDEX", "1111.1111");
        // child_table update test
        bizObjectFacade.updateBizObject(bizObject);
        System.out.println("aaa");
    }

    @ApiOperation(value = "bizServiceFacadeTest")
    @GetMapping("/bizServiceFacadeTest")
    public void bizServiceFacadeTest() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();

        String serviceCode = "testests";
        String methodCode = "AAA_ITEM_CODE_INSERT_1";
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("ITEM_CODE", "1111111");
        inputParams.put("ITEM_NAME", "test");
        // 1. 验证服务和方法存在
        BizServiceModel service = bizServiceFacade.getBizServiceByCode(serviceCode);
        if (service == null) {
            throw new RuntimeException("业务服务不存在: serviceCode=" + serviceCode);
        }

        BizServiceMethodModel method = bizServiceFacade.getBizServiceMethodByCode(serviceCode, methodCode);
        if (method == null) {
            throw new RuntimeException("业务方法不存在: serviceCode=" + serviceCode + ", methodCode=" + methodCode);
        }

        // 2. 验证数据源配置
        BizDatabaseConnectionPoolModel dbPool = bizServiceFacade.getBizDbConnPoolByBizServiceCode(serviceCode);
        if (dbPool == null) {
            throw new RuntimeException("未找到业务服务对应的数据源配置: serviceCode=" + serviceCode);
        }


        // 3. 执行方法并返回结果
        Map<String, Object> stringObjectMap = bizServiceFacade.testBizServiceMethod(
                serviceCode,
                methodCode,
                inputParams
        );

        ObjectMapper mapper = new ObjectMapper();
        try {
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(stringObjectMap);
            System.out.println(json);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


}
