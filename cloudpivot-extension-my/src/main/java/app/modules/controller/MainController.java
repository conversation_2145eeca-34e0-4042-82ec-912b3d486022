package app.modules.controller;

import app.modules.model.erpdto.bom.ErpProjectItem;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Api(tags = "测试模块::MAIN")
@RestController
@RequestMapping("/api/main")
@Controller
@Slf4j
public class MainController extends BaseController {
    @ApiOperation(value = "获取userId")
    @GetMapping("/userId")
    public ResponseResult<List<String>> erpProjectList() {
        List<String> read = new ArrayList<>();

        log.info("======================");
        log.info("userId {}", getUserId());
        read.add(getUserId());
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        String userId = openEngine.getUserId();
        log.info("openEngine userId {}", userId);
        log.info("======================");
        read.add(userId);
        return getOkResponseResult(read);
    }
}
