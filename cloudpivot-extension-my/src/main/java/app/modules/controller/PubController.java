package app.modules.controller;

import app.modules.model.dto.user.UserByTeleRequest;
import app.modules.model.dto.user.UserByTeleResponse;
import com.authine.cloudpivot.engine.api.facade.DepartmentFacade;
import com.authine.cloudpivot.engine.api.facade.UserFacade;
import com.authine.cloudpivot.engine.api.model.organization.DepartmentModel;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.engine.enums.ErrCode;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "公共模块::公共")
@RestController
@RequestMapping("/public")
@Slf4j
public class PubController extends BaseController {

    @Autowired
    private EngineService engineService;

    @ApiOperation(value = "获取项目状态")
    @PostMapping("/user/getUserByTele")
    public ResponseResult<UserByTeleResponse> getUserByTele(@RequestBody UserByTeleRequest request) {
        if (ObjectUtils.isEmpty(request) || StringUtils.isAnyBlank(request.getTelephone())) {
            throw new PortalException(ErrCode.REQUEST_NULL);
        }

        UserFacade userFacade = engineService.getUserFacade();
        DepartmentFacade departmentFacade = engineService.getDepartmentFacade();

        UserModel firstByMobile = userFacade.getFirstByMobile(request.getTelephone());
        if (firstByMobile == null) {
            throw new PortalException(ErrCode.REQUEST_NULL, "该电话无对应用户");
        }
        DepartmentModel departmentModel = departmentFacade.get(firstByMobile.getDepartmentId());
        UserByTeleResponse userByTeleResponse = new UserByTeleResponse();
        BeanUtils.copyProperties(firstByMobile, userByTeleResponse);
        userByTeleResponse.setCurrentDeptName(departmentModel.getName());
        return getOkResponseResult(userByTeleResponse);
    }

}
