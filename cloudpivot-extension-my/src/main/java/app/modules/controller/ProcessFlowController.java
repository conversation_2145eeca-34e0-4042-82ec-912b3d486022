package app.modules.controller;

import app.modules.listener.BaseExcelListener;
import app.modules.model.dto.processflow.ProcessFlowSyncItem;
import app.modules.model.dto.processflow.ProcessFlowSyncRequest;
import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.entity.ProcessFlow;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.BomService;
import app.modules.service.ProcessFlowService;
import app.modules.service.impl.ProjectItemCurServiceImpl;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "工艺行程模块::工艺行程")
@RestController
@RequestMapping("/api/processFlow")
@Slf4j
public class ProcessFlowController extends BaseController {

    @Resource
    private ProcessFlowService processFlowService;

    @Resource
    private BomService bomServiceImpl;

    @Resource
    private ProjectItemCurServiceImpl projectItemCurServiceImpl;

    @ApiOperation(value = "工艺行程数据同步")
    @PostMapping("/sync")
    public ResponseResult<List<Map<String, Object>>> processFlowSync(@RequestBody ProcessFlowSyncRequest processFlowSyncRequest) {
        if (ObjectUtil.isNull(processFlowSyncRequest)) {
            throw new PortalException(ResultEnum.ILLEGAL_PARAMETER_ERR.getErrCode(), "请求参数错误");
        }

        ProcessFlowSyncItem[] items = processFlowSyncRequest.getItems();
        List<ProcessFlowSyncItem> uniqueItems = Arrays.stream(items).distinct().collect(Collectors.toList());

        processFlowService.processFlowSync(uniqueItems);

        return getOkResponseResult(null, "同步成功");
    }

    @ApiOperation(value = "生成bom表数据")
    @PostMapping("/generateBom")
    public ResponseResult<List<Map<String, Object>>> generateBom(@RequestBody ProcessFlowSyncRequest processFlowSyncRequest) {
        if (ObjectUtil.isNull(processFlowSyncRequest)) {
            throw new PortalException(ResultEnum.ILLEGAL_PARAMETER_ERR.getErrCode(), "请求参数错误");
        }

        ProcessFlowSyncItem[] items = processFlowSyncRequest.getItems();

        boolean hasAnyBlank = Arrays.stream(items).anyMatch(o -> {
            return StringUtils.isAnyBlank(o.getProductCode(), o.getProductCode());
        });

        boolean hasNoNewIndustryItem = false;
        if (items.length > 1) {
            hasNoNewIndustryItem = Arrays.stream(items).anyMatch(o -> {
                return !"Y".equals(o.getNewIndustryFlag());
            });
        }

        if (hasAnyBlank || hasNoNewIndustryItem) {
            throw new PortalException(ResultEnum.ILLEGAL_PARAMETER_ERR.getErrCode(), "请求参数错误");
        }

        List<ProcessProjectInfo> projectInfos = processFlowService.processFlowSyncRequestToBoms(processFlowSyncRequest);

        StringBuilder sb = new StringBuilder();
        for (ProcessProjectInfo projectInfo : projectInfos) {
            String format = String.format("项目编码：%s, 产品编码: %s\n", projectInfo.getProjectCode(), projectInfo.getProductCode());
            sb.append(format);
        }
        if (!projectInfos.isEmpty()) {
            sb.append("BOM生成成功");
        }

        return getOkResponseResult(null, sb.toString());
    }

    @ApiOperation(value = "生成bom表数据")
    @PostMapping("/generateBomByUser")
    public ResponseResult<List<Map<String, Object>>> generateBomByUser() {
        List<ErpProjectItem> erpProjectItems = processFlowService.readCreaterProjectInfo();
        ProcessFlowSyncItem[] items = new ProcessFlowSyncItem[erpProjectItems.size()];
        int idx = 0;
        for (ErpProjectItem erpProjectItem : erpProjectItems) {
            ProcessFlowSyncItem temp = new ProcessFlowSyncItem();
            temp.setProjectCode(erpProjectItem.getProjectCode());
            temp.setProjectName(erpProjectItem.getProjectName());
            temp.setProductCode(erpProjectItem.getProductCode());
            temp.setProductName(erpProjectItem.getProductName());
            temp.setProjWorkCode(erpProjectItem.getProjWorkCode());
            temp.setProjEpmWorkCode(erpProjectItem.getProjEpmWorkCode());
            temp.setNewIndustryFlag(erpProjectItem.getNewIndustryFlag());
            temp.setProjYear(erpProjectItem.getProjYear());
            items[idx++] = temp;
        }

        ProcessFlowSyncRequest processFlowSyncRequest = new ProcessFlowSyncRequest();
        processFlowSyncRequest.setItems(items);
        /*boolean hasAnyBlank = Arrays.stream(items).anyMatch(o -> {
            return StringUtils.isAnyBlank(o.getProductCode(), o.getProductCode());
        });

        boolean hasNoNewIndustryItem = false;
        if (items.length > 1) {
            hasNoNewIndustryItem = Arrays.stream(items).anyMatch(o -> {
                return !"Y".equals(o.getNewIndustryFlag());
            });
        }

        if (hasAnyBlank || hasNoNewIndustryItem) {
            throw new PortalException(ResultEnum.ILLEGAL_PARAMETER_ERR.getErrCode(), "请求参数错误");
        }*/

        List<ProcessProjectInfo> projectInfos = processFlowService.processFlowSyncRequestToBoms(processFlowSyncRequest);

        StringBuilder sb = new StringBuilder();
        for (ProcessProjectInfo projectInfo : projectInfos) {
            String format = String.format("项目编码：%s, 产品编码: %s\n", projectInfo.getProjectCode(), projectInfo.getProductCode());
            sb.append(format);
        }
        if (!projectInfos.isEmpty()) {
            sb.append("BOM生成成功");
        }

        return getOkResponseResult(null, sb.toString());
    }


    @ApiOperation(value = "工艺形成文件导入")
    @PostMapping("/excelImport")
    public ResponseResult<Map<String, Object>> handleFileUpload(@RequestPart("processProjectInfo") ProcessProjectInfo processProjectInfo, @RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "未选择文件");
        }
        try {
            BaseExcelListener<ProcessFlow> baseExcelListener = new BaseExcelListener<>();
            EasyExcel.read(file.getInputStream(), ProcessFlow.class, baseExcelListener).autoTrim(true).sheet().doRead();
            List<ProcessFlow> dataList = baseExcelListener.getDataList();

            List<ErpProjectItem> read = projectItemCurServiceImpl.getRealErpProjectItem(processProjectInfo);
            if (read.isEmpty()) {
                throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "请求参数错误，项目不存在");
            }
            log.info("starting insert processFlow");
            // 进行数据 insertOrUpdate()
            processFlowService.processFlowSyncForExcel(read, dataList);
            //processFlowService.insertByDelete(processProjectInfo.toErpProjectItem(), dataList);
            log.info("ending insert processFlow");
            return getOkResponseResult(null, "导入成功");

        } catch (Exception e) {
            log.error(Arrays.toString(e.getStackTrace()));
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "清空数据")
    @PostMapping("/deleteAll")
    public ResponseResult<Boolean> deleteAllById() {
        processFlowService.deleteAllById();
        return getOkResponseResult(null, "清空成功");
    }

    @ApiOperation(value = "数据传输到erp")
    @PostMapping("/epmBomSendErp")
    public ResponseResult<Boolean> epmBomSendErp(@RequestBody ProcessFlowSyncRequest processFlowSyncRequest) {
        bomServiceImpl.epmBomSendErpByProject(processFlowSyncRequest);
        return getOkResponseResult(null, "数据成功传输到erp");
    }


    @ApiOperation(value = "按照用户将数据传输到erp")
    @PostMapping("/epmBomSendErpByUser")
    public ResponseResult<Boolean> epmBomSendErpByUser() {
        List<ErpProjectItem> erpProjectItems = processFlowService.readCreaterProjectInfo();
        ProcessFlowSyncItem[] items = new ProcessFlowSyncItem[erpProjectItems.size()];
        int idx = 0;
        for (ErpProjectItem erpProjectItem : erpProjectItems) {
            ProcessFlowSyncItem temp = new ProcessFlowSyncItem();
            temp.setProjectCode(erpProjectItem.getProjectCode());
            temp.setProjectName(erpProjectItem.getProjectName());
            temp.setProductCode(erpProjectItem.getProductCode());
            temp.setProductName(erpProjectItem.getProductName());
            temp.setProjWorkCode(erpProjectItem.getProjWorkCode());
            temp.setProjEpmWorkCode(erpProjectItem.getProjEpmWorkCode());
            temp.setNewIndustryFlag(erpProjectItem.getNewIndustryFlag());
            temp.setProjYear(erpProjectItem.getProjYear());
            items[idx++] = temp;
        }

        ProcessFlowSyncRequest processFlowSyncRequest = new ProcessFlowSyncRequest();
        processFlowSyncRequest.setItems(items);

        bomServiceImpl.epmBomSendErpByProject(processFlowSyncRequest);
        return getOkResponseResult(null, "数据成功传输到erp");
    }
}
