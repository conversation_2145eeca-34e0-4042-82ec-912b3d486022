package app.modules.controller;

import app.modules.model.entity.ProcessProjectInfo;
import app.modules.model.erpdto.bom.ErpProjectItem;
import app.modules.service.ProcessProjectInfoService;
import app.modules.service.ProjectItemCurService;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 获取当前项目的状态
 */
@Api(tags = "工艺行程模块::项目")
@RestController
@RequestMapping("/api/project")
@Slf4j
public class ProjectController extends BaseController {
    @Resource
    private ProcessProjectInfoService processProjectInfoServiceImpl;

    @Resource
    private ProjectItemCurService projectItemCurServiceImpl;

    @ApiOperation(value = "获取项目状态")
    @GetMapping("/getProjectStatus")
    public ResponseResult<String> getProjectStatus(@RequestParam String projectCode, @RequestParam String productCode) {
        if (StringUtils.isAnyBlank(projectCode, productCode)) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "请求参数为空");
        }
        List<ProcessProjectInfo> read = processProjectInfoServiceImpl.readByCurrentUser(projectCode, productCode);
        ProcessProjectInfo processProjectInfo = read.get(0);
        String status = "";
        if (processProjectInfo != null) {
            status = processProjectInfo.getProjectStatus();
        }
        return getOkResponseResult(status, "清空成功");
    }

    @ApiOperation(value = "获取ERP中的项目")
    @GetMapping("/erpProjectList")
    public ResponseResult<List<ErpProjectItem>> erpProjectList() {
        List<ErpProjectItem> read = projectItemCurServiceImpl.read(new HashMap<>());
        return getOkResponseResult(read, "清空成功");
    }


}
