package app.modules.controller;

import app.modules.service.BomService;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "工艺行程模块::BOM")
@RestController
@RequestMapping("/api/bom")
@Slf4j
public class BomController extends BaseController {

    @Resource
    private BomService bomServiceImpl;

    @ApiOperation(value = "清空数据")
    @PostMapping("/deleteAll")
    public ResponseResult<Boolean> deleteAllById() {
        bomServiceImpl.deleteAllById();
        return getOkResponseResult(null, "清空成功");
    }
}
