package app.modules.controller;

import app.modules.constant.ServiceConstant;
import app.modules.constant.UserConstant;
import app.modules.excel.CheckExcelWriter;
import app.modules.model.dto.check.BomData;
import app.modules.model.dto.check.MaterialData;
import app.modules.service.BomCheckService;
import app.modules.service.ProcessProjectInfoService;
import app.modules.utils.BizServiceFacadeUtil;
import app.modules.utils.MapUtils;
import com.authine.cloudpivot.engine.api.facade.BizServiceFacade;
import com.authine.cloudpivot.engine.open.OpenEngineFactory;
import com.authine.cloudpivot.engine.open.domain.organization.User;
import com.authine.cloudpivot.engine.open.service.OpenEngine;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.exception.PortalException;
import com.authine.cloudpivot.web.api.exception.ResultEnum;
import com.authine.cloudpivot.web.api.service.EngineService;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import com.google.common.base.CaseFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "EPM物料检查模块::ERP_CHECK")
@RestController
@RequestMapping("/api/erpCheck")
@Controller
public class BomCheckController extends BaseController {

    @Autowired
    private EngineService engineService;

    @Resource
    private ProcessProjectInfoService processProjectInfoServiceImpl;

    @Resource
    private BomCheckService bomCheckServiceImpl;

    @ApiOperation(value = "物料数据计算、定义规范检查")
    @PostMapping("/checkItemAttr")
    public ResponseResult<Boolean> checkItemAttributes() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();

        // 获取当前的用户，和当前用户对应的项目
        String companyId = "01";
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        String defaultFlag = "N";

        User userById = openEngine.getUserById(userId);
        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);

        boolean returnFlag = bomCheckServiceImpl.checkItemAttributes(companyId, userCode, defaultFlag);
        return getOkResponseResult(returnFlag);
    }

    @ApiOperation(value = "BOM结构、工艺行程规范检查")
    @PostMapping("/checkBom")
    public ResponseResult<Boolean> checkBom() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();

        // 获取当前的用户，和当前用户对应的项目
        String companyId = "01";
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        String defaultFlag = "N";

        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);

        boolean returnFlag = bomCheckServiceImpl.checkBom(companyId, userCode, defaultFlag);
        return getOkResponseResult(returnFlag);
    }

    @ApiOperation(value = "物料推送到ERP的CIM接口")
    @PostMapping("/epmItemPush")
    public ResponseResult<Boolean> epmItemPush() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();

        // 获取当前的用户，和当前用户对应的项目
        String companyId = "01";
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        String defaultFlag = "N";

        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);

        boolean returnFlag = bomCheckServiceImpl.epmItemPush(companyId, userCode, defaultFlag);
        return getOkResponseResult(returnFlag);
    }

    @ApiOperation(value = "BOM推送到ERP的CIM接口")
    @PostMapping("/epmBomPush")
    public ResponseResult<Boolean> epmBomPush() {
        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();

        // 获取当前的用户，和当前用户对应的项目
        String companyId = "01";
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        String defaultFlag = "N";

        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);

        boolean returnFlag = bomCheckServiceImpl.epmBomPush(companyId, userCode, defaultFlag);
        return getOkResponseResult(returnFlag);
    }

    @ApiOperation(value = "物料数据检查结果导出")
    @PostMapping("/itemResultDownload")
    public void itemResultDownload(HttpServletResponse response) throws IOException, InvocationTargetException, IllegalAccessException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("物料数据查询", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("PERSON_CODE", userCode);
        Map<String, Object> stringObjectMap = BizServiceFacadeUtil.get(bizServiceFacade, ServiceConstant.EDM_STAFF, ServiceConstant.USER_CODE_TO_UNIQUE_ID, queryParams);
        boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
        Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
        String msg = (String) stringObjectMap.getOrDefault("msg", false);
        if (!success) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
        }
        String userUniqueId = "";
        if (data != null) {
            userUniqueId = (String) data.get("USER_UNIQUE_ID");
        }
        HashMap<String, Object> checkResultQueryParams = new HashMap<>();
        checkResultQueryParams.put("USER_UNIQUE_ID", userUniqueId);
        stringObjectMap = BizServiceFacadeUtil.get(bizServiceFacade, ServiceConstant.CIM_EPM_ITEM_CHECK_RESULT, ServiceConstant.CIM_EPM_ITEM_CHECK_RESULT_SELECT_1, checkResultQueryParams);
        success = (boolean) stringObjectMap.getOrDefault("success", false);
        data = MapUtils.getMap(stringObjectMap, "data");
        msg = (String) stringObjectMap.getOrDefault("msg", false);
        if (!success) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
        }
        List<Map<String, Object>> dataList = null;
        if (data != null) {
            dataList = MapUtils.getList(data, "data");
        }
        List<MaterialData> materialDataList = new ArrayList<>();
        for (Map<String, Object> objectMap : dataList) {
            Map<String, Object> tempMap = new HashMap<>();
            for (String s : objectMap.keySet()) {
                String s1 = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, s);
                tempMap.put(s1, objectMap.get(s));
            }
            MaterialData materialData = new MaterialData();
            BeanUtils.populate(materialData, tempMap);
            materialDataList.add(materialData);
        }
        CheckExcelWriter.itemWrite(response, materialDataList);
    }

    @ApiOperation(value = "产品结构检查结果导出")
    @PostMapping("/itemResultDownload")
    public void bomResultDownload(HttpServletResponse response) throws IOException, InvocationTargetException, IllegalAccessException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("物料数据查询", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

        OpenEngine openEngine = OpenEngineFactory.getOpenEngine();
        BizServiceFacade bizServiceFacade = engineService.getBizServiceFacade();
        String userId = openEngine.getUserId() == null ? UserConstant.ADMIN_ID : openEngine.getUserId();
        User userById = openEngine.getUserById(userId);
        if (userById == null) {
            throw new PortalException(ResultEnum.REQUEST_NULL.getErrCode(), "用户不存在");
        }
        String userCode = userById.getUserId().substring(userById.getUserId().length() - 6);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("PERSON_CODE", userCode);
        Map<String, Object> stringObjectMap = BizServiceFacadeUtil.get(bizServiceFacade, ServiceConstant.EDM_STAFF, ServiceConstant.USER_CODE_TO_UNIQUE_ID, queryParams);
        boolean success = (boolean) stringObjectMap.getOrDefault("success", false);
        Map<String, Object> data = MapUtils.getMap(stringObjectMap, "data");
        String msg = (String) stringObjectMap.getOrDefault("msg", false);
        if (!success) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
        }
        String userUniqueId = "";
        if (data != null) {
            userUniqueId = (String) data.get("USER_UNIQUE_ID");
        }
        HashMap<String, Object> checkResultQueryParams = new HashMap<>();
        checkResultQueryParams.put("USER_UNIQUE_ID", userUniqueId);
        stringObjectMap = BizServiceFacadeUtil.get(bizServiceFacade, ServiceConstant.CIM_EPM_BOM_CHECK_RESULT, ServiceConstant.CIM_EPM_BOM_CHECK_RESULT_SELECT_1, checkResultQueryParams);
        success = (boolean) stringObjectMap.getOrDefault("success", false);
        data = MapUtils.getMap(stringObjectMap, "data");
        msg = (String) stringObjectMap.getOrDefault("msg", false);
        if (!success) {
            throw new PortalException(ResultEnum.SERVER_ERROR.getErrCode(), String.format("存储过程执行失败: %s", msg));
        }
        List<Map<String, Object>> dataList = null;
        if (data != null) {
            dataList = MapUtils.getList(data, "data");
        }
        List<BomData> BOMDataList = new ArrayList<>();
        for (Map<String, Object> objectMap : dataList) {
            Map<String, Object> tempMap = new HashMap<>();
            for (String s : objectMap.keySet()) {
                String s1 = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, s);
                tempMap.put(s1, objectMap.get(s));
            }
            BomData materialData = new BomData();
            BeanUtils.populate(materialData, tempMap);
            BOMDataList.add(materialData);
        }
        CheckExcelWriter.bomWrite(response, BOMDataList);
    }

}
