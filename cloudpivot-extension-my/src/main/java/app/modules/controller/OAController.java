package app.modules.controller;

import app.modules.configuration.OAConfig;
import app.modules.model.vo.OAUnFinishedVO;
import com.authine.cloudpivot.engine.api.model.PageableSupport;
import com.authine.cloudpivot.engine.api.model.organization.UserModel;
import com.authine.cloudpivot.engine.api.model.runtime.CirculateItemModel;
import com.authine.cloudpivot.engine.api.model.runtime.WorkItemModel;
import com.authine.cloudpivot.engine.api.spec.WorkItemQuerySpec;
import com.authine.cloudpivot.engine.domain.organization.RelatedCorpSetting;
import com.authine.cloudpivot.engine.enums.type.UnitType;
import com.authine.cloudpivot.engine.service.system.RelatedCorpSettingService;
import com.authine.cloudpivot.foundation.util.api.GenerateUtils;
import com.authine.cloudpivot.web.api.controller.base.BaseController;
import com.authine.cloudpivot.web.api.view.ResponseResult;
import com.sun.xml.bind.v2.TODO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.codec.digest.DigestUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "OA门户")
@RestController
@RequestMapping("/public")
@Slf4j
public class OAController extends BaseController {
    @Autowired
    private OAConfig oaConfig;


    //TODO typeID只有待办,先获取待办和代阅
    @ApiOperation(value = "获取待办")
    @PostMapping("/getTodoList")
    public OAUnFinishedVO getTodoList(@RequestParam String appID, @RequestParam String checkID,
                                      @RequestParam String userID, @RequestParam String typeID, @RequestParam String count,
                                      @RequestParam String timeStamp) {

        OAUnFinishedVO oaUnFinishedVO = new OAUnFinishedVO();
        String secretKey = oaConfig.getSecretKey();
        String checkInput = oaConfig.getAppID() + secretKey + timeStamp;
        String check = DigestUtils.md5Hex(checkInput);
        if (!check.equals(checkID)) {
            oaUnFinishedVO.setCode("002");
            oaUnFinishedVO.setMessage("失败");
            OAUnFinishedVO.WorkItemOA workItemOA = new OAUnFinishedVO.WorkItemOA();
            workItemOA.setInfo("checkID校验失败！");
            oaUnFinishedVO.setData(workItemOA);
            return oaUnFinishedVO;
        }
        oaUnFinishedVO.setCode("001");
        oaUnFinishedVO.setMessage("成功");
        oaUnFinishedVO.setAppID(appID);
        oaUnFinishedVO.setTimestamp(String.valueOf(System.currentTimeMillis()));

        UserModel userByUsername = getOrganizationFacade().getUserByUsername(userID);
        Pageable pageable = PageRequest.of(0, Integer.parseInt(count), Sort.by(Sort.Direction.DESC, new String[]{"startTime"}));
        WorkItemQuerySpec workItemQuerySpec = WorkItemQuerySpec.builder()
                .userId(userByUsername.getId())
                .build();
        workItemQuerySpec.setPageable(pageable);
        OAUnFinishedVO.WorkItemOA workItemOA = new OAUnFinishedVO.WorkItemOA();
        OAUnFinishedVO.PageInfo pageInfo = new OAUnFinishedVO.PageInfo();
        List<OAUnFinishedVO.Detail> details = new ArrayList<>();
        //公司编码
        String cmpyCode = "01";
        String cmpyName = "中车株辆";
        // 定义日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        workItemOA.setPageInfo(pageInfo);
        if ("1".equals(typeID)) {
            Page<WorkItemModel> workItemPage = this.getWorkflowInstanceFacade().queryWorkItems(workItemQuerySpec);
            oaUnFinishedVO.setCount(String.valueOf(workItemPage.getTotalElements()));
            if (CollectionUtils.isNotEmpty(workItemPage.getContent())) {
                workItemPage.getContent().forEach(workitem -> {
                    OAUnFinishedVO.Detail detail = new OAUnFinishedVO.Detail();
                    String participant = workitem.getParticipant();
                    String originator = workitem.getOriginator();
                    UserModel user = getOrganizationFacade().getUser(participant);
                    UserModel originatorUser = getOrganizationFacade().getUser(originator);
                    detail.setCmpyCode(cmpyCode);
                    detail.setDoneCmpyName(cmpyName);
                    detail.setDoneDeptCode(user.getDepartmentId());
                    detail.setDoneDeptName(user.getDepartmentName());
                    detail.setDoneUserCode(user.getUsername());
                    detail.setDoneUserName(user.getName());
                    detail.setSendCmpyName(cmpyName);
                    detail.setSendDeptCode(originatorUser.getDepartmentId());
                    detail.setSendDeptName(originatorUser.getDepartmentName());
                    detail.setSendUserCode(user.getUsername());
                    detail.setSendUserName(user.getName());
                    String pcUrl = String.format("%s/form/detail?workitemId=%s&workflowInstanceId=%s&workitemType=unfinishedWorkitem&userName=%s",
                            oaConfig.getYunshuURL(), workitem.getId(), workitem.getInstanceId(), user.getUsername());
                    String mobileUrl = String.format("%s/mobile#/form/detail?workitemId=%s&workflowInstanceId=%s&workitemType=unfinishedWorkitem&userName=%s",
                            oaConfig.getYunshuURL(), workitem.getId(), workitem.getInstanceId(), user.getUsername());
                    detail.setTodoAppUrl(mobileUrl);
                    detail.setTodoUrl(pcUrl);
                    detail.setTodoCatalog("1");
                    detail.setTodoId(workitem.getId());
                    detail.setTodoNode(workitem.getActivityName());
                    detail.setTodoSendTime(sdf.format(workitem.getStartTime()));
                    detail.setTodoTitle(workitem.getInstanceName());
                    detail.setOperType("0");
                    details.add(detail);
                });
            }
        }
        if ("2".equals(typeID)) {
            PageableSupport pageableSupport = new PageableSupport(pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());
            Page<CirculateItemModel> circulatePage =
                    this.getWorkflowInstanceFacade().queryCirculateItems(workItemQuerySpec, pageableSupport);
            oaUnFinishedVO.setCount(String.valueOf(circulatePage.getTotalElements()));
            circulatePage.getContent().forEach(workitem -> {
                OAUnFinishedVO.Detail detail = new OAUnFinishedVO.Detail();
                String participant = workitem.getParticipant();
                String originator = workitem.getOriginator();
                UserModel user = getOrganizationFacade().getUser(participant);
                UserModel originatorUser = getOrganizationFacade().getUser(originator);
                detail.setCmpyCode(cmpyCode);
                detail.setDoneCmpyName(cmpyName);
                detail.setDoneDeptCode(user.getDepartmentId());
                detail.setDoneDeptName(user.getDepartmentName());
                detail.setDoneUserCode(user.getUsername());
                detail.setDoneUserName(user.getName());
                detail.setSendCmpyName(cmpyName);
                detail.setSendDeptCode(originatorUser.getDepartmentId());
                detail.setSendDeptName(originatorUser.getDepartmentName());
                detail.setSendUserCode(user.getUsername());
                detail.setSendUserName(user.getName());
                String pcUrl = String.format("%s/form/detail?workitemId=%s&workflowInstanceId=%s&workitemType=unreadWorkitem&userName=%s",
                        oaConfig.getYunshuURL(), workitem.getId(), workitem.getInstanceId(), user.getUsername());
                String mobileUrl = String.format("%s/mobile#/form/detail?workitemId=%s&workflowInstanceId=%s&workitemType=unreadWorkitem&userName=%s",
                        oaConfig.getYunshuURL(), workitem.getId(), workitem.getInstanceId(), user.getUsername());
                detail.setTodoAppUrl(mobileUrl);
                detail.setTodoUrl(pcUrl);
                detail.setTodoCatalog("2");
                detail.setTodoId(workitem.getId());
                detail.setTodoNode(workitem.getActivityName());
                detail.setTodoSendTime(sdf.format(workitem.getStartTime()));
                detail.setTodoTitle(workitem.getInstanceName());
                detail.setOperType("9");
                details.add(detail);
            });
        }
        workItemOA.setList(details);
        oaUnFinishedVO.setData(workItemOA);
        return oaUnFinishedVO;

    }

}
